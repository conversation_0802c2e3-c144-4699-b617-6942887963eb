(ns ch.deepimpact.white.custom.mobileid.infra.mobile-id-server
  (:require [clj-http.client :as http]
            [mount.core :refer [defstate]]
            [smbh.log.core :as log]
            [cheshire.core :as json]
            [clojure.string :as str]
            [clj-http.core :as http-core]
            [clj-http.client :as client]
            [ch.deepimpact.white.custom.mobileid.infra.mobile-id :as mobileid]
            [ch.deepimpact.black.infra.config :as config]
            [ch.deepimpact.black.security.jwt :as jwt]
            [ch.deepimpact.black.support.ex :as ex]
            [ch.deepimpact.black.infra.healthcheck :refer [defhealthcheck] :as healthcheck]
            [ch.deepimpact.black.support.date-time :as date-time]
            [clojure.string :as string])
  (:import (clojure.lang ExceptionInfo)
           (java.util UUID)
           (java.security KeyStore Security)
           (java.io FileInputStream)
           (org.bouncycastle.cert.jcajce JcaX509CertificateConverter JcaCertStoreBuilder)
           (org.apache.commons.codec.binary Base64)
           (org.bouncycastle.cms CMSSignedData)
           (java.security.cert CertPathBuilder X509CertSelector PKIXBuilderParameters)
           (org.bouncycastle.jce.provider BouncyCastleProvider)
           (org.bouncycastle.cms.jcajce JcaSimpleSignerInfoVerifierBuilder)))

(log/deflogger)

(defn create-proxy-jwt []
  (let [valid-for (or (get-in config/config [:mobile-id :proxy-jwt-valid-in-mins]) 10)]
    (jwt/sign (jwt/claims {:sub "spectra" :aud "mobile-id-proxy"} nil valid-for))))

(defn create-encrypted-sms-token-jwt [phone-number token]
  (let [valid-for (or (get-in config/config [:mobile-id :otp-sms :valid-in-mins]) 5)
        claims    (jwt/claims {:sub phone-number :aud "spectra" :sms-token token} nil valid-for)]
    (jwt/encrypt claims)))

(defn http-opts [config]
  {:socket-timeout (or (:socket-timeout config) 60000)
   :conn-timeout   (or (:conn-timeout config) 6000)
   :content-type   :json})

(defn headers []
  {:headers {"auth-jwt" (create-proxy-jwt)}})

(defn handle-error
  [msg data]
  (throw (ex/internal-ex :phone-activation/unsuccessful-mid-proxy-call (str "Mobile ID proxy call was unsuccessful. " msg) (or data {}))))

(defn generate-request-id []
  (str "R" (Math/abs (.hashCode (UUID/randomUUID)))))

(defn call-mid-proxy
  [endpoint req-body]
  (try
    (let [mid-config (:mobile-id config/config)
          url        (str (:base-url mid-config) endpoint)
          req        (merge (http-opts mid-config)
                            (headers)
                            {:body (json/generate-string req-body)})
          result     (http/post url req)
          status     (:status result)
          body       (:body result)]
      (if (#{200 201} status)
        (json/parse-string body true)
        (handle-error (str "Url:" url ", http result code:" status) req-body)))
    (catch Exception e
      (println e)
      (handle-error (str "Endpoint: " endpoint ", exception: " (.getMessage e)) req-body))))

(defn signature-request-body
  "Creates the body of the signature request.
  This request can be used for sms sending and mobile id signature testing too."
  [signature-type {:keys [language phone-number ap-id ap-pwd message]}]
  (let [instant  (date-time/instant->iso-offset-date-time (date-time/now!))
        msg-mode (if (= signature-type :sms) "asynch" "synch")
        profile  (cond
                   (= signature-type :sms) "http://mid.swisscom.ch/MID/v1/OtpProfileText"
                   (= signature-type :mid-app) "http://mid.swisscom.ch/Device-LoA4"
                   (= signature-type :mid-sim) "http://mid.swisscom.ch/STK-LoA4"
                   ;;TODO this is for backward compatibility only, it should be removed when the new API is deployed on Swisscom side
                   (= signature-type :mid) "http://mid.swisscom.ch/MID/v1/AuthProfile1"
                   :else "http://mid.swisscom.ch/Any-LoA4")]
    {:MSS_SignatureReq
     {:MajorVersion       "1"
      :MinorVersion       "1"
      :MessagingMode      msg-mode
      :SignatureProfile   profile
      :TimeOut            "80"
      :AdditionalServices [{:Description "http://mss.ficom.fi/TS102204/v1.0.0#userLang"
                            :UserLang    {:Value (str/upper-case language)}}]
      :MobileUser         {:MSISDN phone-number}
      :MSSP_Info          {:MSSP_ID {:URI "http://mid.swisscom.ch/"}}
      :AP_Info            {:AP_ID      ap-id
                           :AP_PWD     ap-pwd
                           :Instant    instant
                           :AP_TransID (generate-request-id)}
      :DataToBeSigned     {:MimeType "text/plain"
                           :Encoding "UTF-8"
                           :Data     message}}}))

(defn send-signature-request
  "Sends the signature request to the Swisscom APi and returnes the parsed body."
  [signature-type {:keys [uri keystore-file keystore-pwd] :as opts}]
  (def signature-type signature-type)
  (def opts opts)
  (let [keystore-path (str config/config-path "/" keystore-file)
        req-body      (signature-request-body signature-type opts)
        res           (http-core/request
                        {:request-method :post
                         :scheme         :https
                         :uri            uri
                         :keystore       keystore-path
                         :keystore-pass  keystore-pwd
                         :insecure?      true
                         :content-type   :json
                         :accept         :json
                         :body           (json/generate-string req-body)})
        res-body      (json/parse-string (slurp (:body res)))
        res-code      (get-in res-body ["MSS_SignatureResp" "Status" "StatusCode" "Value"])
        signature     (get-in res-body ["MSS_SignatureResp" "MSS_Signature" "Base64Signature"])
        res-reason    (or
                        (get-in res-body ["MSS_SignatureResp" "Status" "StatusMessage"])
                        (get-in res-body ["Fault" "Reason"]))]
    {:result-code   res-code
     :result-reason res-reason
     :signature     signature}))


(defn print-cert-info
  [trust-store signed-data signer-info signer-id cert-holder signer-cert serial-no]
  (let [
        _          (log/info-m "Class of trust-store {}" (.getClass trust-store))
        _          (log/info-m "Class of signed-data {}" (.getClass signed-data))
        _          (log/info-m "Class of signer-info {}" (.getClass signer-info))
        _          (log/info-m "Class of cert-holder {}" (.getClass cert-holder))
        _          (log/info-m "Class of signer-cert {}" (.getClass signer-cert))

        ; the sent message
        auth-msg   (String. (-> signed-data .getSignedContent .getContent))

        _          (log/info-m "serial number: {}" serial-no)

        ; not before
        not-before (.getNotBefore signer-cert)
        _          (log/info-m "not before: {}" not-before)

        ; not after
        not-after  (.getNotAfter signer-cert)
        _          (log/info-m "not after: {}" not-after)

        ; subject
        subject    (.getSubjectDN signer-cert)
        _          (log/info-m "subject: {}" subject)

        ; issuer
        issuer-dn  (.getIssuerDN signer-cert)
        _          (log/info-m "issuer dn: {}" issuer-dn)

        ; algorithm
        algorithm  (-> signer-cert
                       .getPublicKey
                       .getAlgorithm)
        _          (log/info-m "algorithm: {}" algorithm)
        ]))

(defn validate-signature [b64-signature conf]
  (try
    (let [truststore-pwd    (.toCharArray (:mid-sign-truststore-pwd conf))
          truststore-path   (str config/config-path "/" (:mid-sign-truststore-file conf))
          ts-file-is        (FileInputStream. truststore-path)
          trust-store       (doto
                              (KeyStore/getInstance "JKS")
                              (.load ts-file-is truststore-pwd))

          signed-data       (CMSSignedData. (Base64/decodeBase64 b64-signature))

          signer-info       (-> signed-data
                                .getSignerInfos
                                .getSigners
                                first)
          signer-id         (.getSID signer-info)
          cert-holder       (-> signed-data
                                .getCertificates
                                (.getMatches signer-id)
                                first)
          signer-cert       (.getCertificate (JcaX509CertificateConverter.) cert-holder)
          issuer            (.getName (.getIssuerDN signer-cert))

          ; the serial number
          serial-no         (->> signer-cert
                                 .getSubjectDN
                                 .getName
                                 .toUpperCase
                                 (re-find #".*SERIALNUMBER=(.{16}).*")
                                 second)
          ;_                 (println "*** serial no:" serial-no)

          ; is verified?
          _                 (Security/addProvider (BouncyCastleProvider.))
          is-verified       (.verify signer-info
                                     (.build (doto
                                               (JcaSimpleSignerInfoVerifierBuilder.)
                                               (.setProvider "BC")) cert-holder))
          _                 (when (not is-verified)
                              (throw Exception))

          ; cert path validation
          certs             (.build (doto
                                      (JcaCertStoreBuilder.)
                                      (.setProvider "BC")
                                      (.addCertificates (.getCertificates signed-data))))
          target-constraint (doto
                              (X509CertSelector.)
                              (.setIssuer (.getEncoded (.getIssuer signer-id)))
                              (.setSerialNumber (.getSerialNumber signer-id)))
          params            (doto
                              (PKIXBuilderParameters. trust-store target-constraint)
                              (.addCertStore certs)
                              (.setRevocationEnabled false))
          ; throws an exception when not valid
          pb-result         (.build (CertPathBuilder/getInstance "PKIX" "BC") params)

          ;TODO this was the original version, remove if the new version seems to be stable
          ;cert-list         (doto
          ;                    (ArrayList.)
          ;                    (.add signer-cert))
          ;params            (doto
          ;                    (PKIXParameters. trust-store)
          ;                    (.setRevocationEnabled true))
          ; Activate OCSP
          ;_                 (Security/setProperty "ocsp.enable" "true")
          ; Activate CRLDP
          ;_                 (System/setProperty "com.sun.security.enableCRLDP" "true")
          ; Ensure that the ocsp.responderURL property is not set.
          ;_                 (when (some? (Security/getProperty "ocsp.responderURL"))
          ;                    (throw (Exception. "The ocsp.responderURL property must not be set")))
          ;cpv               (CertPathValidator/getInstance (CertPathValidator/getDefaultType))
          ;_                 (.validate cpv (.generateCertPath (CertificateFactory/getInstance "X.509") cert-list) params)
          ]
      {:serial serial-no
       :issuer issuer})
    (catch Throwable e
      (log/error-e e "Signed signature validation failed.")
      (throw (ex/business-ex :phone-activation/mid-auth-test-invalid-signed-data "The validation of the signed data was unsuccessful." {})))))


(defn send-test-mid-auth' [phone-number language method]
  (let [{:keys [test-auth-msg] :as conf} (get-in config/config [:mobile-id :otp-sms])
        message   (get test-auth-msg (keyword language))
        sign-type (cond
                    (= method "app") :mid-app
                    (= method "sim") :mid-sim
                    ;; DEPRECATED, only for backward compatibility
                    :else :mid)
        {:keys [result-code result-reason signature]}
        (send-signature-request sign-type (merge conf {:language     language
                                                       :phone-number phone-number
                                                       :message      message}))
        resp      (when (#{"500" "502"} result-code)
                    (validate-signature signature conf))]
    (cond
      (#{"500" "502"} result-code)
      resp
      (= "UNKNOWN_CLIENT" result-reason)
      (throw (ex/business-ex :phone-activation/mid-auth-test-unknown-client "Test MID authentication was unsuccessful." result-reason))
      (= "EXPIRED_TRANSACTION" result-reason)
      (throw (ex/business-ex :phone-activation/mid-auth-test-expired-transaction "Test MID authentication was unsuccessful." result-reason))
      (= "USER_CANCEL" result-reason)
      (throw (ex/business-ex :phone-activation/mid-auth-test-user-cancel "Test MID authentication was unsuccessful." result-reason))
      (= "PIN_NR_BLOCKED" result-reason)
      (throw (ex/business-ex :phone-activation/mid-auth-test-pin-nr-blocked "Test MID authentication was unsuccessful." result-reason))
      (= "CARD_BLOCKED" result-reason)
      (throw (ex/business-ex :phone-activation/mid-auth-test-card-blocked "Test MID authentication was unsuccessful." result-reason))
      (= "NO_KEY_FOUND" result-reason)
      (throw (ex/business-ex :phone-activation/mid-auth-test-no-key-found "Test MID authentication was unsuccessful." result-reason))
      (= "PB_SIGNATURE_PROCESS" result-reason)
      (throw (ex/business-ex :phone-activation/mid-auth-test-pb-signature-process "Test MID authentication was unsuccessful." result-reason))
      (= "NO_CERT_FOUND" result-reason)
      (throw (ex/business-ex :phone-activation/mid-auth-test-no-cert-found "Test MID authentication was unsuccessful." result-reason))
      :else
      (throw (ex/business-ex :phone-activation/mid-auth-test-error "Test MID authentication was unsuccessful." result-reason)))))


(defn generate-sms-token []
  "Generates 4-char random uppercase string"
  (let [chars     (map char (concat (range 49 58)
                                    (range 65 79)
                                    (range 80 90)))
        rnd-chars (take 4 (repeatedly #(rand-nth chars)))]
    (apply str rnd-chars)))

(defn send-sms-original!
  [send-sms conf language phone-number message enc-jwt]
  (let [res (when send-sms
              (log/info-m "Sending SMS with original API")
              (send-signature-request :sms (merge conf {:language     language
                                                        :phone-number phone-number
                                                        :message      message})))]
    (cond
      (or (not send-sms) (= "100" (:result-code res)))
      enc-jwt

      (= "403" (:result-code res))                          ; reason: CARD_BLOCKED
      (throw (ex/business-ex :phone-activation/otp-sms-card-blocked "Could not send the OTP SMS." (:result-reason res)))

      :else
      (do
        (log/warn-m "Got an error while requesting for OTP SMS" res)
        (throw (ex/business-ex :phone-activation/sms-sending-error "Could not send the OTP SMS." (:result-reason res)))))))

(defn send-sms-digital-marketplace-api!
  [send-sms conf _language phone-number message enc-jwt]
  (when send-sms
    (try
      (log/info-m "Sending SMS with Digital Marketplace API")
      (client/post
        "https://api.swisscom.com/messaging/sms"
        {:content-type :json
         :accept       :json
         :headers      {"client_id"   (:dm-api-client-id conf)
                        "SCS-Version" "2"}
         :body         (json/generate-string {:to   phone-number
                                              :text message})})
      (catch Exception ex
        (throw (ex/business-ex :phone-activation/sms-sending-error "Could not send the OTP SMS." (.getMessage ex))))))
  enc-jwt)

(defn send-sms-sts!
  [send-sms conf _language phone-number message enc-jwt]
  (when send-sms
    (let [request-id (str "spectra-" (random-uuid))]
      (try
        (log/info-m (str "Sending Swisscom STS SMS request" request-id))
        (client/post
          (str (:sts-api-url conf) "/messaging/sms")
          {:content-type :json
           :accept       :json
           :headers      {"client_id"      (:sts-client-id conf)
                          "sts-request-id" request-id
                          "sts-version"    "1.0"
                          "x-api-key"      (:sts-client-secret conf)
                          "content-type"   "application/json"
                          "accept"         "application/json"
                          }
           :body         (json/generate-string {:to   phone-number
                                                :text message})})
        (catch Exception ex
          (throw (ex/business-ex :phone-activation/sms-sending-error "Could not send the OTP SMS." (.getMessage ex)))))))
  enc-jwt)

(defn send-auth-sms' [phone-number language send-mode]
  (when (not-any? #(string/starts-with? phone-number %)
                  (get-in config/config [:mobile-id :otp-sms :allowed-prefixes]))
    (throw (ex/forbidden-ex :phone-activation/sms-sending-error "Could not send the OTP SMS." {})))
  (let [conf     (get-in config/config [:mobile-id :otp-sms])
        message  (get-in conf [:message (keyword language)])
        send-sms (get conf :send-otp-sms true)
        token    (generate-sms-token)
        enc-jwt  (create-encrypted-sms-token-jwt phone-number token)
        message  (str/replace message #"#TOKEN#" token)
        send-fn  (case send-mode
                   "digital-marketplace" send-sms-digital-marketplace-api!
                   "original" send-sms-original!
                   "sts" send-sms-sts!
                   send-sms-original!)]
    (send-fn send-sms conf language phone-number message enc-jwt)))


(defn validate-sms-token'
  [phone-number user-token encrypted-jwt-token]
  (let [master-token (get-in config/config [:mobile-id :otp-sms :master-sms-token])
        ;; when the decrypt fails for any reason (for example expired token) the claims will be empty,
        ;; so the result will be false
        claims       (try
                       (jwt/decrypt encrypted-jwt-token)
                       (catch ExceptionInfo e
                         nil))
        ;_            (clojure.pprint/pprint claims)
        {:keys [sub exp sms-token]} claims
        expires-at   (date-time/create-from-epoch-secs (or exp 0))
        valid?       (and (= sub phone-number)
                          (or (= sms-token (str/upper-case user-token))
                              (and (not (str/blank? master-token))
                                   (= user-token master-token))))]
    {:jwt-phone-no  sub
     :jwt-sms-token sms-token
     :valid?        valid?
     :expires-at    expires-at}))


(defn get-esign-evidences' [phone-number]
  (let [base-url (get-in config/config [:mobile-id :e-signature :base-url])
        url      (str base-url "/api/evidences/lookup")
        req-body {:msisdn phone-number}
        result
                 (try
                   (->
                     (http/post url {:socket-timeout 50000
                                     :conn-timeout   5000
                                     :content-type   :json
                                     :body           (json/generate-string req-body)})
                     :body
                     (json/parse-string true))
                   (catch Throwable e
                     (let [status (:status (ex-data e))]
                       ;; not found means there is no evidence, but it is not an error
                       (if (= 404)
                         nil
                         (throw (ex/internal-ex :phone-activation/get-profile-error
                                                "Could not get e-sign evidences." status))))))]
    result))


(defn check-recaptcha-response' [recaptcha-response recaptcha-version]
  (if-let [secret-key (or (get-in config/config [:mobile-id :recaptcha :secret-keys (keyword recaptcha-version)])
                          ;; Fallback to not break current prod
                          (get-in config/config [:mobile-id :recaptcha :secret-key]))]
    (let [url    (get-in config/config [:mobile-id :recaptcha :url])
          result (http/post url {:form-params {:secret   secret-key
                                               :response recaptcha-response}})
          {:keys [success score error-codes] :as recaptcha-resp} (when (= (:status result) 200)
                                                                   (some-> result
                                                                           :body
                                                                           (json/parse-string true)))]
      (cond
        (not success)
        (throw (ex/business-ex :phone-activation/invalid-recaptcha-response "The reCaptcha result is invalid." error-codes))

        (and (number? score)
             (<= score 0.1))
        (do
          (log/warn-m "Recaptcha score too low {} with response {}" score recaptcha-resp)
          (throw (ex/forbidden-ex :phone-activation/invalid-recaptcha-response "Forbidden" false)))

        :else
        nil))))


(defhealthcheck healthcheck/registry :phoneActivation 3
                :function
                (fn []
                  (let [phone-number (get-in config/config [:mobile-id :healthcheck-phone-number])
                        res          (call-mid-proxy "/get-user-status" {:msisdn     phone-number
                                                                         :request-id (generate-request-id)})]
                    (when (not= 100 (get-in res [:status :statusCode :value]))
                      (throw (ex/internal-ex :healthcheck/unsuccessful-phone-activation
                                             "The phone activation healthcheck was unsuccessful." {})))
                    {:getUserStatus "SUCCESSFUL"})))


(defrecord MobileIdProxy []
  mobileid/MobileId
  (get-sim-card [this phone-number]
    (call-mid-proxy "/get-sim-card" {:msisdn     phone-number
                                     :request-id (generate-request-id)}))

  (get-user [this phone-number]
    (call-mid-proxy "/get-user" {:msisdn     phone-number
                                 :request-id (generate-request-id)}))

  (get-user-status [this phone-number]
    (call-mid-proxy "/get-user-status" {:msisdn     phone-number
                                        :request-id (generate-request-id)}))

  (get-profile [this phone-number]
    (call-mid-proxy "/get-profile" {:msisdn     phone-number
                                    :request-id (generate-request-id)}))

  (insert-user [this phone-number]
    (call-mid-proxy "/insert-user" {:msisdn     phone-number
                                    :request-id (generate-request-id)}))

  (activate-sim-method [this phone-number language recovery-code use-second-method]
    ;;TODO change it to activate-sim-method after the new bellboy version is deployed
    (call-mid-proxy "/activate-user" {:msisdn            phone-number
                                      :request-id        (generate-request-id)
                                      :language          language
                                      :recovery-code     recovery-code
                                      :use-second-method (boolean use-second-method)}))

  (deactivate-sim-method [this phone-number]
    (call-mid-proxy "/deactivate-sim-method" {:msisdn     phone-number
                                              :request-id (generate-request-id)}))

  (activate-app-method [this phone-number language recovery-code use-second-method]
    (call-mid-proxy "/activate-app-method" {:msisdn            phone-number
                                            :request-id        (generate-request-id)
                                            :language          language
                                            :recovery-code     recovery-code
                                            :use-second-method (boolean use-second-method)}))

  (deactivate-app-method [this phone-number]
    (call-mid-proxy "/deactivate-app-method" {:msisdn     phone-number
                                              :request-id (generate-request-id)}))

  (reset-user [this phone-number]
    (call-mid-proxy "/reset-user" {:msisdn     phone-number
                                   :request-id (generate-request-id)}))

  (generate-recovery-code [this phone-number language]
    (call-mid-proxy "/generate-recovery-code" {:msisdn     phone-number
                                               :language   language
                                               :request-id (generate-request-id)}))

  (has-recovery-code [this phone-number]
    (call-mid-proxy "/has-recovery-code" {:msisdn     phone-number
                                          :request-id (generate-request-id)}))

  (send-auth-sms [this phone-number language send-mode]
    (send-auth-sms' phone-number language send-mode))

  (validate-sms-token [this phone-number user-token encrypted-token-jwt]
    (validate-sms-token' phone-number user-token encrypted-token-jwt))

  (send-test-mid-auth [this phone-number language method]
    (send-test-mid-auth' phone-number language method))

  (get-esign-evidences [this phone-number]
    (get-esign-evidences' phone-number))

  (check-recaptcha-response [this recaptcha-response recaptcha-version]
    (check-recaptcha-response' recaptcha-response recaptcha-version))

  )

(defn create-mobileid-proxy []
  (log/info-m "Start")
  (->MobileIdProxy))

(def impl {:start #(create-mobileid-proxy)})

