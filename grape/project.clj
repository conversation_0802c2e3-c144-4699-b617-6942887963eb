(defproject grape "0.8.0"
  :description "The posting stack black hole."
  :url "https://github.com/DEEP-IMPACT-AG/spectra/grape"
  :license {:name "Proprietary"}

  :dependencies [; clojure
                 [org.clojure/clojure "1.11.1"]
                 [org.clojure/core.async "1.6.673"]
                 [org.clojure/java.classpath "1.0.0"]
                 [org.clojure/tools.cli "1.0.214"]
                 [org.apache.httpcomponents/httpcore "4.4.16"]
                 [org.clojure/core.incubator "0.1.4"]

                 ; nrepl - always available
                 [nrepl/nrepl "1.1.0"]

                 ; mount
                 [mount "0.1.17"]

                 ; aws
                 [com.amazonaws/aws-java-sdk-core "1.12.408"]
                 [com.amazonaws/aws-java-sdk-s3 "1.12.408"]
                 [com.amazonaws/aws-java-sdk-logs "1.12.408"]
                 [com.amazonaws/aws-java-sdk-cloudwatch "1.12.408"]
                 [com.amazonaws/aws-java-sdk-sts "1.12.408"]

                 ; db
                 [com.novemberain/monger "3.6.0"]
                 [com.github.lookfirst/sardine "5.10"]
                 [org.apache.tika/tika-java7 "1.27"]
                 [org.apache.commons/commons-compress "1.22"]
                 [de.bitinsomnia/webdav-embedded-server "0.1.2"
                  :exclusions [ch.qos.logback/logback-classic
                               org.eclipse.jetty/jetty-server
                               org.eclipse.jetty/jetty-servlet
                               org.eclipse.jetty/jetty-servlets
                               io.milton/milton-api
                               io.milton/milton-server-ce]]
                 [io.milton/milton-api "*******"]
                 [io.milton/milton-server-ce "*******"]

                 ; ftp
                 [com.velisco/clj-ftp "1.2.0"]
                 [clj-ssh "0.5.14"]

                 ; data
                 [cheshire "5.11.0"]
                 [com.rpl/specter "1.1.4"]
                 [com.cognitect/transit-clj "1.0.329"]
                 [diffit "1.0.0"]
                 [org.jsoup/jsoup "1.15.3"]
                 [org.clojure/data.csv "1.0.1"]
                 [com.taoensso/nippy "3.2.0"]
                 [org.clojure/data.xml "0.0.8"]
                 [clj-commons/clj-yaml "1.0.29"]

                 ; explicitly load the jackson libs because of potential deserialization flaws
                 [com.fasterxml.jackson.core/jackson-core "2.14.2"]
                 [com.fasterxml.jackson.core/jackson-databind "2.14.2"]
                 [com.fasterxml.jackson.core/jackson-annotations "2.14.2"]
                 [com.fasterxml.jackson.dataformat/jackson-dataformat-cbor "2.14.2"]
                 [com.fasterxml.jackson.dataformat/jackson-dataformat-smile "2.14.2"]
                 [com.fasterxml.jackson.dataformat/jackson-dataformat-yaml "2.14.2"]

                 ; aws signer
                 [vc.inreach.aws/aws-signing-request-interceptor "0.0.22" :excludes [org.apache.httpcomponents/httpcore]]

                 ; date & time
                 [clj-time "0.15.2"]
                 [clojure.java-time "0.3.3"]
                 [org.immutant/scheduling "2.1.10" :exclusions
                  [ch.qos.logback/logback-classic
                   org.jboss.logging/jboss-logging]]

                 ; support
                 [org.clojure/math.numeric-tower "0.0.5"]
                 [com.eaio.uuid/uuid "3.2"]
                 [org.glassfish.corba/glassfish-corba-omgapi "4.2.4"] ;; Required for com.eaio.uuid/uuid use with JDK11
                 ;; as not bundled by default. No effect in JDK8
                 [byte-streams "0.2.4"]
                 [robert/hooke "1.3.0"]
                 [potemkin "0.4.6"]
                 [commons-codec/commons-codec "1.15"]
                 [camel-snake-kebab "0.4.3"]
                 [com.cloudinary/cloudinary-http44 "1.33.0"]
                 [com.twilio.sdk/twilio "7.16.0"]           ;; Unless used for any new clients, don't bother with upgrading
                 [com.walmartlabs/lacinia "0.31.0"]         ;; Don't bump until resolve invalid scalar types
                 [org.clojure/core.memoize "1.0.257"]

                 [com.google.guava/guava "23.6-jre"]

                 ; logging
                 [co.wrisk.logback/logback-ext-cloudwatch-appender "1.0.9"]
                 [ch.qos.logback/logback-classic "1.2.9"]
                 [ch.qos.logback/logback-core "1.2.9"]
                 [net.logstash.logback/logstash-logback-encoder "7.2"]
                 [org.clojars.stanhbb/smbh-log "1.1.9"]
                 [org.slf4j/log4j-over-slf4j "1.7.36"]
                 [org.slf4j/jul-to-slf4j "1.7.36"]
                 [org.slf4j/jcl-over-slf4j "1.7.36"]
                 [org.slf4j/slf4j-api "1.7.36"]

                 ; metrics
                 [metrics-clojure "2.10.0"]

                 ; configuration
                 [environ "1.2.0"]

                 ; security
                 [buddy/buddy-core "1.10.1"]
                 [buddy/buddy-sign "3.4.1"]

                 ; web API
                 [ring/ring-core "1.9.6"]
                 [ring/ring-jetty-adapter "1.9.6"]
                 [bidi "2.1.6"]
                 [clj-http "3.12.3" :excludes [org.apache.httpcomponents/httpcore org.apache.httpcomponents/httpcore-nio]]

                 ; Elasticsearch
                 [cc.qbits/spandex "0.7.10" :excludes [org.apache.httpcomponents/httpcore]]

                 ;; Test containers
                 [clj-test-containers "0.7.4"]

                 ;; Async
                 [manifold "0.2.3"]

                 ;; http
                 [hato "0.9.0"]

                 [faker "0.3.2"]
                 [aprint "0.1.3"]]

  :plugins [[test2junit "1.4.2"]
            [lein-ancient "1.0.0-RC3"]
            [lein-set-version "0.4.1"]
            [lein-environ "1.2.0"]
            [lein-marginalia "0.9.1"]
            [jonase/eastwood "0.3.12"]
            [lein-count "1.0.9"]
            [s3-wagon-private "1.3.4"]]

  ; both used by the de.bitinsomnia/webdav-embedded-server
  :repositories {"jcenter"      "https://jcenter.bintray.com"
                 ;"milton"       "https://dl.bintray.com/milton/Milton"
                 "di-releases"  {:url           "s3p://di-mvn-private/releases/"
                                 :no-auth       true
                                 :sign-releases false}
                 "di-snapshots" {:url           "s3p://di-mvn-private/snapshots/"
                                 :no-auth       true
                                 :sign-releases false
                                 :releases      false}}

  :main ch.deepimpact.black.system

  :source-paths ["src/main/clojure"]
  :java-source-paths ["src/main/java"]
  :resource-paths ["src/main/resources"]
  :test-paths ["src/test/clojure"]

  :aliases {"doc"         ["marg" "-m" "-d" "doc" "-f" "index.html" "src/main/clojure"]
            "count-black" ["count" ":by-file" "./src/main/clojure/ch/deepimpact/black"]
            "count-all"   ["count" ":by-file" "./src/main/clojure"]}

  :eastwood {:source-paths    ["src/main/clojure"]
             :namespaces      [:source-paths]
             :exclude-linters [:suspicious-expression :unused-ret-vals]}

  :test2junit-output-dir ~(or (System/getenv "CIRCLE_TEST_REPORTS") "target/test2junit")
  :test2junit-run-ant false
  :test-selectors {:dealestate :dealestate}

  :jar-name "spectra.jar"
  :uberjar-name "spectra-standalone.jar"

  :cloverage {:ns-regex [#"ch.deepimpact.*"]}

  :profiles {:uberjar {:aot :all}
             :dev     {:source-paths   ["src/dev/clojure"]
                       :dependencies   [[clj-kondo "2023.01.20"]
                                        [org.clojure/test.check "1.1.1"]
                                        [org.clojure/tools.namespace "1.4.1"]
                                        [ring/ring-mock "0.4.0"]
                                        [better-cond "2.1.5"]
                                        [philoskim/debux "0.8.2"]
                                        [com.hypirion/clj-xchart "0.2.0"]
                                        [criterium "0.4.6"]]
                       :plugins        [[jonase/eastwood "0.4.0"]
                                        [lein-ancient "1.0.0-RC3"]
                                        [lein-cloverage "1.2.2"]
                                        [lein-kibit "0.1.8"]
                                        [lein-nvd "1.4.1"]]
                       :env            {:spectra-env      "dev"
                                        :spectra-universe "universe-test"}
                       :resource-paths ["src/test/resources"]}
             :test    {:resource-paths ["src/test/resources"]
                       :aot            :all
                       :env            {:spectra-env      "tst"
                                        :spectra-universe "universe-test"}}})
