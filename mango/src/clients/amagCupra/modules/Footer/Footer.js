/* @flow */
import React from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import classNames from 'classnames';
import { getLocale } from '../../../../modules/i18n/selectors';
import { localize, type LocalizeProps } from '../../../../modules/i18n';
import withDealer from '../../../amagCore/modules/withDealer';
import type { DealerProps } from '../../../amagCore/modules/withDealer';
import {
	getDealerHasNewRetailerLayout,
	makeGetDealerName,
} from '../../../amagCore/modules/DealerConfigProvider/selectors';
import FooterLink from './FooterLink';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import {
	externalUrls,
	NEW_RETAIL_PAGE_DEALERS_WHITELIST,
} from '../../../amagCore/modules/constants';
import externalLinks from '../externalLinks';
import { AddressAccordion } from '../../../amagCore/modules/footer';

import SocialIcon from './SocialIcon';
import styles from './Footer.css';

export type ExternalProps = {
	links: Array<any>,
};

export type Props = ExternalProps &
	DealerProps &
	LocalizeProps & {
		dealerName: string,
	};

function makeFooterLinks(dealerId, locale) {
	const gschwendGarage = '**********';
	const gngGarage = '**********';

	const links = [
		{
			key: 'contact',
			route: 'contact',
			caption: 'contact',
		},
		{
			key: 'catalog',
			route: externalLinks[locale].catalog,
			caption: 'catalogs',
			external: true,
		},
		{
			key: 'legal.notice',
			route: 'legalNotice',
			caption: 'legalNotice',
		},
		{
			key: 'dataProtection',
			route: externalUrls.cupra.dataProtection[locale],
			caption: 'dataProtection',
			external: true,
		},
		{
			key: 'imprint',
			route: 'imprint',
			caption: 'imprint',
		},
	].filter(
		item =>
			NEW_RETAIL_PAGE_DEALERS_WHITELIST.includes(dealerId) ||
			item.key !== 'imprint',
	);

	if (dealerId === gschwendGarage) {
		links.push({
			key: 'partnerDataProtection',
			route: `https://www.gschwend-garage.ch/${locale}/datenschutzerklaerung`,
			caption: 'partnerDataProtection',
			external: true,
		});
	}

	if (dealerId === gngGarage) {
		links.push({
			key: 'partnerDataProtection',
			route: `https://www.gng.ch/${locale}/datenschutzerklaerung`,
			caption: 'partnerDataProtection',
			external: true,
		});
	}

	if (dealerId === 'CHE024') {
		links.push({
			key: 'partnerDataProtection',
			route: 'https://www.sennautos.ch/fr/protection-des-donnees',
			caption: 'partnerDataProtection',
			external: true,
		});
	}

	return links;
}

function Footer({ dealerId, social, hasNewRetailerLayout, locale }: Props) {
	const links = makeFooterLinks(dealerId, locale);

	return (
		<footer className={styles.footer}>
			<div className={styles.container}>
				<AddressAccordion
					className={styles.address}
					hideOpeningHours
					hideWebsite
					isFooter={!hasNewRetailerLayout}
				/>
				<ul className={styles.links}>
					{links.map((item: any, idx: number) => (
						<FooterLink
							key={idx}
							className={styles.linkItem}
							{...item}
							external={item.external}
						/>
					))}
				</ul>
				<ul className={styles.socials}>
					{social.map((item, index) => (
						<SocialIcon
							key={index}
							className={classNames(
								styles.socialIcon,
								styles[item.get('type')],
							)}
							type={item.get('type')}
							url={item.get('url')}
						/>
					))}
				</ul>
			</div>
		</footer>
	);
}

function makeMapStateToProps() {
	const getDealerName = makeGetDealerName();

	return (state: ReduxState) => {
		const locale = getLocale(state);

		return {
			dealerName: getDealerName(state)(locale),
			hasNewRetailerLayout: getDealerHasNewRetailerLayout(state),
		};
	};
}

const withHocs = combineHOCs([
	withRouter,
	connect(makeMapStateToProps),
	withDealer,
	localize(),
	withStyles(styles),
]);

export default withHocs(Footer);
