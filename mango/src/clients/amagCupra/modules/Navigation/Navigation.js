/* eslint-disable max-len */
import React from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import { List } from 'immutable';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import { localize, type LocalizeProps } from '../../../../modules/i18n';
import withDealerBrandWebsiteConfig, {
	type DealerBrandWebsiteConfigProps,
} from '../../../amagCore/modules/withDealerBrandWebsiteConfig';
import {
	getDealerHasNewRetailerLayout,
	makeGetDealerHasServiceOnly,
	getDealerIsAmagRetailer,
} from '../../../amagCore/modules/DealerConfigProvider/selectors';
import DesktopNav from './DesktopNav';
import externalLinks from '../externalLinks';
import { namespace } from './i18n';
import withDealer from '../../../amagCore/modules/withDealer';
import { NAV_MODELS } from './constants';
import CarModelTabs from './CarModelTabs';
import {
	DAMAGE_REPORT_ALIAS,
	OCCASION_PLUS_ALIAS,
} from '../../modules/constants';
import { prepareLinkItem } from '../../../amagCore/modules/DealerConfigProvider/prepareLinkItem';

type ExternalProps = {
	offers: List<any>,
};

type InternalProps = {
	serviceAppointmentUrl?: string,
	testDriveUrl?: string,
	newCarsUrl?: string,
	usedCarsUrl?: string,
	hasNewRetailerLayout?: boolean,
	isServicePartner?: boolean,
	isAmagRetailer?: boolean,
	showFleetcenterURL?: boolean,
	showFleetSpecialistURL?: boolean,
	showTotalGlassURL?: boolean,
	showTotalRepairURL?: boolean,
	damageReportUrl?: string,
	hasOccasionPlus?: boolean,
};

type Props = ExternalProps &
	InternalProps &
	LocalizeProps &
	DealerBrandWebsiteConfigProps;

function Navigation({
	t,
	locale,
	serviceAppointmentUrl,
	testDriveUrl,
	newCarsUrl,
	usedCarsUrl,
	hasNewRetailerLayout,
	isServicePartner,
	offers,
	isAmagRetailer,
	damageReportUrl,
	showFleetCenterURL,
	showFleetSpecialistURL,
	showTotalGlassURL,
	showTotalRepairURL,
	hasOccasionPlus,
}: Props) {
	const primaryDropdownNav = [
		{
			key: 'carModelsTabs', // used to remove from mobile nav & SERVICE only partners
			label: t(`${namespace}.models`),
			render: () => <CarModelTabs models={NAV_MODELS} />,
		},
		{
			key: 'offers',
			label: t(`${namespace}.offers`),
			image: {
				rawFormat: 'cloudinary/V1',
				raw: {
					public_id:
						'amag/cupra/navigation/L-XL-N1-GlobalNavigation-Angebote.jpg', // 3 cars circling
					version: 1,
				},
			},
			innerElements: [
				{
					// Heading
					key: 'offers',
					label: t(`${namespace}.offers`),
				},
				{
					key: 'specialModels',
					label: t(`${namespace}.specialModels`),
					to: externalLinks[locale].offers,
					external: true,
				},
				prepareLinkItem({
					key: 'newCars',
					label: t(`${namespace}.newCars`),
					uri: newCarsUrl,
					locale,
				}),
				prepareLinkItem({
					key: 'usedCars',
					label: t(`${namespace}.usedCars`),
					uri: hasOccasionPlus ? OCCASION_PLUS_ALIAS : usedCarsUrl,
					locale,
					isInternalLink: hasOccasionPlus,
				}),
				{
					key: 'electromobility',
					label: t(`${namespace}.electromobility`),
					to: 'electrification',
				},
				{
					// Heading
					key: 'aboutTheCars',
					label: t(`${namespace}.aboutTheCars`),
				},
				{
					key: 'catalog',
					label: t(`${namespace}.catalog`),
					to: externalLinks[locale].catalog,
					external: true,
				},
				{
					key: 'businessClients',
					label: t(`${namespace}.businessClients`),
				},
				{
					key: 'cupraForBusiness',
					label: t(`${namespace}.cupraForBusiness`),
					hasChildren: true,
				},
				offers.size
					? {
						// Heading
						key: 'offersAndActions',
						label: t(`${namespace}.offersAndActions`), // Only show if there are offers
					}
					: null,
			]
				.filter(item => {
					// Remove the items (set in AMAG Admin) if they aren't set
					if (item === null) return false;

					if (!usedCarsUrl && item.key === 'offers') {
						return false;
					}

					if (isServicePartner && item && item.key) {
						// Items removed from SERVICE only partners
						return ![
							'specialModels',
							'newCars',
							'electromobility',
							'aboutTheCars',
							'catalog',
							'businessClients',
							'cupraForBusiness',
						].includes(item.key);
					}

					return true;
				})
				.concat([
					...offers
						.map(offer => ({
							label: offer.getIn(
								['post', 'managed', 'published', 'article', 'title'],
								'',
							),
							to: offer
								.getIn(['post', 'meta', 'aliases', 0], '')
								.startsWith('amag/')
								? 'article.alias.amag'
								: 'article.alias',
							params: {
								aliasName: offer
									.getIn(['post', 'meta', 'aliases', 0], '')
									.split('/')
									.pop(),
								locale,
							},
						}))
						.filter(item => item.label)
						.toJS(),
				]),
		},
		{
			key: 'service',
			label: t(`${namespace}.service`),
			image: {
				rawFormat: 'cloudinary/V1',
				raw: {
					public_id:
						'amag/cupra/navigation/L-XL-N1-GlobalNavigation-BrandUniverse.jpg', // blondie
					version: 1,
				},
			},
			innerElements: [
				{ label: 'CUPRA for drivers', key: 'cupraForDrivers' },
				{
					key: 'cupraConnect',
					label: t(`${namespace}.connect`),
					to: 'connect',
				},
				{
					key: 'cupraCare',
					label: t(`${namespace}.care.title`),
					to: 'care',
					description: t(`${namespace}.care.description`),
				},
				{
					key: 'instructions',
					label: t(`${namespace}.instructions.title`),
					description: t(`${namespace}.instructions.description`),
					to: 'instructions',
				},
			].filter(item => {
				// Remove the items (set in AMAG Admin) if they aren't set
				if (item === null) return false;

				if (isServicePartner && item && item.key) {
					// Remove the `offers` heading label if `usedCars` is not set
					if (
						item.key === 'offers'
						// !array.find(item2 => item2 && item2.key === 'usedCars')
					) {
						return false;
					}

					// Items removed from SERVICE only partners
					return ![
						'cupraForDrivers',
						'cupraConnect',
						'cupraCare',
						'instructions',
					].includes(item.key);
				}

				return true;
			}),
		},
		{
			key: 'aboutus',
			label: t(`${namespace}.aboutus`),
			image: {
				rawFormat: 'cloudinary/V1',
				raw: {
					public_id:
						'amag/cupra/navigation/L-XL-N1-GlobalNavigation-Services.jpg', // Bowie copycat
					version: 1,
				},
			},
			innerElements: [
				{
					label: t(`${namespace}.aboutus`),
					to: 'aboutus',
				},
				{
					label: t(`${namespace}.team`),
					to: 'team',
				},
				{
					label: t(`${namespace}.contact`),
					to: 'contact',
				},
				// $FlowFixMe
			].filter(
				item =>
					(!hasNewRetailerLayout || item.to !== 'team') &&
					(!isAmagRetailer || item.to !== 'team'),
			),
		},
	]
		.filter(item => !isServicePartner || item.key !== 'carModelsTabs') // Remove the car models tab if the user is not a service partner
		.filter(
			item =>
				(item.innerElements && item.innerElements.length) ||
				(item.render && item.render !== undefined),
		); // Remove Parent menu if has no children

	if (showTotalGlassURL && primaryDropdownNav[2].innerElements) {
		primaryDropdownNav[2].innerElements.push({
			label: t(`${namespace}.totalGlass`),
			to: 'total-glass',
		});
	}

	if (showTotalRepairURL && primaryDropdownNav[2].innerElements) {
		primaryDropdownNav[2].innerElements.push({
			label: t(`${namespace}.totalRepair`),
			to: 'total-repair',
		});
	}

	const damageReportLink = () => {
		const damageReportLinkItem = prepareLinkItem({
			uri: damageReportUrl || DAMAGE_REPORT_ALIAS,
			locale,
			isInternalLink: !damageReportUrl,
		});

		if (!damageReportLinkItem) return {};

		return {
			to: damageReportLinkItem.to,
			params: damageReportLinkItem.params || {},
		};
	};

	const secondaryNav = [
		{
			key: 'configurator',
			to: externalLinks[locale].configurator,
			text: t(`${namespace}.configurator`),
			external: true,
		},
		{
			key: 'testdrive',
			text: t(`${namespace}.testDrive`),
			to: testDriveUrl,
		},
		{
			key: 'serviceappointment',
			to: serviceAppointmentUrl || 'serviceappointmentform',
			text: hasNewRetailerLayout
				? t(`${namespace}.retailServiceAppointment`)
				: t(`${namespace}.serviceAppointment`),
			external: !!serviceAppointmentUrl,
		},
		{
			key: 'damageReport',
			text: t(`${namespace}.damageReport`),
			...damageReportLink(),
		},
	]
		.filter(item => !isServicePartner || !['configurator'].includes(item.key))
		.filter(item => !hasNewRetailerLayout || !['testdrive'].includes(item.key))
		.filter(
			item =>
				!hasNewRetailerLayout ||
				!!serviceAppointmentUrl ||
				item.key !== 'serviceappointment',
		);

	if (showFleetCenterURL) {
		// $FlowFixMe
		primaryDropdownNav[1].innerElements.push({
			key: 'commercialClient',
			label: t(`${namespace}.commercialClient`),
		});

		// $FlowFixMe
		primaryDropdownNav[1].innerElements.push({
			key: 'fleetCenter',
			to: 'fleet-center',
			label: t(`${namespace}.fleetCenter`),
		});
	}

	if (showFleetSpecialistURL) {
		// $FlowFixMe
		primaryDropdownNav[1].innerElements.push({
			key: 'commercialClient',
			label: t(`${namespace}.commercialClient`),
		});

		// $FlowFixMe
		primaryDropdownNav[1].innerElements.push({
			key: 'fleetSpecialist',
			to: 'fleet-specialist',
			label: t(`${namespace}.fleetSpecialist`),
		});
	}

	if (isServicePartner) {
		// SERVICE
		primaryDropdownNav.splice(1, 0, {
			key: 'service',
			label: t(`${namespace}.service`),
			innerElements: [
				{
					key: 'service',
					label: t(`${namespace}.service`),
				},
				{
					key: 'howToVideo',
					label: t(`${namespace}.howToVideo`),
					to: externalLinks[locale].howToVideo,
					external: true,
				},
				{
					key: 'cupraConnect',
					label: t(`${namespace}.connect`),
					to: 'connect',
				},
				{
					key: 'OTAUpdates',
					label: t(`${namespace}.OTAUpdates`),
					to: externalLinks[locale].OTAUpdates,
					external: true,
				},
				{
					key: 'cupraCare',
					label: t(`${namespace}.care.title`),
					to: 'care',
					description: t(`${namespace}.care.description`),
				},
				{
					key: 'technicalSupport',
					label: t(`${namespace}.technicalSupport`),
					hasChildren: true,
				},
				{
					key: 'equipmentAndAccessories',
					label: t(`${namespace}.equipmentAndAccessories`),
					hasChildren: true,
				},
			],
		});
	}

	return <DesktopNav primary={primaryDropdownNav} secondary={secondaryNav} />;
}

function mapStateToProps(state: ReduxState) {
	const getDealerIsServiceOnlyPartner = makeGetDealerHasServiceOnly();

	return {
		hasNewRetailerLayout: getDealerHasNewRetailerLayout(state),
		isAmagRetailer: getDealerIsAmagRetailer(state),
		isServicePartner: getDealerIsServiceOnlyPartner(state)('cupra'),
	};
}

const withHocs = combineHOCs([
	withRouter,
	localize(),
	connect(mapStateToProps),
	withDealer,
	withDealerBrandWebsiteConfig,
]);

export default (withHocs(Navigation): ReactComponent<EmptyProps>);
