import React, { useEffect } from 'react';
import classNames from 'classnames';
import { withRouter } from 'react-router';
import { connect } from 'react-redux';

import combineHOCs from '../../../../tango/hoc/combineHOCs';
import Link from '../Layout/Link/Link';
import {
	makeGetDealerName,
	makeGetDealerHasServiceOnly,
} from '../../../amagCore/modules/DealerConfigProvider/selectors';
import { getLocale } from '../../../../modules/i18n/selectors';
import { localize, type LocalizeProps } from '../../../../modules/i18n';
import NavigationBlock from './NavigationBlock';
import LocaleSwitcher from '../LocaleSwitcher';
import type { NavElement, DropdownNavElement } from './types';
import { ToggleButton } from '../../../../modules/toggle';
import ToggleContent from '../../../../modules/toggle/fac/Content';
import PrimaryNavContents from './PrimaryNavContents';
import AdminBar from './AdminBar';
import { TOGGLE_NAME } from './constants';

import { Logo, Burger } from '../../assets/svg';
import styles from './Navigation.css';

import withDealerBrandWebsiteConfig from '../../../amagCore/modules/withDealerBrandWebsiteConfig';

type ExternalProps = {
	primary: Array<DropdownNavElement>,
	secondary: Array<NavElement>,
};

type InternalProps = {
	dealerName: string,
	isServicePartner: boolean,
};

type Props = ExternalProps & InternalProps & LocalizeProps;

const getNavbarHeight = navbar =>
	Number(getComputedStyle(navbar).height.replace('px', ''));

function DesktopNav({
	primary,
	secondary,
	dealerName,
	isServicePartner,
	testDriveUrl,
}: Props) {
	useEffect(() => {
		if (document && document.body) {
			let c;
			let currentScrollTop = 0;
			const navbar = document.getElementsByTagName('nav')[0];
			const body = document.body;

			if (body && body.style) {
				body.style.paddingTop = `${getNavbarHeight(navbar)}px`; // set on initial render

				// Hide the navbar when scrolling down, show it when scrolling up
				window.onscroll = () => {
					currentScrollTop = window.pageYOffset;
					const navbarHeight = getNavbarHeight(navbar); // costly operation, but needs to be inside the onscroll function as the height can change depending on the viewport width
					body.style.paddingTop = `${navbarHeight}px`;

					if (c < currentScrollTop && currentScrollTop > navbarHeight) {
						navbar.classList.add(styles.scrollUp);
					} else {
						navbar.classList.remove(styles.scrollUp);
					}

					c = currentScrollTop;
				};
			}
		}
	}, []);

	return (
		<nav className={styles.navigation}>
			<AdminBar />

			<ToggleContent
				name={TOGGLE_NAME}
				closeOnClickOutside={false}
				openPerDefault={false}
				closeOnNav
			>
				{(isOpen: boolean) => (
					<React.Fragment>
						<div className={styles.inner}>
							<div className={styles.main}>
								<Link to="home" className={styles.logoLink}>
									<Logo className={styles.logo} />
								</Link>

								<div className={styles.buttons}>
									<div className={styles.topBar}>
										<div className={styles.secondaryMenuWrapper}>
											<NavigationBlock
												navElements={secondary}
												className={styles.secondaryMenu}
												testDriveUrl={testDriveUrl}
											/>
										</div>
									</div>
									<LocaleSwitcher />
									{isOpen ? (
										<ToggleButton
											name={TOGGLE_NAME}
											className={styles.closeMenu}
											disallowMultipleOpen
										>
											<Burger close />
										</ToggleButton>
									) : (
										<ToggleButton
											name={TOGGLE_NAME}
											className={styles.burgerMenu}
											openedClassName={styles.buttonOpened}
											closedClassName={styles.buttonClosed}
											disallowMultipleOpen
										>
											<Burger />
										</ToggleButton>
									)}
								</div>
							</div>
						</div>

						<div
							className={classNames(styles.menus, {
								[styles.open]: isOpen,
							})}
						>
							<div className={styles.contentsWrapper}>
								<div className={styles.controlsContainer}>
									<Logo className={styles.logo} />
								</div>

								<PrimaryNavContents
									primary={primary}
									isServicePartner={isServicePartner}
								/>
							</div>
						</div>
					</React.Fragment>
				)}
			</ToggleContent>

			<div className={styles.subMenu}>
				<span className={styles.dealerName}>{dealerName}</span>
			</div>
		</nav>
	);
}

function mapStateToProps(state: ReduxState) {
	const getDealerName = makeGetDealerName();

	return () => {
		const getDealerIsServiceOnlyPartner = makeGetDealerHasServiceOnly();
		const locale = getLocale(state);

		return {
			dealerName: getDealerName(state)(locale),
			isServicePartner: getDealerIsServiceOnlyPartner(state)('cupra'),
			locale,
		};
	};
}

export default combineHOCs([
	withRouter,
	connect(mapStateToProps),
	localize(),
	withDealerBrandWebsiteConfig,
])(DesktopNav);
