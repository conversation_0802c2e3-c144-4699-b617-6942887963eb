/* @flow */
import { spread } from '../../../modules/i18n/util';
import translationPhrases from './translationPhrases.json';

import * as ModulesAlias from '../../../modules/alias/i18n';
import * as ModulesEditorMenu from '../../../modules/editorMenu/i18n';
import * as ModulesSeo from '../../../modules/seo/i18n';
import * as ModulesEditorToolbar from '../../../modules/editor-toolbar/i18n';
import * as ModulesEditorDraft from '../../../modules/editorDraft/i18n';
import * as ModulesLogin from '../../../modules/login/i18n';
import * as ModulesSSO from '../../amagCore/modules/sso/i18n';
import * as ModulesModals from '../../amagCore/modules/article-modals/i18n';
import * as PublishDialog from '../../amagCore/modules/editorMenuDialogs/PublishDialog/i18n';
import * as ArchiveDialog from '../../amagCore/modules/editorMenuDialogs/ArchiveDialog/i18n';
import * as ResetDialog from '../../amagCore/modules/editorMenuDialogs/ResetDialog/i18n';
import * as ModulesDynamicMenu from '../../amagCore/modules/dynamicMenu/i18n';
import * as ModulesArticleEditor from '../../amagCore/modules/ArticleEditor/i18n';
import * as ModulesArticlePreview from '../../amagCore/modules/ArticlePreview/i18n';
import * as ModulesTeam from '../../amagCore/modules/team/i18n';
import * as ModulesSupport from '../../amagCore/modules/Support/i18n';
import * as ModulesContact from '../../amagCore/modules/contact/i18n';
import * as ModulesBrands from '../../amagCore/modules/brands/i18n';
import * as ModulesGoogleMap from '../../amagCore/modules/googleMap/i18n';
import * as ModuesForm from '../../amagCore/modules/form/i18n';
import * as ModulesEditor from '../../../modules/editor/i18n';
import * as CoreFooter from '../../amagCore/modules/footer/i18n';

import * as Elements from '../modules/Elements/i18n';
import * as Navigation from '../modules/Navigation/i18n';
import * as Posts from '../modules/Posts/i18n';
import * as HomeSlider from '../modules/HomeSlider/i18n';
import * as Footer from '../modules/Footer/i18n';
import * as CarGrid from '../modules/Car/i18n';

import * as Team from '../views/Team/i18n';
import * as Imprint from '../views/Imprint/i18n';
import * as AboutUsDashboard from '../views/AboutUsDashboard/i18n';
import * as PromotionsDashboard from '../views/PromotionsDashboard/i18n';
import * as Support from '../views/Support/i18n';
import * as Home from '../views/Home/i18n';
import * as Contact from '../views/Contact/i18n';
import { cloudinaryPhrasesDefault } from '../assets/translations/modules';
import * as ArticleOverview from '../../amagCore/modules/admin/ArticleOverview/i18n';
import * as ArticleOverviewGenericPost from '../../amagCore/modules/admin/ArticleOverview/GenericPostContainer/i18n';
import * as ArticleOverviewControl from '../../amagCore/modules/admin/ArticleOverview/Control/i18n';
import * as UmbrellaPages from '../../amagCore/modules/umbrellaPages/i18n';
import * as GarageFinder from '../../amagCore/modules/umbrellaPages/garage-finder/i18n';
import * as LocalUmbrellaPages from '../views/UmbrellaPage/i18n';
import * as ServiceAppointmentForm from '../views/ServiceAppointmentForm/i18n';
import * as ContactForm from '../views/ContactForm/i18n';

const phrases = spread([
	ModulesEditorToolbar,
	Elements,
	Navigation,
	Posts,
	HomeSlider,
	Footer,
	Team,
	Imprint,
	AboutUsDashboard,
	PromotionsDashboard,
	Support,
	Home,
	Contact,
	ModulesLogin,
	CarGrid,
	ModulesAlias,
	ModulesEditorMenu,
	ModulesSeo,
	ModulesModals,
	ModulesEditorDraft,
	PublishDialog,
	ArchiveDialog,
	ResetDialog,
	ModulesDynamicMenu,
	ModulesArticleEditor,
	ModulesArticlePreview,
	ModulesTeam,
	ModulesSupport,
	ModulesEditor,
	ModulesContact,
	ModulesBrands,
	ModulesGoogleMap,
	ModulesSSO,
	ModuesForm,
	ArticleOverview,
	ArticleOverviewGenericPost,
	ArticleOverviewControl,
	UmbrellaPages,
	LocalUmbrellaPages,
	CoreFooter,
	ServiceAppointmentForm,
	ContactForm,
	GarageFinder,
]);

const translations = {
	de: {
		...phrases.de,
		...translationPhrases.de,
		...cloudinaryPhrasesDefault.de,
	},
	fr: {
		...phrases.fr,
		...translationPhrases.fr,
		...cloudinaryPhrasesDefault.fr,
	},
	it: {
		...phrases.it,
		...translationPhrases.it,
		...cloudinaryPhrasesDefault.it,
	},
};

export default translations;
