/* @flow */
import { spread } from '../../../modules/i18n/util';
import translationPhrases from '../assets/translationPhrases.json';

import * as ModulesLogin from '../../../modules/login/i18n';
import * as <PERSON><PERSON>les<PERSON>lia<PERSON> from '../../../modules/alias/i18n';
import * as ModulesEditorMenu from '../../../modules/editorMenu/i18n';
import * as ModulesEditor from '../../../modules/editor/i18n';
import * as ModulesSeo from '../../../modules/seo/i18n';
import * as ModulesEditorToolbar from '../../../modules/editor-toolbar/i18n';
import * as ModulesEditorDraft from '../../../modules/editorDraft/i18n';
import * as ModulesModals from '../../amagCore/modules/article-modals/i18n';
import * as PublishDialog from '../../amagCore/modules/editorMenuDialogs/PublishDialog/i18n';
import * as ArchiveDialog from '../../amagCore/modules/editorMenuDialogs/ArchiveDialog/i18n';
import * as ResetDialog from '../../amagCore/modules/editorMenuDialogs/ResetDialog/i18n';
import * as ModulesDynamicMenu from '../../amagCore/modules/dynamicMenu/i18n';
import * as ModulesArticleEditor from '../../amagCore/modules/ArticleEditor/i18n';
import * as ModulesTeam from '../../amagCore/modules/team/i18n';
import * as ModulesSupport from '../../amagCore/modules/Support/i18n';
import * as ModulesBrands from '../../amagCore/modules/brands/i18n';
import * as ModulesContact from '../../amagCore/modules/contact/i18n';
import * as ModulesGoogleMap from '../../amagCore/modules/googleMap/i18n';
import * as ModulesFooter from '../../amagCore/modules/footer/i18n';
import * as ModulesSSO from '../../amagCore/modules/sso/i18n';
import * as ContractBrands from '../../amagCore/modules/ContractBrands/i18n';

import * as BrandNavigationModule from '../modules/Navigation/BrandNavigation/i18n';
import * as BrandTagNavigationModule from '../modules/BrandTagNavigation/i18n';
import * as GarageFinderModalModule from '../modules/GarageFinderModal/i18n';
import * as HeaderModule from '../modules/Header/i18n';
import * as AdminArticleListView from '../views/Admin/ArticleList/i18n';
import * as Footer from '../modules/Footer/i18n';
import * as Navigation from '../modules/Navigation/i18n';
import * as PostGrid from '../modules/PostGrid/i18n';
import * as Offers from '../views/Offers/i18n';
import * as Services from '../views/Services/i18n';
import * as UsedCars from '../views/UsedCars/i18n';

import { cloudinaryPhrasesDefault } from '../../../modules/files/upload/cloudinaryPhrasesDefault';
import * as ArticleOverview from '../../amagCore/modules/admin/ArticleOverview/i18n';
import * as ArticleOverviewGenericPost from '../../amagCore/modules/admin/ArticleOverview/GenericPostContainer/i18n';
import * as ArticleOverviewControl from '../../amagCore/modules/admin/ArticleOverview/Control/i18n';

const phrases = spread(
	[
		ModulesEditorToolbar,
		ModulesLogin,
		ModulesAlias,
		ModulesEditorMenu,
		ModulesEditor,
		ModulesSeo,
		ModulesModals,
		ModulesEditorDraft,
		PublishDialog,
		ArchiveDialog,
		ResetDialog,
		ModulesDynamicMenu,
		ModulesArticleEditor,
		ModulesTeam,
		ModulesSupport,
		ModulesBrands,
		ModulesContact,
		ModulesFooter,
		ModulesGoogleMap,
		ModulesSSO,
		BrandNavigationModule,
		BrandTagNavigationModule,
		GarageFinderModalModule,
		HeaderModule,
		AdminArticleListView,
		Footer,
		Navigation,
		PostGrid,
		Offers,
		Services,
		UsedCars,
		ArticleOverview,
		ArticleOverviewGenericPost,
		ArticleOverviewControl,
		ContractBrands,
	],
);

const translations = {
	de: {
		...phrases.de,
		...translationPhrases.de,
		...cloudinaryPhrasesDefault.de,
	},
	fr: {
		...phrases.fr,
		...translationPhrases.fr,
		...cloudinaryPhrasesDefault.fr,
	},
	it: {
		...phrases.it,
		...translationPhrases.it,
		...cloudinaryPhrasesDefault.it,
	},
};

export default translations;
