/* @flow */
import React from 'react';
import { connect } from 'react-redux';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { withRouter } from 'react-router-dom';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import { localize, type LocalizeProps } from '../../../../modules/i18n';
import { close as closeAction } from '../../../../modules/modal/actions';
import { GarageFinder } from '../../../amagCore/modules/umbrellaPages/garage-finder';
import closeButton from '../../assets/img/icons/cancel-black.svg';
import { Modal } from '../modals/Modal';
import styles from './GarageFinderModal.css';
import { namespace } from './i18n';
import { GARAGE_FINDER_NAME } from './constants';
import { getUrlWithHttps } from '../../../../tango/link';

type Props = {
	className?: string,
};

type InternalProps = LocalizeProps & {
	source: string,
	location: Object,
	history: Object,
	close: () => void,
};

function GarageFinderModalComponent(props: Props & InternalProps) {
	const {
		className,
		t,
		close,
	} = props;

	const getLocationUrl = (location: Map<string, any>) => {
		const www = location.get('www');
		return www ? getUrlWithHttps(www) : undefined;
	};

	return (
		<Modal name={GARAGE_FINDER_NAME}>
			<div className={classNames(className, styles.container)}>
				<div className={styles.title}>{t('title')}</div>
				<button className={styles.closeButton} onClick={() => close()}>
					<img src={closeButton} alt="close" />
				</button>
				<div className={styles.garageFinderContainer}>
					<GarageFinder
						className={styles.garagefinder}
						locationTags={['amag']}
						linkLabel={t('goto')}
						customLocationFilter={loc => Boolean(loc.get('www'))}
						getLocationUrl={getLocationUrl}
					/>
				</div>
			</div>
		</Modal>
	);
}

function mapDispatchToProps(dispatch: Function) {
	return {
		close: () => dispatch(closeAction(GARAGE_FINDER_NAME)),
	};
}

const withHocs = combineHOCs([
	withRouter,
	connect(null, mapDispatchToProps),
	localize(namespace),
	withStyles(styles),
]);

export const GarageFinderModal = (withHocs(GarageFinderModalComponent): ReactComponent<Props>);
