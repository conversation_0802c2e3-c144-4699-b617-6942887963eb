/* @flow */
import React from 'react';
import { connect } from 'react-redux';
import classNames from 'classnames';
import { Map, List } from 'immutable';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { localize } from '../../../../../modules/i18n';
import type { LocalizeProps } from '../../../../../modules/i18n/types';
import { BrandLogo } from '../../../../amagCore/modules/brands/BrandLogo';
import styles from './brandNavigation.css';
import { namespace } from '../i18n';
import { close as closeAction } from '../../../../../modules/modal/actions';
import combineHOCs from '../../../../../tango/hoc/combineHOCs';
import { getDealerGroupLabel, getSelectedBrand, getDealerBrandLogos, getDealers } from '../../dealerGroup/selectors';
import { setSelectedBrand, clearSelectedBrand } from '../../dealerGroup/actions';
import closeButton from '../../../assets/img/icons/cancel-black.svg';
import { Modal } from '../../modals/Modal';
import { BRAND_TOGGLE } from '../constants';
import { GarageFinder } from '../../../../amagCore/modules/umbrellaPages/garage-finder';
import { getUrlWithHttps } from '../../../../../tango/link';
import { trackDealerSearchBrandSelection } from '../../tracking/tracking';

type Props = {
	className?: string,
};

type InternalProps = LocalizeProps & {
	dealerGroupLabel: string,
	dealers: List<Map<string, any>>,
	brandLogos: Map<string, any>,
	selectedBrand: string,
	setBrand: (brand: string) => void,
	clearBrand: () => void,
	close: () => void,
};

class BrandContainer extends React.PureComponent {
	props: Props & InternalProps;

	toggleBrand = (brand: string) => {
		const { setBrand, clearBrand, selectedBrand } = this.props;
		if (selectedBrand === brand) {
			clearBrand();
		} else {
			setBrand(brand);
			trackDealerSearchBrandSelection(brand);
		}
	};

	getLocationUrl = (location: Map<string, any>) => {
		const { dealers, locale, selectedBrand, brandLogos } = this.props;
		const dealer = dealers.find(d => d.getIn(['dealer', 'merged-location-details'], new List()).some(loc => loc.get('id') === location.get('id')));
		const www = location.get('www');
		const websiteUrl = www ? getUrlWithHttps(www) : undefined;

		if (dealer && selectedBrand) {
			const index = brandLogos.findIndex(l => l.get('key') === selectedBrand);
			const brandsiteUrl = brandLogos.getIn([index, 'urls', dealer.getIn(['dealer', 'dealer-label']), locale]);
			return brandsiteUrl ? getUrlWithHttps(brandsiteUrl) : websiteUrl;
		}

		return websiteUrl;
	};

	hasLocationUrl = (location: Map<string, any>) => {
		const url = this.getLocationUrl(location);
		return Boolean(url);
	};

	render = () => {
		const {
			brandLogos = new Map(),
			dealerGroupLabel,
			className,
			locale,
			t,
			selectedBrand,
			close,
		} = this.props;


		const activatedBrandLogos = brandLogos.filter(brand => brand.get('activated'));
		const brandFilter = selectedBrand ? [selectedBrand] : undefined;
		const isBrandSelected = Boolean(selectedBrand);

		return (
			<Modal
				name={BRAND_TOGGLE}
			>
				<div>
					<div className={classNames(styles.container, className)}>
						<div className={styles.title}>
							{
								activatedBrandLogos.size > 1 ?
									t('ourBrandPages') :
									t('ourBrandPage')
							}
						</div>
						<ul className={styles.brandNavigation}>
							{activatedBrandLogos
								.map((brandLogo) => {
									const brand = brandLogo.get('key');
									const name = brandLogo.get('name');
									const logo = brandLogo.getIn(['logos', locale]);
									const isServiceOnly = brandLogo.get('isServiceLogo', false);

									return (
										<li key={brand}>
											<button type="button" onClick={() => this.toggleBrand(brand)}>
												<BrandLogo
													brandKey={brand}
													name={name}
													image={logo}
													isServiceOnly={isServiceOnly}
													className={classNames({
														[styles.inactive]: selectedBrand !== brand && isBrandSelected,
													})}
												/>
											</button>
										</li>
									);
								},
							)}
						</ul>
						<button className={styles.closeBrands} onClick={() => close()}>
							<img src={closeButton} alt="closeNavigation" />
						</button>
						<div className={styles.garageFinderContainer}>
							<GarageFinder
								locationTags={[dealerGroupLabel]}
								linkLabel={t('gotosite')}
								salesBrandFilter={brandFilter}
								serviceBrandFilter={brandFilter}
								customLocationFilter={this.hasLocationUrl}
								getLocationUrl={this.getLocationUrl}
							/>
						</div>
					</div>
				</div>
			</Modal>
		);
	}
}

const mapStateToProps = (state: ReduxState) => ({
	dealerGroupLabel: getDealerGroupLabel(state),
	dealers: getDealers(state),
	brandLogos: getDealerBrandLogos(state),
	selectedBrand: getSelectedBrand(state),
});

const mapDispatchToProps = (dispatch: Function) => ({
	setBrand: (brand: string) => dispatch(setSelectedBrand(brand)),
	clearBrand: () => dispatch(clearSelectedBrand()),
	close: () => dispatch(closeAction(BRAND_TOGGLE)),
});

const withHocs = combineHOCs([
	localize(namespace),
	connect(mapStateToProps, mapDispatchToProps),
	withStyles(styles),
]);

export const BrandNavigation = (withHocs(BrandContainer): ReactComponent<Props>);
