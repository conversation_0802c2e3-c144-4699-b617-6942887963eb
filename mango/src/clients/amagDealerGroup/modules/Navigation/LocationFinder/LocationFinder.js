/* @flow */
import React from 'react';
import { connect } from 'react-redux';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import combineHOCs from '../../../../../tango/hoc/combineHOCs';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import closeButton from '../../../assets/img/icons/cancel-black.svg';
import { close as closeAction } from '../../../../../modules/modal/actions';
import { GarageFinder } from '../../../../amagCore/modules/umbrellaPages/garage-finder';
import { getDealerGroupLabel } from '../../dealerGroup/selectors';
import { LOCATION_TOGGLE } from '../constants';
import styles from './LocationFinder.css';
import { namespace } from '../i18n';
import { Modal } from '../../modals/Modal';
import { getUrlWithHttps } from '../../../../../tango/link';

type Props = {
	className?: string,
};

type InternalProps = LocalizeProps & {
	dealerGroupLabel: string,
	close: () => void,
};

function LocationFinderComponent(props: Props & InternalProps) {
	const {
		className,
		dealerGroupLabel,
		close,
		t,
	} = props;

	const getLocationUrl = (location: Map<string, any>) => {
		const www = location.get('www');
		return www ? getUrlWithHttps(www) : undefined;
	};

	return (
		<Modal
			name={LOCATION_TOGGLE}
		>
			<div className={classNames(styles.container, className)}>
				<div className={styles.title}>{t('ourLocations')}</div>
				<button className={styles.closeLocations} onClick={() => close()}>
					<img src={closeButton} alt="close" />
				</button>
				<div className={styles.garageFinderContainer}>
					<GarageFinder
						locationTags={[dealerGroupLabel]}
						linkLabel={t('gotosite')}
						customLocationFilter={loc => Boolean(loc.get('www'))}
						getLocationUrl={getLocationUrl}
					/>
				</div>
			</div>
		</Modal>
	);
}

function mapStateToProps(state: ReduxState) {
	return {
		dealerGroupLabel: getDealerGroupLabel(state),
	};
}

function mapDispatchToProps(dispatch: Function) {
	return {
		close: () => dispatch(closeAction(LOCATION_TOGGLE)),
	};
}

const withHocs = combineHOCs([
	connect(mapStateToProps, mapDispatchToProps),
	localize(namespace),
	withStyles(styles),
]);

export const LocationFinder = (withHocs(LocationFinderComponent): ReactComponent<Props>);
