/* @flow */
import React from 'react';
import { fromJS } from 'immutable';

import type { RouteProps } from '../../../../../tango/routing/types';
import type { ImmutableMenuEntries } from '../../../../../modules/dynamicMenu/types';
import createRoutesConfigForMenuEntries from '../../../modules/dynamicMenu/createRoutesConfigForMenuEntries';
import createCommonRoutes from '../../../../amagCore/modules/routing/createCommonRoutes';
import createDealerSitesRoutes from '../../../../amagCore/modules/routing/createDealerSitesRoutes';
import { CHANGELOG_ALIAS } from '../../../../amagCore/modules/alias/constants';
import AdminArticleList from './Admin/ArticleList';
import Home from './Home';
import Login from './Login';
import ArticleViewer from './ArticleViewer';
import ArticleEditor from './ArticleEditor';
import Offers from './Offers';
import Occassionen from './Occassionen';
import Contact from './Contact';
import News from './News';
import Support from './Support';
import Team from './Team';
import { AutoAbo } from './AutoAbo';
import { ContactForm } from './ContactForm';
import { ServiceAppointmentForm } from './ServiceAppointmentForm';
import TagFilterMenuEntry from './TagFilterMenuEntry';
import Error404 from '../../../views/Error404/Error404';

import StaticAliasConfig from './StaticAliasConfig';
import {
	LEGAL_NOTICE,
	DATA_PROTECTION,
} from '../../../../amagCore/modules/Placeholders';
import { AuthCallback } from '../../../../amagCore/modules/sso/AuthCallback';
import Loader from '../../../modules/Loader';

function createRouteConfigForRouteName(
	name: string,
	dealerLabel: string,
	dealerId: string,
) {
	switch (name) {
		case 'aboutus':
			return {
				noindex: true,
				render: ({ match }: RouteProps) => {
					const { locale } = match.params;
					const alias = `/${locale}/${dealerLabel}-uberuns`;
					return <ArticleViewer alias={alias} />;
				},
			};
		case 'contactform':
			return {
				component: ContactForm,
			};
		case 'serviceappointmentform':
			return {
				component: ServiceAppointmentForm,
			};
		case 'team':
			return { component: Team };
		case 'article':
			return {
				noindex: true,
				render: ({ match }: RouteProps) => (
					<ArticleViewer
						identity={[match.params.articleId, 'article']}
						dealerId={dealerId}
					/>
				),
			};
		case 'article.editor':
			return {
				noindex: true,
				render: ({ match }: RouteProps) => (
					<ArticleEditor articleId={match.params.articleId} />
				),
			};
		case 'legalNotice':
			return {
				render: ({ match }: RouteProps) => {
					const { locale } = match.params;
					const aliasName = 'legalNotice';
					const alias = `${dealerLabel}/${locale}/${aliasName}`;
					return (
						<ArticleViewer
							alias={alias}
							placeholder={fromJS(LEGAL_NOTICE[locale])}
						/>
					);
				},
			};
		case 'dataProtection':
			return {
				render: ({ match }: RouteProps) => {
					const { locale } = match.params;
					const aliasName = 'dataProtection';
					const alias = `${dealerLabel}/${locale}/${aliasName}`;
					return (
						<ArticleViewer
							alias={alias}
							placeholder={fromJS(DATA_PROTECTION[locale])}
						/>
					);
				},
			};
		case 'article.english.landing.page':
			return {
				noindex: true,
				render: () => {
					const alias = `${dealerLabel}/*/english`;
					return <ArticleViewer alias={alias} />;
				},
			};
		case 'admin.article':
			return {
				noindex: true,
				component: AdminArticleList,
			};
		case 'admin.changelog':
			return {
				noindex: true,
				render: () => <ArticleViewer alias={CHANGELOG_ALIAS} />,
			};
		case 'userManual':
			return {
				noindex: true,
				render: ({ match }: RouteProps) => {
					const { locale } = match.params;
					const aliasName = StaticAliasConfig.userManual[locale];
					const alias = `amag/${locale}/${aliasName}`;
					return <ArticleViewer alias={alias} />;
				},
			};
		case 'faq':
			return {
				noindex: true,
				render: ({ match }: RouteProps) => {
					const { locale } = match.params;
					const aliasName = StaticAliasConfig.faq[locale];
					const alias = `amag/${locale}/${aliasName}`;
					return <ArticleViewer alias={alias} />;
				},
			};
		case 'support':
			return {
				noindex: true,
				component: Support,
			};
		case 'login':
			return {
				noindex: true,
				component: Login,
			};
		case 'sso-callback':
			return {
				noindex: true,
				render: () => <AuthCallback loaderComponent={Loader} />,
			};
		case 'article.alias':
			return {
				noindex: true,
				render: ({ match }: RouteProps) => {
					const { locale, aliasName } = match.params;
					const alias = `${dealerLabel}/${locale}/${aliasName}`;
					return <ArticleViewer alias={alias} dealerId={dealerId} />;
				},
			};
		case 'article.alias.amag':
			return {
				noindex: true,
				render: ({ match }: RouteProps) => {
					const { locale, aliasName } = match.params;
					const alias = `amag/${locale}/${aliasName}`;
					return <ArticleViewer alias={alias} />;
				},
			};
		default:
			return {
				noindex: true,
				component: Error404,
			};
	}
}

// eslint-disable-next-line max-len
function createRoutingConfig(
	dealerLabel: string,
	menuEntries: ?ImmutableMenuEntries,
	dealerId?: string,
) {
	const dynamicRouting = createRoutesConfigForMenuEntries(menuEntries, key => (
		<TagFilterMenuEntry menuEntryKey={key} />
	));

	const basePath = '';
	const commonRoutes = [
		...createDealerSitesRoutes(basePath),
		...createCommonRoutes(basePath),
	];

	const routes = commonRoutes.map(({ name, path }) => ({
		name,
		path,
		...createRouteConfigForRouteName(name, dealerLabel, dealerId),
	}));

	return [
		{
			name: 'home',
			path: '/:locale',
			component: Home,
		},
		{
			name: 'offers',
			path: {
				de: '/:locale/angebote',
				fr: '/:locale/offres',
				it: '/:locale/offerte',
			},
			component: Offers,
		},
		{
			name: 'usedcars',
			path: {
				de: '/:locale/occasionen',
				fr: '/:locale/occasions',
				it: '/:locale/occasioni',
			},
			render: ({ match }: RouteProps) => {
				const { locale } = match.params;

				// custom route rule for GNG
				if (dealerId === '**********') {
					const alias = `gng/${locale}/gng-occasionen`;
					return <ArticleViewer alias={alias} />;
				}

				return <Occassionen />;
			},
		},
		{
			name: 'contact',
			path: {
				de: '/:locale/kontakt',
				fr: '/:locale/contact',
				it: '/:locale/contatto',
			},
			component: Contact,
		},
		{
			name: 'news',
			path: {
				de: '/:locale/news',
				fr: '/:locale/news',
				it: '/:locale/news',
			},
			component: News,
		},
		dealerId === 'CHE021' && {
			name: 'olympic-abo',
			path: {
				de: '/:locale/olympic-abo',
				fr: '/:locale/olympic-abo',
				it: '/:locale/olympic-abo',
			},
			component: AutoAbo,
		},
		...dynamicRouting,
		...routes,
		{
			name: 'error.not-found',
			noindex: true,
			component: Error404,
		},
	].filter(route => !!route);
}

export default createRoutingConfig;
