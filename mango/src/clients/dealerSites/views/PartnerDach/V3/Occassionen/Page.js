/* @flow */
import React from 'react';
import { connect } from 'react-redux';
import { Helmet } from 'react-helmet-async';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import {
	makeGetDealerWebsiteUsedCarsUrl,
	getDealerId,
} from '../../../../../amagCore/modules/DealerConfigProvider/selectors';
import {
	Autoscout24,
	isAutoscout24Url,
} from '../../../../../amagCore/modules/autoscout24';
import { localize } from '../../../../../../modules/i18n';
import CTAButtonLink from '../../../../modules/CTAButtonLink';
import type { LocalizeProps } from '../../../../../../modules/i18n/types';
import { namespace } from './i18n';
import Content from '../../../../modules/Content';

import s from './occassionen.css';
import IframeResponsive from './IframeResponsive';

type Props = {
	iframe: string,
	dealerId?: string,
} & LocalizeProps;

const iframeResizerOptions = {
	autoResize: true,
	checkOrigin: false,
	resizeFrom: 'parent',
	heightCalculationMethod: 'bodyOffset',
	iframeResizerEnable: true,
};

function Page({ iframe, dealerId, t }: Props) {
	const isAutoscout = isAutoscout24Url(iframe);
	const isAutolina = iframe && iframe.includes('autolina.ch');
	const isGNG = dealerId && dealerId === '**********';
	const isBifang = dealerId && dealerId === '**********';
	const pageTitle = isGNG ? t('usedCarsTitle') : t('title');

	if (isAutolina && isBifang) {
		return (
			<div>
				<Helmet title={pageTitle} />
				<div>
					<iframe
						style={{
							width: '100%',
							height: '800px',
							border: 'none',
							display: 'block',
						}}
						id="auto-iframe"
						src={iframe}
					/>
				</div>
				<CTAButtonLink />
			</div>
		);
	}

	return (
		<div>
			<Content>
				<Helmet title={pageTitle} />
				<div>
					{!isAutoscout && !isAutolina && (
						<IframeResponsive
							src={iframe}
							iframeResizerOptions={iframeResizerOptions}
							frameBorder={0}
							id={'occasionen-iframe'}
							iframeResizerEnable
							style={{
								width: '1px',
								minWidth: '100%',
								minHeight: 1000,
							}}
						/>
					)}
					{isAutoscout && <Autoscout24 url={iframe} />}
				</div>
				<CTAButtonLink />
			</Content>
		</div>
	);
}

function makeMapStateToProps() {
	const getDealerWebsiteUsedCarsUrl = makeGetDealerWebsiteUsedCarsUrl();
	return (state: State, props: LocalizeProps) => ({
		iframe: getDealerWebsiteUsedCarsUrl(state)(props.locale),
		dealerId: getDealerId(state),
	});
}

export default localize(namespace)(
	withStyles(s)(connect(makeMapStateToProps)(Page)),
);
