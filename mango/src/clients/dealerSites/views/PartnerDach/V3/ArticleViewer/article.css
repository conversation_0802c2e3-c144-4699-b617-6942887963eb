@import '../../../../assets/variables.css';
@import '../../../../assets/editorDraft/editor.css';

.articleView {
	@apply --max-width;
	padding-bottom: 40px;

	@media (--screen-mobile) {
		padding-bottom: 20px;
	}

	& h1, & h2, & h3, & h4 {
		margin: 10px 0;
		line-height: var(--h1-line-height);
	}

	& h1 {
		font-size: var(--h1-font-size);
		line-height: var(--h1-line-height);
		font-family: var(--font-head);
	}

	& h2 {
		font-size: var(--h2-font-size);
		line-height: var(--h2-line-height);
		font-family: var(--font-head);
	}
}

.backgroundImage {
	@apply --headerImage;
	@apply --max-width;
}

.content {
	width: 100%;
	float: none;
	margin: auto;

	@media (--screen-tablet) {
		width: 100%;
		padding-right: inherit;
	}

	@media (--screen-mobile) {
		width: 100%;
		padding-right: inherit;
	}
}

aside {
	background: var(--grey-soft);
	padding: 20px;
	box-sizing: border-box;
	overflow: hidden;
	margin: 0 auto 0 auto;
	float: right;
	width: 30%;

	@media (--screen-tablet) {
		width: 100%;
	}

	@media (--screen-mobile) {
		width: 100%;
	}
}

.tryAndBuy {
	height: 700px;
	width: 100%;
}
