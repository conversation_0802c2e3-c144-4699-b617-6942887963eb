/* @flow */
import React from 'react';
import { connect } from 'react-redux';
import { Helmet } from 'react-helmet-async';
import { Map } from 'immutable';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { ElementView } from '../../../../../../modules/elements';
import s from './article.css';
import { ResponsiveBackground } from '../../../../../../modules/files';
import * as imageSizes from '../../../responsiveSizes';
import PostMeta from '../../../../../amagCore/modules/article/PostMeta';
import { getI18n } from '../../../../../../modules/i18n/selectors'; // eslint-disable-line
import getEditorViewConfig from '../../../../modules/getEditorViewConfig';
import Content from '../../../../modules/Content';
import { ABOUT_US_ALIAS_REGEX } from '../../../../../amagCore/modules/alias/constants';
import Team from '../Team';

type Props = {
	post: Map<any, any>,
	alias?: string,
	dealerId?: string,
};

const config = {
	...getEditorViewConfig(),
	anchorScroll: { offset: -85 },
};

const DEALER_ID = '0000000404';
const TRY_AND_BUY_HTML_CONTENT =
	'<carify-vehicle-grid try_and_buy lang="de" />';

function Article({ post, alias, dealerId }: Props) {
	const article = post.get('article');
	const elements = post.get('elements');
	const image = article.get('image');

	return (
		<div className={s.articleView}>
			<Helmet title={article.get('title')}>
				<meta property="og:title" content={article.get('title')} />
				<meta
					property="og:image"
					content={article.getIn(['image', 'raw', 'secure_url'])}
				/>
				<meta property="og:description" content={article.get('leadText')} />
			</Helmet>
			<PostMeta published={post} />
			{image && (
				<ResponsiveBackground
					className={s.backgroundImage}
					image={image}
					sizes={imageSizes.articleViewSmall}
				/>
			)}
			<Content className={s.content}>
				<div className={s.article}>
					<h1>{article.get('title')}</h1>
					<h2>{article.get('leadText')}</h2>
					{elements.map(element => (
						<ElementView
							key={element.get('id')}
							type={element.get('type')}
							data={element.get('data')}
							config={config}
						/>
					))}

					{dealerId === DEALER_ID && alias.includes('try-and-buy') && (
						<React.Fragment>
							<Helmet>
								<script src="https://cdn.jsdelivr.net/gh/carify-me/carify-components@^4/dist/carify.min.js?dealer=8c63694e-3350-4095-ac75-f60445527420" />
							</Helmet>
							<div
								className={s.tryAndBuy}
								// eslint-disable-next-line react/no-danger
								dangerouslySetInnerHTML={{ __html: TRY_AND_BUY_HTML_CONTENT }}
							/>
						</React.Fragment>
					)}
				</div>
			</Content>
			{alias && ABOUT_US_ALIAS_REGEX.test(alias) && <Team />}
		</div>
	);
}

function mapStateToProps(state: State) {
	return {
		i18n: getI18n(state),
	};
}

export default connect(mapStateToProps)(withStyles(s)(Article));
