/* @flow */
import React from 'react';
import { Map } from 'immutable';

import type { Identity } from '../../../../../../modules/request/types';
import Article from './Article';
import {
	ArticleViewerFAC,
	type ChildProps,
} from '../../../../modules/ArticleViewer/ArticleViewerFAC';

type Props = {
	alias?: string,
	identity?: Identity,
	isTeamPage?: boolean,
	placeholder?: Map<string, any>,
	dealerId: string,
};

function ArticleViewer(props: Props) {
	const { alias, identity, isTeamPage, placeholder, dealerId } = props;

	return (
		<ArticleViewerFAC
			alias={alias}
			identity={identity}
			isTeamPage={isTeamPage}
			placeholder={placeholder}
		>
			{({ post }: ChildProps) => (
				<React.Fragment>
					<Article post={post} alias={alias} dealerId={dealerId} />
				</React.Fragment>
			)}
		</ArticleViewerFAC>
	);
}

export default ArticleViewer;
