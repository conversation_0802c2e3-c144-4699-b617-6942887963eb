/* @flow */
import React from 'react';
import { withRouter } from 'react-router-dom';
import classNames from 'classnames';
import { fromJS } from 'immutable';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { Helmet } from 'react-helmet-async';
import combineHOCs from '../../../../../tango/hoc/combineHOCs';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import { type RouteProps } from '../../../../../tango/routing/types';
import { searchToObject } from '../../../../../tango/routing';
import { getBrowserOrigin } from '../../../../../tango/location/getBrowserOrigin';
import { namespace } from '../../../../amagCore/modules/form/i18n';
import {
	TextInput,
	TextArea,
	FormRow,
	CheckboxInput,
} from '../../../../amagCore/modules/form/fields';
import MailForm, { type ChildProps } from '../../../../amagCore/modules/form/MailForm';
import { validate } from './validate';
import WithBrand from '../../../../amagCore/modules/brands/WithBrand';
import Content from '../../../modules/Content';
import { SecondaryNavigation } from '../../../modules/SecondaryNavigation';
import { ResponsiveBackground } from '../../../../../modules/files';
import { namespace as audinamespace } from './i18n';
import { Button, THEME_PRIMARY } from '../../buttons';
import { InnerHTML } from '../../../../../modules/inner-html/InnerHTML';
import { type ConnectedProps } from './AccessoriesRequestFormContainer';
import ExternalLink from '../../../../../modules/external-link';
import { externalUrls } from '../../../../amagCore/modules/constants';

import styles from './AccessoriesRequestForm.css';

type InternalProps = LocalizeProps & RouteProps & ConnectedProps;

/*
	This will match to the following mandrill template:
	accessoriesrequest-locale-env
	- locale = url locale
	- env = test | prod, from mandrill config in tenant config
*/
const MANDRILL_BASE_TEMPLATE_NAME = 'accessoriesrequest';

const imageSizes = {
	tiny: { w: 320 },
	mobile: { w: 640 },
	medium: { w: 640 },
	tablet: { w: 1024 },
	FHD: { W: 1440 },
	QHD: { w: 1440 },
	UHD: { w: 1440 },
};

const cover = fromJS({
	rawFormat: 'cloudinary/V1',
	raw: {
		public_id: 'amag/cover_audi',
	},
	transformations: [],
});

function handleClick(id, brand) {
	if (brand === 'audi') {
		// trackGoogleAnalyticConversion(id);
	}
}

function AccessoriesRequestFormComponent(props: InternalProps) {
	const { t, location, locale } = props;

	const origin = `${getBrowserOrigin()}${location.pathname}`;
	const queryParameter = searchToObject(location.search).p;

	/*
	 * This form will be sent to Mandrill.
	 * That's why all the field names need to be lowercase!
	 * Mandrill cannot handle camelCase.
	 */
	const initialValues = {
		product: queryParameter || '',
		firstname: '', // required
		lastname: '', // required
		email: '', // required
		phone: '', // required
		model: '',
		chassisnumber: '', // required
		message: '',
		origin,
		acceptedprivacypolicy: false, // required
	};

	return (
		<div>
			<Helmet title={t('title', undefined, audinamespace)} />
			<ResponsiveBackground
				image={cover}
				sizes={imageSizes}
				className={styles.image}
			>
				<h1>{t('title', undefined, audinamespace)}</h1>
			</ResponsiveBackground>
			<SecondaryNavigation showIcons />
			<Content>
				<WithBrand>
					{(brand: string) => (
						<MailForm
							initialValues={initialValues}
							validate={validate}
							baseTemplate={MANDRILL_BASE_TEMPLATE_NAME}
							templateLocale={locale}
							name="Zubehör Anfrage"
							recipientId="accessoriesRequest"
						>
							{(childProps: ChildProps) => {
								const {
									isSubmitting,
									submitted,
									hasSubmissionError,
									errors,
									isValid,
								} = childProps;

								if (submitted) {
									return (
										<div className={styles.formContainer}>
											<div className={styles.submissionSuccess}>
												{t('submissionSucceeded')}
											</div>
										</div>
									);
								}

								const formErrors = errors.form || [];

								return (
									<div className={styles.formContainer}>
										<FormRow className={styles.row}>
											<TextInput
												name="product"
												placeholder={t('product.placeholder')}
												label={t('product.label')}
												error={errors.product}
												required
												className={styles.input}
											/>
										</FormRow>
										<FormRow className={styles.row}>
											<TextInput
												name="firstname"
												placeholder={t('firstname.placeholder')}
												label={t('firstname.label')}
												error={errors.firstname}
												required
												className={styles.input}
											/>
											<TextInput
												name="lastname"
												placeholder={t('lastname.placeholder')}
												label={t('lastname.label')}
												error={errors.lastname}
												required
												className={styles.input}
											/>
										</FormRow>

										<FormRow className={styles.row}>
											<TextInput
												name="email"
												placeholder={t('email.placeholder')}
												label={`${t('email.label')} **`}
												error={errors.email}
												className={styles.input}
											/>
										</FormRow>
										<FormRow className={styles.row}>
											<TextInput
												name="phone"
												placeholder={t('phone.placeholder')}
												label={`${t('phone.label')} **`}
												error={errors.phone}
												className={styles.input}
											/>
										</FormRow>
										<InnerHTML className={styles.innerHtml}>{`** ${t(
											'phoneEmailRequired',
										)}`}</InnerHTML>
										<FormRow className={styles.row}>
											<TextInput
												name="model"
												placeholder={t('model.placeholder')}
												label={t('model.label')}
												error={errors.model}
												className={styles.input}
											/>
										</FormRow>
										<FormRow className={styles.row}>
											<TextInput
												name="chassisnumber"
												placeholder={t('chassisNumberAccessories.placeholder')}
												label={t('chassisNumberAccessories.label')}
												error={errors.chassisnumber}
												className={styles.input}
											/>
										</FormRow>

										<FormRow className={styles.row}>
											<TextArea
												name="message"
												label={t('comments.label')}
												error={errors.message}
												className={styles.textArea}
											/>
										</FormRow>
										<FormRow
											className={classNames(
												styles.row,
												styles.checkBoxRow,
												styles.single,
											)}
										>
											<CheckboxInput
												name="acceptedprivacypolicy"
												label={
													<span>
														{t('acceptedprivacypolicy.label1')}&nbsp;
														<ExternalLink
															href={externalUrls.audi.dataProtection[locale]}
															target="_blank"
														>
															{t('acceptedprivacypolicy.label2')}
														</ExternalLink>
													</span>
												}
												error={errors.acceptedprivacypolicy}
												required
												className={styles.checkbox}
											/>
										</FormRow>
										<FormRow>{t('obligatoryDataLegend')}</FormRow>
										{formErrors.length > 0 && (
											<ul className={styles.formErrors}>
												{formErrors.map((error, index) => (
													<li key={index}>
														<span
															className={classNames(
																styles.errorMessage,
																styles.formErrorMessage,
															)}
														>
															{t(error)}
														</span>
													</li>
												))}
											</ul>
										)}
										<div className={styles.center}>
											<Button
												type="submit"
												disabled={!isValid || isSubmitting}
												onClick={() => handleClick(brand)}
												theme={THEME_PRIMARY}
											>
												{t('submit')}
											</Button>
										</div>
										{hasSubmissionError && (
											<div
												className={classNames(
													styles.errorMessage,
													styles.submissionErrorMessage,
												)}
											>
												{t('submissionErrorMessage')}
											</div>
										)}
									</div>
								);
							}}
						</MailForm>
					)}
				</WithBrand>
			</Content>
		</div>
	);
}

const withHocs = combineHOCs([
	withRouter,
	localize(namespace),
	withStyles(styles),
]);

export const AccessoriesRequestForm = (withHocs(
	AccessoriesRequestFormComponent,
): ReactComponent<EmptyProps>);
