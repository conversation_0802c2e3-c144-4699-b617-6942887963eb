@import '../../assets/variables.css';
@import '../../modules/buttons/Button.css';

:root {
	--amagCoreInfoWindow: {
		clear: both;

		& p {
			color: var(--black);
		}

		& .mainButton {
			@apply --button;

			@apply --primary;
			background-color: var(--secondary-color) !important;
			border: solid 2px var(--secondary-color) !important;
			margin: 20px 0 0 0;

			&:hover {
				border: solid 2px var(--secondary-color-hover) !important;
				background-color: var(--secondary-color-hover) !important;
				color: var(--black) !important;
			}
		}
	}

	--amagCoreUmbrella: {
		& .map {
			position: relative;
			z-index: -1;
		}

		& .controls {
			flex-wrap: wrap;
			max-width: 100%;
			width: 100%;
			padding: 20px !important;

			@media (--screen-phablet) {
				display: block;
				margin: 0;
			}

			& .searchInputSection {
				border: none;
				padding: 0;
				z-index: 100
			}

			& .searchIcon {
				display: none;
			}

			& .searchInput {
				flex: 1;
				border-radius: 0;
				border: none;
				border-bottom: solid 1px #e4e4e4;
				background: url("../img/search.svg") no-repeat;
				background-position: 5% center;
				background-size: 16px;
				transition: all 0.3s ease;
				color: var(--black);
				position: relative;
				z-index: 65;

				@media (--screen-phablet) {
					width: 100%;
					max-width: 100%;
				}

				&::placeholder {
					color: var(--skoda-gray-100, #7c7d7e);
				}

				&:focus {
					border-bottom: solid 1px var(--primary-color);
				}
			}

			& .filtersToggle {
				border-radius: 0;
				border: none;
				border-bottom: solid 1px #e4e4e4;
				transition: all 0.3s ease;
				padding-right: 16px;
				padding-left: 6px;
				z-index: 35;

				@media (--screen-phablet) {
					max-width: 100%;
					width: 100%;
				}

				& img {
					display: none;
				}

				& label {
					color: var(--black);
				}

				&.active {
					border-bottom: solid 1px var(--primary-color);

					&:after {
						transform: translateY(-50%) rotate(180deg);
					}
				}
			}

			& .filtersContainer {
				box-shadow: none;
				border-radius: 4px;
				border: none;
				box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.15);
				padding-bottom: 0;
				padding-right: 16px;
				padding-left: 16px;
				z-index: 10;

				@media (--screen-phablet) {
					max-width: 100%;
					width: 100% !important;
				}

				&:first-of-type {
					z-index: 30;
				}

				&.filterIsOpen {
					padding-bottom: 0;
					border-radius: 4px;
					box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.15);
					border: none;
				}

				& span {
					color: var(--black);
				}

				& label {
					color: var(--skoda-gray-100, #7c7d7e);
				}
			}

			& .suggestions {
				border: none;
				box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.15);
				border-radius: 4px;
				z-index: 60;
				top: 100%;

				@media (--screen-phablet) {
					width: calc(100% - 40px)
				}

				&.suggestionsIsOpen {
					clear: both;
				}

				& ul {
					& span {
						display: block;
						border-bottom: solid 1px #e4e4e4;
						padding: 10px 20px;

						& label {
							color: var(--skoda-gray-100, #7c7d7e);
							margin: 0;
							padding: 0;
						}

						& hr {
							display: none;
						}
					}

					& li {
						color: var(--skoda-gray-100, #7c7d7e);
						font-size: var(--size-text-mobile);
						border-bottom: solid 1px #e4e4e4;
						padding: 20px;

						& button {
							margin: -20px;
							width: calc(100% + 40px);
							border: none;

							& div {
								margin: auto 0;

								& strong {
									margin: 0;
									padding: 0;
								}
							}
						}
					}
				}
			}

			& .filterSectionFront {
				z-index: 10;
			}

			& .filterSection {
				z-index: 0;
			}
		}
	}
}
