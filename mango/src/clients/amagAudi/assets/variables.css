@import '../../amagCore/assets/variables.css';
@import './fonts.css';

:root {
	--primary-color: #f50537; /* Bright Red */
	--primary-color-hovered: color(var(--primary-color) lightness(45%));
	--primary-color-hover: color(var(--primary-color) lightness(45%));
	--extra-color: #f50537;
	--light-gray: #e5e5e5;
	--super-gray: #f2f2f2;
	--dark-gray: #4a4a4a;
	--secondary-color: black;
	--secondary-color-hover: white;
	--tertiary-color: white;

	--text-dark-gray: #4c4c4c;
	--text-light-gray: #b3b3b3;

	--audi-gray: #e8eaeb;

	--success-color: var(--black);
	--error-color: #f50537;

	/* Audi brand colors */
	--audi-white: #fff;
	--audi-black: #000;
	--audi-aluminium-silver: #b3b3b3;
	--audi-red: #f50537;
	--audi-warm-silver: #b6b1a9;
}

@custom-media --screen-tiny (max-width: 320px);
@custom-media --screen-mobile (max-width: 640px);
@custom-media --screen-desktop-only (min-width: 640px);
@custom-media --screen-phablet (max-width: 800px);
@custom-media --screen-phablet-min (min-width: 800px);
@custom-media --screen-tablet print, (max-width: 1000px);
@custom-media --screen-tablet-big print, (max-width: 1024px);
@custom-media --screen-desktop print, (min-width: 1024px);
@custom-media --screen-middle-max (max-width: 1300px);
@custom-media --screen-middle-min (min-width: 1300px);
@custom-media --screen-big print, (min-width: 2000px);
@custom-media --screen-4k (min-width: 3870px);

:root {
	--font-regular: AudiType, sans-serif;
	--font-text: AudiType, sans-serif;
	--font-light: AudiTypeLight;
	--font-bold: AudiTypeBold;
	--font-head: AudiType, sans-serif;
	--font-special: AudiType, sans-serif;
	--font-icon: 'VW Icon Font';

	--smaller-font-size: 12px;
	--smaller-line-height: 1.4;

	--small-font-size: 14px;
	--small-line-height: 1.5;

	--default-font-size: 16px;
	--default-line-height: 1.8;

	--big-font-size: 48px;
	--big-line-height: 1.3;

	--bigger-font-size: 64px;
	--bigger-line-height: 1.2;

	--bigger-font-size-mobile: 38px;
	--bigger-line-height-mobile: 1.1;

	--h1-font-size: 44px;
	--h1-line-height: 1.4;

	--h1-font-size-mobile: 34px;
	--h1-line-height-mobile: 1.3;

	--h2-font-size: 36px;
	--h2-line-height: 1.4;

	--h2-font-size-mobile: 26px;
	--h2-line-height-mobile: 1.3;

	--h3-font-size: 24px;
	--h3-line-height: 1.8;

	--h3-font-size-mobile: 22px;
	--h3-line-height-mobile: 1.3;

	--h3-editor-font-size: 24px;
	--h3-editor-line-height: 1.8;

	--h4-font-size: 20px;
	--h4-line-height: 1.4;

	--h4-font-size-mobile: 19px;
	--h4-line-height-mobile: 1.3;

	--h4-editor-font-size: 20px;
	--h4-editor-line-height: 1.4;

	--h5-font-size: 19px;
	--h5-line-height: 1.4;

	--h5-font-size-mobile: 16px;
	--h5-line-height-mobile: 1.3;

	--h6-font-size: 16px;
	--h6-line-height: 1.4;

	--h6-font-size-mobile: 13px;
	--h6-line-height-mobile: 1.3;

	/* --> Text --------------------------------------------------- */
	--size-text-mobile: 14px;
	--lineheight-text-mobile: 1.8;
	--size-text-desktop: 16px;
	--lineheight-text-desktop: 1.9;

	--amagUmbrella-font-weight: 700;
	--amagUmbrella-strong-font-size: 16px;
	--amagUmbrella-strong-line-height: 1.4;
}

:root {
	--clearfix: {
		clear: both;

		&:before,
		&:after {
			content: '';
			display: table;
			clear: both;
		}
	}

	--noList: {
		box-sizing: border-box;
		list-style: none;
		margin: 0;
		padding: 0;

		& li {
			box-sizing: border-box;
			padding: 0;
			margin: 0;
		}
	}

	--resetButton: {
		appearance: none;
		border: none;
		background: var(--white);
		outline: none;
		margin: 0;
		padding: 0;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	--maxWidth: {
		box-sizing: border-box;
		max-width: 1300px;
		margin: auto;

		@media (--screen-middle-max) {
			padding: 0 20px;
		}
	}

	--fullWidth: {
		position: relative;
		left: 50%;
		transform: translateX(-50%);
		width: 100vw;
	}

	--container: {
		margin: 0 auto;
		max-width: 1024px;
		box-sizing: border-box;
	}

	--coverSize: {
		aspect-ratio: 2.39 / 1;
		max-height: 100vh;
		width: 100%;
		height: 30vw;
	}

	--centerBg: {
		background-size: cover !important;
		background-position: center center !important;
		background-repeat: no-repeat !important;
	}

	--centerLeftBg : {
		background-size: cover !important;
		background-position: left center !important;
		background-repeat: no-repeat !important;
	}

	--topCenterBg: {
		background-size: cover !important;
		background-position: center top !important;
		background-repeat: no-repeat !important;
	}

	--sliderWrapper: {
		@apply --maxWidth;
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		padding: 20px 100px;
		z-index: 2;

		& h4 {
			padding: 20px 0;
		}
	}

	--backgroundImage {
		@apply --centerBg;
		@apply --coverSize;

		position: relative;
		min-height: 300px;
	}
}
