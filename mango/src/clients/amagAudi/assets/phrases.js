/* @flow */
import { spread } from '../../../modules/i18n/util';
import translationPhrases from '../assets/translationPhrases.json';

import * as EditorModule from '../../../modules/editor/i18n';
import * as ModulesEditorToolbar from '../../../modules/editor-toolbar/i18n';
import * as EditorDraft from '../../../modules/editorDraft/i18n';

import * as ModulesModals from '../../amagCore/modules/article-modals/i18n';
import * as ModulesArticleEditor from '../../amagCore/modules/ArticleEditor/i18n';
import * as ModulesArticlePreview from '../../amagCore/modules/ArticlePreview/i18n';
import * as PublishDialog from '../../amagCore/modules/editorMenuDialogs/PublishDialog/i18n';
import * as ArchiveDialog from '../../amagCore/modules/editorMenuDialogs/ArchiveDialog/i18n';
import * as ResetDialog from '../../amagCore/modules/editorMenuDialogs/ResetDialog/i18n';
import * as ModulesDynamicMenu from '../../amagCore/modules/dynamicMenu/i18n';
import * as ModulesSupport from '../../amagCore/modules/Support/i18n';
import * as ModulesTeam from '../../amagCore/modules/team/i18n';
import * as ModulesContact from '../../amagCore/modules/contact/i18n';
import * as ModulesBrands from '../../amagCore/modules/brands/i18n';
import * as ModulesSSO from '../../amagCore/modules/sso/i18n';
import * as ModulesForm from '../../amagCore/modules/form/i18n';
import * as ModulesSeo from '../../../modules/seo/i18n';
import * as ModulesEditorMenu from '../../../modules/editorMenu/i18n';
import * as ModulesLogin from '../../../modules/login/i18n';

import * as Navigation from '../modules/Navigation/i18n';
import * as ServiceButtons from '../modules/ServiceButtons/i18n';
import * as Error404 from '../views/Error404/i18n';
import * as ServiceOffers from '../views/ServiceOffers/i18n';
import * as CurrentOffers from '../views/CurrentOffers/i18n';
import * as CurrentNews from '../views/CurrentNews/i18n';
import * as Order from '../views/Order/i18n';
import * as Service from '../views/Service/i18n';
import * as Accessories from '../views/Accessories/i18n';
import * as ContactForm from '../views/ContactForm/i18n';
import * as ServiceAppointmentForm from '../views/ServiceAppointmentForm/i18n';
import * as Subfooter from '../modules/Subfooter/i18n';
import * as Footer from '../modules/Footer/i18n';
import * as HeroVideo from '../modules/hero-video/i18n';
import * as Teaser from '../modules/teaser/i18n';
import { cloudinaryPhrasesDefault } from './translations/modules';
import * as ArticleOverview from '../../amagCore/modules/admin/ArticleOverview/i18n';
import * as ArticleOverviewGenericPost from '../../amagCore/modules/admin/ArticleOverview/GenericPostContainer/i18n';
import * as ArticleOverviewControl from '../../amagCore/modules/admin/ArticleOverview/Control/i18n';
import * as Team from '../views/Team/i18n';
import * as Posts from '../modules/posts/i18n';
import * as Buttons from '../modules/buttons/i18n';
import * as Navbar from '../modules/Navbar/i18n';
import * as SecondaryNavigation from '../modules/SecondaryNavigation/i18n';
import * as TestDrive from '../views/TestDrive/i18n';
import * as Homepage from '../views/Homepage/i18n';
import * as LocalUmbrellaPages from '../views/UmbrellaPage/i18n';
import * as UmbrellaPages from '../../amagCore/modules/umbrellaPages/i18n';
import * as GarageFinder from '../../amagCore/modules/umbrellaPages/garage-finder/i18n';
import * as ServiceDashboard from '../modules/service-dashboard/i18n';
import * as AccessoriesRequestForm from '../modules/Forms/accessories-request/i18n';
import * as Contacts from '../views/Contact/i18n';
import * as Impressum from '../views/Imprint/i18n';
import * as ImportedArticle from '../modules/imported-article/i18n';
import * as ModelBand from '../modules/ScrapedElements/ModelBand/i18n';
import * as AccessoriesRequestButton from '../modules/AccessoriesRequestButton/i18n';
import * as ShoppingWorldButton from '../modules/ShoppingWorldButton/i18n';
import * as CarModels from '../views/ArticleViewer/models/i18n';

const phrases = spread([
	ModulesEditorToolbar,
	Navigation,
	EditorModule,
	EditorDraft,
	Error404,
	ModulesSSO,
	ServiceOffers,
	CurrentOffers,
	CurrentNews,
	Order,
	Service,
	Accessories,
	ContactForm,
	ServiceAppointmentForm,
	Subfooter,
	Footer,
	ArticleOverview,
	ArticleOverviewGenericPost,
	ArticleOverviewControl,
	HeroVideo,
	Teaser,
	ModulesLogin,
	ModulesTeam,
	ModulesModals,
	ModulesArticleEditor,
	ModulesArticlePreview,
	PublishDialog,
	ArchiveDialog,
	ResetDialog,
	ModulesDynamicMenu,
	ModulesSupport,
	ModulesTeam,
	ModulesContact,
	ModulesBrands,
	ModulesForm,
	ModulesSeo,
	ModulesEditorMenu,
	Buttons,
	Posts,
	Team,
	Navbar,
	SecondaryNavigation,
	TestDrive,
	Homepage,
	LocalUmbrellaPages,
	UmbrellaPages,
	ServiceDashboard,
	AccessoriesRequestForm,
	Contacts,
	Impressum,
	CarModels,
	ImportedArticle,
	ModelBand,
	GarageFinder,
	ServiceButtons,
	AccessoriesRequestButton,
	ShoppingWorldButton,
]);

const translations = {
	de: {
		...phrases.de,
		...translationPhrases.de,
		...cloudinaryPhrasesDefault.de,
	},
	en: {
		...phrases.en,
		...cloudinaryPhrasesDefault.en,
	},
	fr: {
		...phrases.fr,
		...translationPhrases.fr,
		...cloudinaryPhrasesDefault.fr,
	},
	it: {
		...phrases.it,
		...translationPhrases.it,
		...cloudinaryPhrasesDefault.it,
	},
};

export default translations;
