/* @flow */
import React from 'react';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { List } from 'immutable';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import Content from '../../modules/Content';
import { WithPost } from '../../../amagCore/modules/post/WithPost';
import { CreateButton } from '../../modules/CreateButton';
import { SecondaryNavigation } from '../../modules/SecondaryNavigation';
import { HomepageSlider } from './HomepageSlider';
import { Innovations } from '../../modules/teaser';
import { HomepageTeasers } from './HomepageTeasers';
import { HomepageServiceOffers } from './HomepageServiceOffers';
import { HomepageCurrentOffers } from './HomepageCurrentOffers';
import styles from './HomeComponent.css';
import { IMPORT_MODELS_ALIAS } from '../../App/constants';
import { localize, type LocalizeProps } from '../../../../modules/i18n';
import { ModelBandContainer } from './ModelBandContainer';

type Props = {
	post: Map<string, any>,
	isServicePartnerOnly: boolean,
} & LocalizeProps;

function HomepageComponent(props: Props) {
	const { post, locale, isServicePartnerOnly } = props;
	const modelsAlias = `amag/${locale}/${IMPORT_MODELS_ALIAS}`;

	return (
		<React.Fragment>
			<SecondaryNavigation showIcons />
			<HomepageSlider
				header={post.get('header')}
				displayScraped={false /* !dealerCapabilities.isServicePartnerOnly */}
			/>
			{!isServicePartnerOnly && (
				<WithPost alias={modelsAlias}>
					{({ post: postModels }: ChildProps) => {
						const article =
							post && post.getIn(['managed', 'published'], new Map());
						if (!article) return null;

						const modelsData = postModels
							.getIn(
								['managed', 'published', 'article', 'current', 'carModels'],
								[],
							)
							.toJS();

						if (!modelsData && modelsData.length === 0) return null;

						return (
							<ModelBandContainer modelsData={modelsData} locale={locale} />
						);
					}}
				</WithPost>
			)}
			<Content>
				<Innovations />
				<HomepageTeasers offers={post.get('teasersOffers', new List())} />
				<HomepageCurrentOffers />
				<HomepageServiceOffers />
			</Content>
			<CreateButton naviTagValue="homepage" />
		</React.Fragment>
	);
}

const withHOCs = combineHOCs([localize(), withStyles(styles)])(
	HomepageComponent,
);

export { withHOCs as HomepageComponent };
