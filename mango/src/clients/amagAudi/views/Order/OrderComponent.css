@import '../../assets/variables.css';
@import '../../modules/buttons/Button.css';

.hidden {
	display: none;
}

.backgroundImage {
	@apply --backgroundImage;
}

.container {
	margin: 0 auto;

	& div {
		color: white;
		height: 175px;
		margin-bottom: 10px;
		font-size: 21px;
		letter-spacing: 1px;
		object-fit: cover;
		background-size: cover;
		background-repeat: no-repeat;
		background-position: center;
		font-weight: 600;
		font-size: 22px;

		& div {
			width: 100%;
			height: 100%;
			background-color: rgba(0, 0, 0, 0.3);
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	@media (--screen-desktop-only) {
		display: grid;
		grid-template-columns: 1fr 1fr;
		width: 85%;
		margin: 0 auto;
		grid-gap: 10px;

		& div {
			margin-bottom: 0;
			height: 200px;

			&:hover {
				& div {
					width: 100%;
					height: 100%;
					background-color: rgba(0, 0, 0, 0.5);
					transition: opacity .5s ease-in-out;
				}
			}
		}
	}

	@media (--screen-desktop) {
		& div {
			height: 250px;
		}
	}

	@media (--screen-middle-min) {
		& div {
			height: 300px;
		}
	}
}

.sliderWrapper {
	@apply --sliderWrapper;
}