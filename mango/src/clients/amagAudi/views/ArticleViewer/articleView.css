@import '../../assets/variables.css';
@import '../../assets/editorDraft/editor.css';
@import '../../modules/buttons/Button.css';

.article {
	background: transparent;
}

& .topSection {
	display: flex;
	align-items: center;
	justify-content: center;
	text-align: center;
}

.container {
	display: flex;
	width: 100%;
	gap: 30px;
	flex-direction: row;

	@media (max-width: 1000px) {
		flex-direction: column;
		gap: 0;
	}
}

.modelsContainer {
	@media (max-width: 1000px) {
		padding: 0px !important;
	}
}

.leftSection {
	width: 20%;

	@media (max-width: 1000px) {
		width: 100%;
	}
}

.rightSection {
	width: 80%;
	background-color: #f2f2f2;
	padding: 15px;

	& .resultsText {
		font-size: 12px;
		color: #333333;
	}

	@media (max-width: 1000px) {
		width: auto;
		padding: 25px;
	}
}

.filterContainer {
	display: flex;
	gap: 4px;
	flex-wrap: wrap;
	padding: 10px;

	@media (max-width: 1000px) {
		padding: 15px;
		gap: 8px;
	}
}

.mainFilterTile {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	border: none;
	background-color: transparent;
	text-align: left;
	font-size: 18px;
	padding: 10px;

	& img {
		align: right;
		transform: rotate(-90deg);
		transition: transform 0.3s ease-in-out;
	}

	@media (max-width: 1000px) {
		padding: 15px 25px;
		font-size: 11px;
		border-bottom: 3px solid transparent;

		&.active {
			border-bottom: 3px solid #000;
		}

		& img {
			display: none;
		}
	}
}

.deleteFilterBtn {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	border: none;
	background-color: transparent;
	text-align: left;
	font-size: 18px;
	padding: 10px;
}

.filterTile {
	padding: 8px 16px;
	background-color: white;
	border: 1px solid #d9d9d9;
	cursor: pointer;

	&.isFilterActive {
		background-color: #f2f2f2;
		border: 1px solid #f2f2f2;
	}

	&.disabled {
		cursor: not-allowed;
		background-color: #ffffff;
		border: 1px solid #ffffff;
		pointer-events: none;
		color: #b3b3b3;
	}

	@media (max-width: 1000px) {
		padding: 10px 20px;
		border-radius: 4px;
	}
}

.mainFilters {
	display: flex;
	gap: 5px;
	flex-direction: column;
	padding: 40px 0;

	@media (max-width: 1000px) {
		flex-direction: row;
		padding: 0;
		border-bottom: 1px solid #e5e5e5;
		overflow-x: auto;
		white-space: nowrap;
		gap: 0;
	}
}

.carContainer {
	display: flex;
	flex-wrap: wrap;
	flex-direction: row;
	gap: 20px;
	padding: 20px 0;

	@media (max-width: 1000px) {
		padding: 15px 0;
	}
}

.title {
	font-size: 12px;
	padding: 10px 10px 0px 10px;

	@media (max-width: 1000px) {
		padding: 15px 15px 0px 15px;
		font-weight: 600;
	}
}

.sliderWrapper {
	@apply --sliderWrapper;
}

.backgroundImage {
	@apply --backgroundImage;
}

button[x-apple-data-detectors] {
	color: var(--black) !important;
}

span[x-apple-data-detectors] {
	color: white !important;
}

.blackContent {
	& a, & button {
		&:first-child {
			@apply --primary;
			border-color: transparent;
		}
	}
}