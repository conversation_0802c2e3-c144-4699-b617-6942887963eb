/* @flow */
import React, { type Node } from 'react';
import { Map, List, fromJS } from 'immutable';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import classNames from 'classnames';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';

import combineHOCs from '../../../../tango/hoc/combineHOCs';
import { ElementView } from '../../../../modules/elements';
import type { PostElementsViewConfig } from '../../../../modules/elements/types';
import { ResponsiveBackground } from '../../../../modules/files';
import * as imageSizes from '../../modules/responsiveSizes';
import PostMeta from '../../../amagCore/modules/article/PostMeta';
import Content from '../../modules/Content';
import { H4 } from '../../modules/headings';
import { SecondaryNavigation } from '../../modules/SecondaryNavigation';
import {
	getDealerId,
	getDealerLabel,
} from '../../../amagCore/modules/DealerConfigProvider/selectors';
import { ServiceButtons } from '../../modules/ServiceButtons/ServiceButtons';
import {
	serviceIconLinks,
	accessoriesRequestLinks,
	shoppingWorldLinks,
} from './constants';
import { AccessoriesRequestButton } from '../../modules/AccessoriesRequestButton/AccessoriesRequestButton';
import { ShoppingWorldButton } from '../../modules/ShoppingWorldButton/ShoppingWorldButton';
import { HomepageSlideHeading } from '../../views/Homepage/HomepageSlideHeading';
import {
	DEFAULT_TITLE_COLOR,
	TITLE_COLOR_WHITE,
} from '../../../amagCore/modules/editorMenuDialogs/PublishDialog/types';

import styles from './articleView.css';

const config: PostElementsViewConfig = {
	anchorScroll: { offset: -81 },
	pluginConfigurations: {
		ctas: {
			availableStyles: ['link', 'secondary', 'linkButton'],
			defaultStyle: 'link',
		},
	},
};

type Props = {
	post: Map<any, any>,
	customClassKey?: string,
	subNav?: Node,
	identity: List<string>,
};

type InternalProps = {
	dealerId: string,
	dealerLabel: string,
};

function Article({
	post,
	identity,
	customClassKey,
	subNav,
	dealerId,
	dealerLabel,
	location,
}: Props & InternalProps) {
	const article = post.get('article', new Map());
	const elements = post.get('elements', new Map());
	const image = article.get('image');
	const customClass = customClassKey ? styles[customClassKey] : '';
	const title = article.get('title');
	const leadText = article.get('leadText');
	const showServiceIcons = serviceIconLinks.some(element =>
		location.pathname.includes(element),
	);
	const isLegalNotice = article.get('isLegalNotice', false);

	const accessoriesRequestButton = accessoriesRequestLinks.some(element =>
		location.pathname.includes(element),
	);

	const showShoppingWorldButton = shoppingWorldLinks.some(element =>
		location.pathname.includes(element),
	);

	const titleColor = article.getIn(
		['simple', 'titleColor'],
		DEFAULT_TITLE_COLOR,
	);

	const isWhite = titleColor === TITLE_COLOR_WHITE;

	return (
		<div className={classNames(styles.article, customClass)}>
			<PostMeta published={post} />
			<SecondaryNavigation showIcons />
			{subNav}
			<div className={styles.headContainer}>
				{image && (
					<ResponsiveBackground
						className={styles.backgroundImage}
						image={image}
						sizes={imageSizes.fullWidthImage}
					>
						<div
							className={classNames(styles.sliderWrapper, {
								[styles.blackContent]: !isWhite,
							})}
						>
							<HomepageSlideHeading white={isWhite}>
								{title}
							</HomepageSlideHeading>
							{!isLegalNotice && leadText && <H4 white={isWhite}>{leadText}</H4>}
						</div>
					</ResponsiveBackground>
				)}
			</div>
			<Content push>
				<div className={styles.topSection}>
					{showServiceIcons && <ServiceButtons />}
					{accessoriesRequestButton && <AccessoriesRequestButton />}
					{showShoppingWorldButton && <ShoppingWorldButton />}
				</div>
				{elements.map(element => (
					<ElementView
						key={element.get('id')}
						config={config}
						type={element.get('type')}
						data={element.get('data')}
						variables={article
							.getIn(['variables', dealerId], new Map())
							.concat(fromJS({ dealerLabel }))}
						identity={identity}
					/>
				))}
			</Content>
		</div>
	);
}

function mapStateToProps(state) {
	return {
		dealerId: getDealerId(state),
		dealerLabel: getDealerLabel(state),
	};
}

export default combineHOCs([
	withStyles(styles),
	connect(mapStateToProps),
	withRouter,
])(Article);
