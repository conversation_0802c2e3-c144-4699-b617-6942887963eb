/* @flow */
import React from 'react';
import { connect } from 'react-redux';
import { Map, List } from 'immutable';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import Content from '../../modules/Content';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import { localize, type LocalizeProps } from '../../../../modules/i18n';
import { namespace } from './i18n';
import { SecondaryNavigation } from '../../modules/SecondaryNavigation';
import { ElementRenderer } from '../../modules/imported-article/elements';
import { Grid } from '../../modules/LayoutComponents';
import { HeroSection } from '../../modules/hero-section';
import { audiChLayerUrl, localTitle } from '../ImportedArticle/utils';
import {
	getDealerHasNewRetailerLayout,
	getDealerId,
	getDealerIsAmagRetailer,
} from '../../../amagCore/modules/DealerConfigProvider/selectors';
import { Posts } from '../../modules/posts';

import styles from './AccessoriesComponent.css';

type Props = LocalizeProps & {
	article: Map<string, any>,
};

function getDisplayFilters(
	locale: string,
	hasNewRetailerLayout: boolean,
): ?Object {
	return {
		teaserEditorial: (data: Map<string, any>) => {
			const excluded = [
				'Zubehörkatalog.',
				'Servicekontakt Audi Partner.',
				"Catalogue d'accessoires",
				'Contact de service des partenaires Audi.',
				'Catalogo accessori.',
				'Contatti servizio partner Audi.',
				'Zubehörkatalog',
				'Catalogo accessori',
				'Catalogue d’accessoires',
			];

			let items = data.get('items', List());

			items = items.filter(item => !excluded.includes(item.get('title')));

			if (items.size > 0) {
				return data.set('items', items);
			}

			return false;
		},
		collage: (data: Map<string, any>) => {
			let ret = data;

			const index = data
				.get('media', List())
				.findIndex(item => item.get('type') === 'textblock');

			if (index >= 0) {
				const path = ['media', index, 'link', 'url'];
				const url = data.getIn(path, '');

				const transformed = url.split('audi.ch')[1];

				if (transformed) {
					ret = data.setIn(path, audiChLayerUrl(transformed, locale));
				}
			}

			return ret;
		},
		intro: (data: Map<string, any>) => {
			// as per #7510 the alias is to be overridden for accessories pages
			// this is done by setting the alias to `accessoriesrequestform` which will be handled by IconLink.js
			// Also change in `ImportedArticleContainer.js
			let accessoriesLink = 'accessoriesrequestform';

			if (hasNewRetailerLayout) {
				accessoriesLink = 'accessories.enquiries';
			}

			const iconlinks = data
				.get('iconLinks', List())
				.map(
					item =>
						item
							.setIn(['link', 'alias'], accessoriesLink)
							.setIn(
								['link', 'name'],
								localTitle('accessoriesRequest', locale),
						) /* HACK: this should be changed in the National Page (this is from scrapped data) */,
				);

			return data.set('iconLinks', iconlinks);
		},
	};
}

const strategyOverrides = {
	collage: () => 'external',
};

function AccessoriesComponent({
	article,
	hasNewRetailerLayout,
	locale,
}: Props) {
	return (
		<React.Fragment>
			<HeroSection media={article.get('header')} />

			<SecondaryNavigation showIcons />

			<Content push>
				<ElementRenderer
					elements={article.getIn(['columns', 0, 'elements'], List())}
					filters={getDisplayFilters(locale, hasNewRetailerLayout)}
					linkStrategies={strategyOverrides}
				/>

				<Grid numberOfItemsPerRow={3} className={styles.grid} >
					<Posts
						name="accessories-dashboard"
						dashboardName="originalparts"
						initialSize={3}
						hideLoadMore
						hideIfEmpty
					/>
				</Grid>
			</Content>
		</React.Fragment>
	);
}

const withHOCs = combineHOCs([
	localize(namespace),
	withStyles(styles),
	connect((state: ReduxState) => ({
		hasNewRetailerLayout: getDealerHasNewRetailerLayout(state),
		isAmagRetailer: getDealerIsAmagRetailer(state),
		dealerId: getDealerId(state),
	})),
])(AccessoriesComponent);

export { withHOCs as AccessoriesComponent };
