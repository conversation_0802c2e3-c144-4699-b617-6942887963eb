/* @flow */
import React from 'react';
import { Field } from 'formik';
import { fromJS } from 'immutable';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { ResponsiveBackground } from '../../../../modules/files';
import { localize, type LocalizeProps } from '../../../../modules/i18n';
import MailChimpForm, { type ChildProps } from '../../../../modules/mailchimp/MailChimpForm';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import { Button, THEME_GHOST } from '../buttons';
import { H2 } from '../headings';
import { namespace } from './i18n';
import styles from './NewsletterSubscription.css';
import shape from '../../assets/img/shape.svg';

const image = fromJS({
	rawFormat: 'cloudinary/V1',
	raw: {
		public_id: 'stadttheaterlangenthal/newsletterLow2',
	},
});

function NewsletterSubscriptionComponent({ t }: LocalizeProps) {
	return (
		<div className={styles.newsletter}>
			<ResponsiveBackground className="image" image={image}>
				<img src={shape} alt="just visual" className={styles.shape} />
				<div className={styles.content}>
					<H2>{t('title')}</H2>
					<p className={styles.newsletterText}>{t('text')}</p>
					<MailChimpForm initialValues={{ email: '' }}>
						{({ isSubmitting, submitted, hasSubmissionError }: ChildProps) => {
							if (submitted) {
								return (
									<div className={styles.successMessage}>
										{t('successMessage')}
									</div>
								);
							}
							return (
								<div>
									<Field
										type="email"
										name="email"
										required
										placeholder={t('placeholder')}
									/>
									<Button
										type="submit"
										theme={THEME_GHOST}
										small
										disabled={isSubmitting}
									>
										{t('subscribe')}
									</Button>
									{hasSubmissionError && (
										<div className={styles.errorMessage}>
											<span>{t('error')}</span>
										</div>
									)}
								</div>
							);
						}}
					</MailChimpForm>
				</div>
			</ResponsiveBackground>
		</div>
	);
}

const withHOCs = combineHOCs([withStyles(styles), localize(namespace)]);

const NewsletterSubscription = withHOCs(NewsletterSubscriptionComponent);

export { NewsletterSubscription };
