/* @flow */
import React from 'react';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { localize, type LocalizeProps } from '../../../../modules/i18n';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import { Link } from '../../../../tango/routing';
import MailTo from '../../../../modules/MailTo';
import { H5, H6 } from '../headings';
import styles from './Footer.css';
import { namespace } from './i18n';
import logo1 from '../../assets/img/z_Inst_Stadt_Langenthal.png';
import logo2 from '../../assets/img/z_Kanton.png';
import logo3 from '../../assets/img/z_Gemeinde.png';
import facebook from '../../assets/img/icons/facebook.svg';
import instagram from '../../assets/img/icons/instagram.svg';
import ExternalLink from '../../../../modules/external-link';
import {
	WithGoogleMapConfig,
	type GoogleMapConfig,
} from '../../../../modules/googleMaps/WithGoogleMapConfig';
import {
	COMMUNICATION_MARKETING_LEAD_INFO,
	INFO,
	TECHNICAL_LEAD_INFO,
} from './constants';

function Footer({ t, locale }: LocalizeProps) {
	return (
		<footer className={styles.footer}>
			<div className={styles.content}>
				<div className={styles.contacts}>
					<div className={styles.flexContainer}>
						<div className={styles.slot}>
							<div className={styles.bottomMargin}>
								<H6>Betriebsbüro, Sekretariat</H6>
								<p>
									Telefon <a href={INFO.tel.href}>{INFO.tel.value}</a>
								</p>
								<p>
									<MailTo recipient={INFO.email}>{INFO.email}</MailTo>
								</p>
							</div>
							<div className={styles.bottomMargin}>
								<H6>Co-Leitung Stadttheater / </H6>
								<H6>Technische Leitung</H6>
								<p>{TECHNICAL_LEAD_INFO.fullName}</p>
								<p>
									Telefon{' '}
									<a href={TECHNICAL_LEAD_INFO.tel.href}>
										{TECHNICAL_LEAD_INFO.tel.value}
									</a>
								</p>
								<p>
									<MailTo recipient={TECHNICAL_LEAD_INFO.email}>
										{TECHNICAL_LEAD_INFO.email}
									</MailTo>
								</p>
							</div>
							<div className={styles.bottomMargin}>
								<H6>
									Co-Leitung Stadttheater / Leitung Kommunikation und Marketing
								</H6>
								<p>{COMMUNICATION_MARKETING_LEAD_INFO.fullName}</p>
								<p>
									Telefon{' '}
									<a href={COMMUNICATION_MARKETING_LEAD_INFO.tel.href}>
										{COMMUNICATION_MARKETING_LEAD_INFO.tel.value}
									</a>
								</p>
								<p>
									<MailTo recipient={COMMUNICATION_MARKETING_LEAD_INFO.email}>
										{COMMUNICATION_MARKETING_LEAD_INFO.email}
									</MailTo>
								</p>
							</div>
							<H6>
								<Link
									to="our-house.dashboard.aboutUs.page"
									params={{
										naviTag: 'aboutUs',
										alias: 'team',
									}}
								>
									Weitere Kontakte
								</Link>
							</H6>
						</div>
						<div className={`${styles.slot}`}>
							<H6>Theaterkasse</H6>
							<p className={styles.bottomMargin}>
								Stadttheater Langenthal, Haupteingang
								<br />
								Telefon <a href="tel:+41629222666">062 922 26 66</a>
								<br />
								<MailTo recipient="<EMAIL>">
									<EMAIL>
								</MailTo>
								<br />
								Dienstag, Donnerstag, Freitag
								<br />
								17 bis 19 Uhr
							</p>
							<p className={styles.bottomMargin}>
								Einwohnerschalter Stadt Langenthal
								<br />
								Jurastrasse 21, 4900 Langenthal
								<br />
								Montag:
								<br />
								08 bis 12 Uhr | 14 bis 18 Uhr
								<br />
								Dienstag:
								<br />
								08 bis 12 Uhr
								<br />
								Mittwoch & Donnerstag:
								<br />
								08 bis 12 Uhr | 14 bis 17 Uhr
								<br />
								Freitag:
								<br />
								07 bis 14 Uhr
							</p>
						</div>
					</div>
				</div>
				<address>
					<H5>{t('address')}</H5>
					<div className={styles.mapContainer}>
						<WithGoogleMapConfig>
							{(googleMapConfig: GoogleMapConfig) => (
								<iframe
									src={`https://www.google.com/maps/embed/v1/place?key=${
										googleMapConfig.key
									}&q=Stadttheater+Langenthal&language=${locale}`}
									allowFullScreen
								/>
							)}
						</WithGoogleMapConfig>
					</div>
					<div className={styles.flexContainer}>
						<div className={styles.slot}>
							<p>
								Stadttheater Langenthal
								<br />
								Theatersträsschen 1 <br />
								4900 Langenthal <br />
								(Haupteingang Seite Jurastrasse)
							</p>
						</div>
						<div className={styles.slot}>
							<H6>Parkieren</H6>
							<p>
								Parkhaus Theatersträsschen oder Parkplatz Wuhrplatz
								<br />
								Veloparkplatz bei der Stadtverwaltung <p>Jurastrasse 21</p>
							</p>
						</div>
					</div>
				</address>
			</div>
			<div className={styles.footerFooter}>
				<div className={styles.content}>
					<div className={styles.logos}>
						<div className={styles.logoContainer}>
							<img src={logo1} alt="Stadt Langenthal" />
						</div>
						<div className={styles.logoContainer}>
							<img src={logo2} alt="Kulturförderung Oberaargau" />
						</div>
						<div className={styles.logoContainer}>
							<img src={logo3} alt="Kulturförderung Oberaargau" />
						</div>
					</div>
					<div className={styles.social}>
						<ExternalLink href="http://www.facebook.com/stadttheaterlangenthal">
							<img
								src={facebook}
								className={styles.facebook}
								alt="Folgen Sie uns auf Facebook"
							/>
						</ExternalLink>
						<ExternalLink href="https://www.instagram.com/stadttheaterlangenthal/">
							<img
								src={instagram}
								className={styles.facebook}
								alt="Folgen Sie uns auf Instagram"
							/>
						</ExternalLink>
					</div>
				</div>
			</div>
		</footer>
	);
}

const withHOCs = combineHOCs([withStyles(styles), localize(namespace)]);

export default (withHOCs(Footer): ReactComponent<EmptyProps>);
