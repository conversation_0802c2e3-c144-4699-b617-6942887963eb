@import '../../../assets/variables.css';

.inCalendarList {
	position: absolute !important;
	top: 0;
	right: 0;
	display: block;
	width: 100px;
	height: 100px;
	background-color: transparent !important;
	padding: 5px 0 0 !important;

	&:before {
		display: none;
	}

	& span {
		position: absolute !important;
		display: block !important;
		transform: rotate(0deg) !important;
		width: 100% !important;
		text-align: center !important;
		color: var(--white) !important;
	}

	& .date {
		font-size: 32px !important;
		top: 20px !important;
		line-height: 1 !important;
	}

	& .monthName {
		top: 55px !important;
		font-size: 12px !important;
		display: block !important;
	}

	& .dayName {
		top: 5px !important;
		font-size: 11px !important;
	}

	& .yearName {
		top: 75px !important;
		font-size: 14px !important;
	}
}

.tile {
	margin-bottom: 30px;
	overflow: hidden;

	& .leadText {
		color: var(--white);
		font-size: 16px;

		@media (--screen-tablet), (--screen-mobile) {
			display: none;
		}
	}

	& :global {
		& .image {
			width: 100%;
			background-size: cover;
			background-position: center;
			filter: grayscale(100%);
			transition: all 500ms ease-in-out;

			&:after {
				content: ' ';
				width: 100%;
				display: block;
				padding-bottom: 100%;
				background-color: rgba(0,0,0,0.5);
				transition: all 500ms ease-in-out;
			}
		}

		& .textContainer {
			background-color: rgba(0,0,0,0.5);
			bottom: 0;
			box-sizing: border-box;
			color: var(--white);
			left: 0;
			padding: 20px;
			padding-bottom: 40px;
			position: absolute;
			width: 100%;
			transition: all 500ms ease-in-out;
		}

		& .title {
			font-family: var(--serif-regular);
			font-size: var(--h2-font-size);
			line-height: var(--h2-line-height);
		}

		& .hiddenTextContainer {
			color: var(--white);
			left: 0;
			opacity: 0;
			position: absolute;
			text-align: center;
			top: 50%;
			transform: translateY(-80%) scale(1.25);
			transition: all 500ms ease-in-out;
			width: 100%;
			filter: blur(10px);

			& p {
				max-width: 75%;
				margin: 0 auto;
			}
		}
	}
}

.big {
	width: calc(50% - 15px);
	position: relative;
	margin: 0 15px 30px;

	&:nth-child(odd) {
		margin-left: 0;
	}

	& :global(.textContainer) {
		& button {
			display: none;
		}
	}

	&:nth-child(even) {
		margin-right: 0;
	}

	@media (--screen-mobile) {
		flex: 0 0 100% !important;
		margin: 0 0 15px !important;
	}

	@media (--screen-desktop), (--screen-tablet) {
		&:hover {
			& :global {
				& .textContainer {
					background-color: rgba(0,0,0,0);
					transition: all 750ms ease-in-out;
					opacity: 0;
				}

				& .image {
					filter: grayscale(0);
				}

				& .hiddenTextContainer {
					opacity: 1;
					filter: blur(0);
					transform: translateY(-50%) scale(1);
				}
			}
		}
	}
}

.small {
	width: calc(33.333% - 30px);
	position: relative;
	margin: 0 22px 44px 22px;

	&:nth-child(3n) {
		margin-right: 0;
	}

	&:nth-child(3n+1) {
		margin-left: 0;
	}

	& .leadText {
		display: none;
	}

	& :global(.textContainer) {
		& button {
			display: none;
		}
	}

	@media (--screen-tablet) {
		flex: 0 0 calc(50% - 15px) !important;
		position: relative;
		margin: 0 15px 30px !important;

		&:nth-child(odd) {
			margin-left: 0 !important;
		}

		&:nth-child(even) {
			margin-right: 0 !important;
		}
	}

	@media (--screen-mobile) {
		flex: 0 0 100% !important;
		margin: 0 0 15px !important;
	}

	&:hover {
		& :global {
			& .image {
				filter: grayscale(0);
				transform: scale(1.05);

				&:after {
					background-color: rgba(0,0,0,0);
				}
			}
		}
	}

	& :global {
		& .title {
			font-family: var(--serif-regular);
			font-size: 24px;
			line-height: var(--h3-line-height);
			margin-bottom: 12px;
			padding-right: 72px;
			line-break: normal;
			word-break: break-word;
		}
	}
}

.slider {
	position: relative;

	& :global {
		& .image {
			width: 100%;
			background-size: cover;
			background-position: center;
			filter: grayscale(0%);

			&:after {
				content: ' ';
				width: 100%;
				display: block;
				padding-bottom: 56.3%;

				@media (--screen-mobile) {
					padding-bottom: 100%;
				}
			}
		}

		& .textContainer {
			background-color: transparent;
			bottom: auto;
			top: 50%;
			left: 40px;
			transform: translateY(-50%);
			color: var(--white);
			max-width: 50%;

			@media (--screen-mobile) {
				max-width: 85%;
				left: 50%;
				transform: translate(-50%,-50%);

				& button {
					display: none;
				}
			}
		}

		& .title {
			font-size: var(--h1-font-size);
			line-height: var(--h1-line-height);

			@media (--screen-mobile) {
				font-size: var(--h1-font-size-mobile);
				line-height: var(--h1-line-height-mobile);
			}
		}

		& .hiddenTextContainer {
			display: none;
		}
	}
}
