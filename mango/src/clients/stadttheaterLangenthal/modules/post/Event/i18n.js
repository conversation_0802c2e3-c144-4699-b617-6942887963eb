const namespace = 'stadttheaterLangenthal.modules.post.event';
const phrases = {
	de: {
		registrationEnd: 'Registrierung endet',
		form: {
			required: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
			invalidDate: 'Bitte wählen Sie ein gültiges Datum',
		},
		leadText: 'Lead text',
		endDate: 'Ende',
		registrationStart: 'Registrierung von',
		name: 'Veranstaltung Name',
		registrationOpenFrom: 'Registrierung beginnt',
		seeAll: 'Alle anzeigen',
		organizer: 'Organisator',
		startDate: 'Datum',
		location: 'Ort',
		eventList: {
			noResults: 'Keine bevorstehende Events',
		},
		downloadCsv: 'Download',
		registrationOpenTo: 'Registrierung offen bis',
		placeholderHours: 'hh',
		placeholderMinutes: 'mm',
		occasions: {
			noOccasions: 'Noch keine Spieldaten erfasst',
			add: 'Add',
		},
		dayNames: {
			0: {
				short: 'Mo',
				long: 'Montag',
			},
			1: {
				short: 'Di',
				long: '<PERSON><PERSON><PERSON>',
			},
			2: {
				short: 'Mi',
				long: 'Mittwoch',
			},
			3: {
				short: 'Do',
				long: 'Don<PERSON>tag',
			},
			4: {
				short: 'Fr',
				long: 'Freitag',
			},
			5: {
				short: 'Sa',
				long: 'Samstag',
			},
			6: {
				short: 'So',
				long: 'Sonntag',
			},
		},
		monthNames: {
			0: {
				short: 'Jan',
				long: 'Januar',
			},
			1: {
				short: 'Feb',
				long: 'Februar',
			},
			2: {
				short: 'März',
				long: 'März',
			},
			3: {
				short: 'Apr',
				long: 'April',
			},
			4: {
				short: 'Mai',
				long: 'Mai',
			},
			5: {
				short: 'Juni',
				long: 'Juni',
			},
			6: {
				short: 'Juli',
				long: 'Juli',
			},
			7: {
				short: 'Aug',
				long: 'August',
			},
			8: {
				short: 'Sep',
				long: 'September',
			},
			9: {
				short: 'Okt',
				long: 'Oktober',
			},
			10: {
				short: 'Nov',
				long: 'November',
			},
			11: {
				short: 'Dez',
				long: 'Dezember',
			},
		},
		venue: 'Saal/Ort',
		category: {
			title: 'Kategorie',
			values: {
				musiktheater: 'Musiktheater',
				schauspiel: 'Schauspiel',
				tanz: 'Tanz',
				konzert: 'Konzert',
				kleinkunst: 'Kleinkunst',
				kindertheater: 'Kinder | Jugend',
				event: 'Event',
				currentCircusAt: 'Aktuelle Zirkuskunst',
				guest: 'Zu Gast',
			},
		},
		abo: {
			pageTitle: 'Abonnemente',
			title: 'Abonnemente',
			noResults: 'No shows available for this abo',
			values: {
				musik: 'Abo M Musik',
				schauspiel: 'Abo S Schauspiel',
				gat: 'GAT Generalabonnement Theater',
				wahlabo: 'Passepartout',
			},
			description: {
				musik:
					'Für 6 festgelegte Vorstellungen mit rund 20% Ermässigung auf Einzelpreise',
				schauspiel:
					'Für 6 festgelegte Vorstellungen mit rund 20% Ermässigung auf Einzelpreise',
				gat:
					'Für 36 festgelegte Vorstellungen mit rund 50% Ermässigung auf Einzelpreise',
				wahlabo:
					'Für 22 festgelegte Vorstellungen mit rund 40% Ermässigung auf Einzelpreise',
			},
			disclaimer: {
				musik:
					'(die einzelnen Vorstellungen werden noch erfasst mit entsprechendem Abo-Tag)',
				schauspiel:
					'(die einzelnen Vorstellungen werden noch erfasst mit entsprechendem Abo-Tag)',
				tanz:
					'(die einzelnen Vorstellungen werden noch erfasst mit entsprechendem Abo-Tag)',
				mix:
					'(die einzelnen Vorstellungen werden noch erfasst mit entsprechendem Abo-Tag)',
				kindertheater:
					'(die einzelnen Vorstellungen werden noch erfasst mit entsprechendem Abo-Tag)',
				wahlabo:
					'(die einzelnen Vorstellungen werden noch erfasst mit entsprechendem Abo-Tag)',
			},
			linkDescription: {
				musik: ' können Sie das Abo M kaufen.',
				schauspiel: ' können Sie das Abo S kaufen.',
				unterhaltung: ' können Sie das Abo U kaufen.',
				kindertheater: ' können Sie das Abo Kindertheater kaufen.',
				gat: ' können Sie das GAT kaufen.',
				wahlabo: ' können Sie den Passepartout kaufen.',
			},
			link: {
				musik:
					'https://www.ticketmaster.ch/event/abonnement-musik-m-tickets/5237',
				schauspiel:
					'https://www.ticketmaster.ch/event/abonnement-schauspiel-s-tickets/5239',
				unterhaltung:
					'https://www.ticketmaster.ch/event/abonnement-unterhaltung-u-tickets/5243',
				kindertheater:
					'https://www.ticketmaster.ch/event/abonnement-kindertheater-k-tickets/5363',
				gat:
					'https://www.ticketmaster.ch/event/general-abonnement-2019-tickets/5279',
				wahlabo:
					'https://www.ticketmaster.ch/event/abonnement-passpartout-p-tickets/5257',
			},
			linkLabel: 'Hier',
		},
		url: 'Booking link',
		book: 'Kaufen',
		publishMenu: 'Publizieren',
		archive: 'Löschen',
		archiveQuestion: 'Wollen Sie den Artikel löschen?',
	},
};

export { phrases, namespace };
