## AdminOverview

### ImportedArticles

We normalize all imported articles to a common format where

```
{
	article: {
		image,
		title,
		link,
	}
}
```

Every article with type importedArticle contains the importedArticle data in post.importedArticle[template].
The mappings below are always relative to this root.

We have the current importedArticle templates:

#### amag

##### volkswagenImportedArticle

* header.image => image
* title => (TransformImportedText => needs to be implemented) => title
* post => (amagCore/modules/alias/resolveRoute) => link 

#### amagAudi

This is not yet built as amagAudi itself is not yet built

#### amagDealerGroups

this uses the same code as dealerSites

#### amagSeat

##### seatImportedArticle

* header.image => image
* post => (amagCore/modules/importedArticles/getSeatImportedArticleDashboardTitle) => title
* post => (amagCore/modules/alias/resolveRoute) => route => link 


#### amagSkoda

##### skodaImportedArticle

* header.image => image
* header.name => title
* post => (amagCore/modules/alias/resolveRoute) => route => link 

#### dealerSites

This can contain posts from amag, seat and audi, but seems to handle seat posts differently...

```
const rootPath = post.getIn(['post', 'importedArticle', 'seatImportedArticle']) ? ['post', 'importedArticle', 'seatImportedArticle'] : ['post', 'importedArticle'];
	const image = post.getIn([...rootPath, 'header', 'image']) || fallbackImage;
	const title = post.getIn([...rootPath, 'header', 'title']);
```

Update: this has been removed so we always use ['post', 'importedArticle', 'seatImportedArticle']

...and dealerSites requies BrandDashLink Component to render the link...so we need to find a way to use custom linking components based on the app we're in...bleh...

So how is the core search handling this? => it uses yet another custom BrandLink component from the core...

Theres even more inconsistencies:
- either the amagCore search component is faulty, i.e. referencing fields that don't exist
or
- the pretest scrapped posts are corrupted, missing fields or showing values in different locations...

- Search has an complex BrandLink component but for imported posts this doesn't seem to yield any url? 
- does this mean for these imported.url is always used?

