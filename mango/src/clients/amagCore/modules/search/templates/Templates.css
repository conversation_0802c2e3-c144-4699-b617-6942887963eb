@import theme(amagCoreSearch/search);

.article {
	display: block;
	box-sizing: border-box;
	text-align: center;
	transition: all 0.5s ease;
	position: relative;
	overflow: hidden;
	height: auto;
	margin-bottom: 30px;
	background-color: transparent;
	box-shadow: 0 0 0 0 rgba(0,0,0,0);
	border-bottom-right-radius: 3px;
	border-bottom-left-radius: 3px;

	& div {
		width: 100% !important;
	}

	@media (--screen-desktop) {
		&:hover {
			color: var(--primary-color-hover);
			opacity: 1 !important;
			transform: scale(1.05);
			box-shadow: 0 20px 50px 0 rgba(0, 0, 0, .10);

			& > a {
				top: 20px;
			}
		}
	}

	& .postImage {
		background-size: cover !important;
		background-position: center;
		padding-top: 56%;
		overflow: hidden;
	}

	& .teamMemberImage {
		background-size: contain;
		background-position: center;
		background-repeat: no-repeat;
		padding-top: 56%;
		overflow: hidden;
	}

	& img {
		width: 100%;
	}
}

.simpleArticle {
	@apply --amag-core-search-simple-article;
}

.externalLinkArticle {
	color: black;
	@apply --amag-core-search-external-link-article;
}

.teamMemberArticle {
	@apply --amag-core-search-team-member-article;
}

.seatImportedArticle {
	@apply --amag-core-search-seat-imported-article;
}

.seatCatalog {
	@apply --amag-core-search-seat-catalog;
}

.seatNews {
	@apply --amag-core-search-seat-news;
}

.seatCarModel {
	@apply --amag-core-search-seat-car-model;
}

.skodaImportedArticle {
	@apply --amag-core-search-skoda-imported-article;
}

.audiImportedArticle {
	@apply --amag-core-search-audi-imported-article;
}

.volkswagenImportedArticle {
	& .defaultPostImage {
		padding-top: 56%;
		background-color: #0099da;
	}

	@apply --amag-core-search-volkswagen-imported-article;
}

.volkswagenCarModel {
	@apply --amag-core-search-volkswagen-car-model;
}

.skodaCarModel {
	@apply --amag-core-search-skoda-car-model;
}

.skodaCarFamily {
	@apply --amag-core-search-skoda-car-family;
}