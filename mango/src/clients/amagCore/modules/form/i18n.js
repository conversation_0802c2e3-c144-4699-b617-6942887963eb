const namespace = 'amagCore.modules.form';
const phrases = {
	de: {
		serviceservice: {
			label: 'Service',
		},
		lastname: {
			placeholder: '<PERSON><PERSON><PERSON>',
			label: 'Nachname',
		},
		zip: {
			placeholder: '1234',
			label: 'PLZ',
		},
		km: {
			placeholder: '3000',
			label: 'Kilometerstand',
		},
		street: {
			placeholder: 'Musterstrasse',
			label: 'Strasse',
		},
		additionalservices: 'Zusatzleistungen',
		message: {
			label: 'Was können wir für Sie tun?',
		},
		comments: {
			label: 'Bemerkungen',
		},
		housenumber: {
			placeholder: '123',
			label: 'Hausnummer',
		},
		model: {
			placeholder: 'XY',
			label: 'Modell',
		},
		city: {
			placeholder: 'Musterhausen',
			label: 'Ort',
		},
		submissionErrorMessage:
			'Leider konnte das Formular nicht abgeschickt werden. Bitte versuchen Sie es später nochmal.',
		errors: {
			required: 'Pflichtfeld',
			formErrors: 'Bitte alle Pflichtfelder ausfüllen',
			phoneOrEmailRequired:
				'Entweder die Telefonnumer oder die E-Mail-Adresse muss angegeben werden.',
			serviceRequired: 'Bitte geben Sie die gewünschte(n) Leistung(en) an.',
		},
		phoneEmailRequired:
			'Die Eingabe der Telefonnummer <strong>oder</strong> der E-Mail-Adresse ist zwingend notwendig.',
		numberplate: {
			placeholder: 'ZH 123 456',
			label: 'Kontrollschild',
		},
		time: {
			placeholder: '',
			label: 'Gewünschte Uhrzeit',
		},
		phone: {
			placeholder: '012 345 67 89',
			label: 'Telefon',
		},
		date: {
			placeholder: '',
			label: 'Gewünschtes Datum',
			firstText:
				'Bitte tragen Sie Ihren gewünschten Wochentag und die Uhrzeit ein.',
			secondText:
				'Gerne kontaktieren wir Sie anschliessend, um einen fixen Termin zu vereinbaren.',
		},
		acceptedprivacypolicy: {
			label1: 'Ich akzeptiere die',
			label2: 'Datenschutzbestimmungen',
		},
		submissionSucceeded:
			'Vielen Dank für Ihre Anfrage. Wir werden uns baldmöglichst bei Ihnen melden.',
		submit: 'Absenden',
		salutation: {
			male: 'Herr',
			female: 'Frau',
			label: 'Anrede',
		},
		timeCaption: 'Uhrzeit',
		replacementvehicleservice: {
			label: 'Ersatzwagenservice',
		},
		requiredField: 'Pflichtfeld',
		firstname: {
			placeholder: 'Marie',
			label: 'Vorname',
		},
		serviceother: {
			label: 'Andere',
		},
		servicereparation: {
			label: 'Reparatur',
		},
		email: {
			placeholder: '<EMAIL>',
			label: 'E-Mail',
		},
		getandfetchservice: {
			label: 'Hol und Bring Service',
		},
		year: {
			placeholder: '2015',
			label: 'Baujahr',
		},
		company: {
			placeholder: 'Muster AG',
			label: 'Firma',
		},
		servicetyrechange: {
			label: 'Reifenwechsel/Räderservice',
		},
		servicemfk: {
			label: 'Motorfahrzeugkontrolle',
		},
		services: 'Leistungen',
		chassisNumberService: {
			placeholder: 'WAUZZZ1K11A111111',
			label: 'Chassis-Nr.',
		},
		chassisNumberAccessories: {
			placeholder: 'WAUZZZ1K11A111111',
			label:
				'Fahrgestellnummer (Bei fahrzeugspezifischem Zubehör bitte angeben. Sie finden die Nummer im Fahrzeugausweis.)',
		},
		product: {
			placeholder: '',
			label: 'Gewünschter Artikel',
		},
		obligatoryDataLegend: '* Pflichtfeld',
	},
	fr: {
		serviceservice: {
			label: 'Entretien',
		},
		lastname: {
			label: 'Nom',
			placeholder: 'Exemple',
		},
		zip: {
			label: 'NPA',
			placeholder: '1234',
		},
		km: {
			label: 'Kilométrage',
			placeholder: '3000',
		},
		street: {
			label: 'Rue',
			placeholder: 'Rue Exemple',
		},
		additionalservices: 'Prestations complémentaires',
		message: {
			label: 'Que pouvons-nous faire pour vous?',
		},
		comments: {
			label: 'Remarques',
		},
		housenumber: {
			label: 'Numéro de maison',
			placeholder: '123',
		},
		model: {
			label: 'Modèle',
			placeholder: 'XY',
		},
		city: {
			label: 'Lieu',
			placeholder: 'Ville Exemple',
		},
		submissionErrorMessage:
			"Nous n'avons malheureusement pas pu envoyer le formulaire. Veuillez réessayer ultérieurement.",
		errors: {
			formErrors: 'Merci de remplir tous les champs obligatoires',
			phoneOrEmailRequired:
				"Il convient d'indiquer le numéro de téléphone ou l'adresse e-mail.",
			required: 'Champ obligatoire',
			serviceRequired: 'Veuillez indiquer la/(les) prestation(s) souhaitée(s).',
		},
		phoneEmailRequired:
			"La saisie du numéro de téléphone <strong>ou</strong> de l'adresse e-mail est obligatoire.",
		numberplate: {
			label: "Plaque d'immatriculation",
			placeholder: 'ZH 123 456',
		},
		time: {
			label: 'Heure souhaitée',
			placeholder: '',
		},
		phone: {
			label: 'Téléphone',
			placeholder: '012 345 67 89',
		},
		date: {
			label: 'Date souhaitée',
			placeholder: '',
			firstText:
				'Veuillez inscrire le jour de la semaine et l’heure souhaités.',
			secondText:
				'Nous vous contacterons ensuite volontiers pour convenir d’une date fixe.',
		},
		acceptedprivacypolicy: {
			label1: "J'accepte les",
			label2: 'dispositions sur la protection des données',
		},
		submissionSucceeded:
			'Nous vous remercions de votre demande. Nous vous contacterons très prochainement.',
		submit: 'Envoyer',
		salutation: {
			female: 'Madame',
			label: 'Civilité',
			male: 'Monsieur',
		},
		timeCaption: 'Heure',
		replacementvehicleservice: {
			label: 'Service de voiture de remplacement',
		},
		requiredField: 'Champ obligatoire',
		firstname: {
			label: 'Prénom',
			placeholder: 'Marie',
		},
		serviceother: {
			label: 'Autres',
		},
		servicereparation: {
			label: 'Réparation',
		},
		email: {
			label: 'E-mail',
			placeholder: '<EMAIL>',
		},
		getandfetchservice: {
			label: 'Service à domicile',
		},
		year: {
			label: 'Année de construction',
			placeholder: '2015',
		},
		company: {
			label: 'Entreprise',
			placeholder: 'Exemple SA',
		},
		servicetyrechange: {
			label: 'Changement des pneus/entretien des roues',
		},
		servicemfk: {
			label: 'Contrôle des véhicules à moteur',
		},
		services: 'Prestations',
		chassisNumberService: {
			placeholder: 'WAUZZZ1K11A111111',
			label: 'Numéro de châssis',
		},
		chassisNumberAccessories: {
			placeholder: 'WAUZZZ1K11A111111',
			label:
				"Numéro de châssis (Pour les accessoires spécifiques, veuillez vous saisir le numéro de votre certificat d'immatriculation.)",
		},
		product: {
			placeholder: '',
			label: 'Article désiré',
		},
		obligatoryDataLegend: '* Obligatoire',
	},
	it: {
		serviceservice: {
			label: 'Servizio',
		},
		lastname: {
			label: 'Cognome',
			placeholder: 'Mustermann',
		},
		zip: {
			label: 'NPA',
			placeholder: '1234',
		},
		km: {
			label: 'Chilometraggio',
			placeholder: '3000',
		},
		street: {
			label: 'Via',
			placeholder: 'Musterstrasse',
		},
		additionalservices: 'Prestazioni supplementari',
		message: {
			label: 'Cosa possiamo fare per lei?',
		},
		comments: {
			label: 'Osservazioni',
		},
		housenumber: {
			label: 'Numero civico',
			placeholder: '123',
		},
		model: {
			label: 'Modello',
			placeholder: 'XY',
		},
		city: {
			label: 'Località',
			placeholder: 'Musterhausen',
		},
		submissionErrorMessage:
			'Purtroppo non è stato possibile inviare il formulario. Si prega di riprovare più tardi.',
		errors: {
			formErrors: 'Si prega di compilare tutti i campi obbligatori',
			phoneOrEmailRequired:
				"Indicare il numero di telefono o l'indirizzo e-mail.",
			required: 'Campo obbligatorio',
			serviceRequired: 'Indicare la/le prestazione/i desiderata/e',
		},
		phoneEmailRequired:
			"È assolutamente necessario inserire il numero di telefono <strong>oppure</strong> l'indirizzo e-mail.",
		numberplate: {
			label: 'Targa',
			placeholder: 'ZH 123 456',
		},
		time: {
			label: 'Ora richiesta',
			placeholder: '',
		},
		phone: {
			label: 'Telefono',
			placeholder: '012 345 67 89',
		},
		date: {
			label: 'Data desiderata',
			placeholder: '',
			firstText: 'Inserire il giorno della settimana e l’ora desiderati.',
			secondText: 'Saremo lieti di contattarla per concordare una data fissa.',
		},
		acceptedprivacypolicy: {
			label1: 'Accetto le',
			label2: 'disposizioni in materia di protezione die dati',
		},
		submissionSucceeded:
			'La ringraziamo della sua richiesta. La contatteremo al più presto.',
		submit: 'Invia',
		salutation: {
			female: 'Signora',
			label: 'Appellativo',
			male: 'Signor',
		},
		timeCaption: 'Ora',
		replacementvehicleservice: {
			label: 'Servizio auto di cortesia',
		},
		requiredField: 'Campo obbligatorio',
		firstname: {
			label: 'Nome',
			placeholder: 'Marie',
		},
		serviceother: {
			label: 'Altro',
		},
		servicereparation: {
			label: 'Riparazione',
		},
		email: {
			label: 'E-mail',
			placeholder: '<EMAIL>',
		},
		getandfetchservice: {
			label: 'Servizio ritiro e consegna',
		},
		year: {
			label: 'Anno di costruzione',
			placeholder: '2015',
		},
		company: {
			label: 'Ditta',
			placeholder: 'Muster SA',
		},
		servicetyrechange: {
			label: 'Cambio pneumatici/servizio ruote',
		},
		servicemfk: {
			label: 'Collaudo tecnico',
		},
		services: 'Prestazioni',
		chassisNumberService: {
			placeholder: 'WAUZZZ1K11A111111',
			label: 'Numero di telaio',
		},
		chassisNumberAccessories: {
			placeholder: 'WAUZZZ1K11A111111',
			label:
				'Numero di telaio (Tale numero si trova nel documento di immatricolazione del veicolo. Per gli accessori specifici al veicolo - si prega di precisarli.)',
		},
		product: {
			placeholder: '',
			label: 'Articolo desiderato',
		},
		obligatoryDataLegend: '* Richiesto',
	},
};

export { phrases, namespace };
