/* @flow */
import React from 'react';
import { withRouter } from 'react-router-dom';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { connect } from 'react-redux';

import combineHOCs from '../../../../../tango/hoc/combineHOCs';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import { getBrowserOrigin } from '../../../../../tango/location/getBrowserOrigin';
import { Link, type RouteProps } from '../../../../../tango/routing';
import {
	trackServiceAppointmentFormSubmitted,
	trackServiceAppointmentFormSubmissionFailed,
	trackGoogleAnalyticConversion,
} from '../../tracking/tracking';
import { namespace } from '../i18n';
import {
	TextInput,
	TextArea,
	FormRow,
	CheckboxInput,
	DatePicker,
	TimePicker,
} from '../fields';
import MailForm, { type ChildProps } from '../MailForm';
import { validate } from './validate';
import WithBrand from '../../brands/WithBrand';
import { InnerHTML } from '../../../../../modules/inner-html/InnerHTML';
import { type ConnectedProps } from './ServiceAppointmentFormContainer';
import { getDealerId } from '../../DealerConfigProvider/selectors';
import ExternalLink from '../../../../../modules/external-link';
import { externalUrls } from '../../constants';

import styles from './ServiceAppointmentForm.css';

type InternalProps = LocalizeProps &
	RouteProps &
	ConnectedProps & {
		dealerId: string,
	};

const HIDE_PICKUP_SERVICE_DEALER_LIST = ['**********', '**********'];

/*
	This will match to the following mandrill template:
	serviceappointment-locale-env
	- locale = url locale
	- env = test | prod, from mandrill config in tenant config
*/
const MANDRILL_BASE_TEMPLATE_NAME = 'serviceappointment';

function handleClick(id, brand) {
	if (brand === 'audi') {
		trackGoogleAnalyticConversion(id);
	}
}

function ServiceAppointmentFormComponent(props: InternalProps) {
	const { t, location, locale, dealerId } = props;

	const origin = `${getBrowserOrigin()}${location.pathname}`;
	const googleConversionId = 'AW-834924361/fA5PCKqlkHsQyd6PjgM';

	const showPickupService = !HIDE_PICKUP_SERVICE_DEALER_LIST.includes(dealerId);

	/*
	 * This form will be sent to Mandrill.
	 * That's why all the field names need to be lowercase!
	 * Mandrill cannot handle camelCase.
	 */
	const initialValues = {
		firstname: '', // required
		lastname: '', // required
		email: '', // required
		phone: '', // required
		model: '', // required
		year: '',
		km: '',
		numberplate: '',
		chassisnumber: '',
		date: undefined,
		data_internal: undefined,
		time: undefined,
		time_internal: undefined,
		serviceservice: false,
		servicereparation: false,
		servicemfk: false,
		servicetyrechange: false,
		serviceother: false,
		getandfetchservice: false,
		replacementvehicleservice: false,
		message: '',
		origin,
		acceptedprivacypolicy: false, // required
	};

	return (
		<WithBrand>
			{(brand: string) => (
				<MailForm
					initialValues={initialValues}
					validate={validate}
					baseTemplate={MANDRILL_BASE_TEMPLATE_NAME}
					templateLocale={locale}
					name="Servicetermin"
					recipientId="serviceAppointment"
					onSubmitted={trackServiceAppointmentFormSubmitted}
					onSubmitFailed={trackServiceAppointmentFormSubmissionFailed}
				>
					{(childProps: ChildProps) => {
						const {
							isSubmitting,
							submitted,
							hasSubmissionError,
							errors,
							isValid,
						} = childProps;

						if (submitted) {
							return (
								<div className={styles.formContainer}>
									<div className={styles.submissionSuccess}>
										{t('submissionSucceeded')}
									</div>
								</div>
							);
						}

						const formErrors = errors.form || [];
						const dataProtectionUrl =
							externalUrls[brand] && externalUrls[brand].dataProtection[locale];

						return (
							<div className={styles.formContainer}>
								<FormRow className={styles.row}>
									<TextInput
										name="firstname"
										placeholder={t('firstname.placeholder')}
										label={t('firstname.label')}
										error={errors.firstname}
										required
										className={styles.input}
									/>
									<TextInput
										name="lastname"
										placeholder={t('lastname.placeholder')}
										label={t('lastname.label')}
										error={errors.lastname}
										required
										className={styles.input}
									/>
								</FormRow>

								<FormRow className={styles.row}>
									<TextInput
										name="email"
										placeholder={t('email.placeholder')}
										label={`${t('email.label')} **`}
										error={errors.email}
										className={styles.input}
									/>
								</FormRow>
								<FormRow className={styles.row}>
									<TextInput
										name="phone"
										placeholder={t('phone.placeholder')}
										label={`${t('phone.label')} **`}
										error={errors.phone}
										className={styles.input}
									/>
								</FormRow>
								<InnerHTML className={styles.innerHtml}>{`** ${t(
									'phoneEmailRequired',
								)}`}</InnerHTML>
								<FormRow className={styles.row}>
									<TextInput
										name="model"
										placeholder={t('model.placeholder')}
										label={t('model.label')}
										error={errors.model}
										required
										className={styles.input}
									/>
									<TextInput
										name="year"
										placeholder={t('year.placeholder')}
										label={t('year.label')}
										error={errors.year}
										type="number"
										className={styles.input}
									/>
								</FormRow>
								<FormRow className={styles.row}>
									<TextInput
										name="km"
										placeholder={t('km.placeholder')}
										label={t('km.label')}
										error={errors.km}
										type="number"
										className={styles.input}
									/>
									<TextInput
										name="numberplate"
										placeholder={t('numberplate.placeholder')}
										label={t('numberplate.label')}
										error={errors.numberplate}
										className={styles.input}
									/>
								</FormRow>
								<FormRow className={styles.row}>
									<TextInput
										name="chassisnumber"
										placeholder={t('chassisNumberService.placeholder')}
										label={t('chassisNumberService.label')}
										error={errors.chassisnumber}
										className={styles.input}
									/>
								</FormRow>
								<h3>{t('services')}&nbsp;*</h3>
								<FormRow className={classNames(styles.row, styles.checkBoxRow)}>
									<CheckboxInput
										name="serviceservice"
										label={t('serviceservice.label')}
										error={errors.serviceservice}
										className={styles.checkbox}
									/>
									<CheckboxInput
										name="servicereparation"
										label={t('servicereparation.label')}
										error={errors.servicereparation}
										className={styles.checkbox}
									/>
									<CheckboxInput
										name="servicemfk"
										label={t('servicemfk.label')}
										error={errors.servicemfk}
										className={styles.checkbox}
									/>
									<CheckboxInput
										name="servicetyrechange"
										label={t('servicetyrechange.label')}
										error={errors.servicetyrechange}
										className={styles.checkbox}
									/>
									<CheckboxInput
										name="serviceother"
										label={t('serviceother.label')}
										error={errors.serviceother}
										className={styles.checkbox}
									/>
								</FormRow>

								{brand !== 'stopgo' && (
									<React.Fragment>
										<h3>{t('additionalservices')}</h3>
										<FormRow
											className={classNames(styles.row, styles.checkBoxRow)}
										>
											{showPickupService && (
												<CheckboxInput
													name="getandfetchservice"
													label={t('getandfetchservice.label')}
													error={errors.getandfetchservice}
													className={styles.checkbox}
												/>
											)}
											<CheckboxInput
												name="replacementvehicleservice"
												label={t('replacementvehicleservice.label')}
												error={errors.replacementvehicleservice}
												className={styles.checkbox}
											/>
										</FormRow>
									</React.Fragment>
								)}

								<section className={styles.dateSection}>
									<label>
										<strong>
											{t('date.firstText')}
										</strong>
									</label>
									<br />
									<label>
										<strong>
											{t('date.secondText')}
										</strong>
									</label>
								</section>

								<FormRow className={styles.row}>
									<DatePicker
										name="date"
										placeholder={t('date.placeholder')}
										label={t('date.label')}
										error={errors.date}
										className={styles.date}
									/>
									<TimePicker
										name="time"
										placeholder={t('time.placeholder')}
										label={t('time.label')}
										error={errors.time}
										className={styles.time}
									/>
								</FormRow>

								<FormRow className={styles.row}>
									<TextArea
										name="message"
										label={t('message.label')}
										error={errors.message}
										className={styles.textArea}
									/>
								</FormRow>
								<FormRow
									className={classNames(
										styles.row,
										styles.checkBoxRow,
										styles.single,
									)}
								>
									<CheckboxInput
										name="acceptedprivacypolicy"
										label={
											<span>
												{t('acceptedprivacypolicy.label1')}&nbsp;
												{dataProtectionUrl ? (
													<ExternalLink
														href={dataProtectionUrl}
														target="_blank"
													>
														{t('acceptedprivacypolicy.label2')}
													</ExternalLink>
												) : (
													<Link to="dataProtection" target="_blank">
														{t('acceptedprivacypolicy.label2')}
													</Link>
												)}
											</span>
										}
										error={errors.acceptedprivacypolicy}
										required
										className={styles.checkbox}
									/>
								</FormRow>
								<FormRow>{t('obligatoryDataLegend')}</FormRow>
								{formErrors.length > 0 && (
									<ul className={styles.formErrors}>
										{formErrors.map((error, index) => (
											<li key={index}>
												<span
													className={classNames(
														styles.errorMessage,
														styles.formErrorMessage,
													)}
												>
													{t(error)}
												</span>
											</li>
										))}
									</ul>
								)}
								<div className={styles.center}>
									<button
										type="submit"
										disabled={!isValid || isSubmitting}
										onClick={() => handleClick(googleConversionId, brand)}
										className={styles.button}
									>
										{t('submit')}
									</button>
								</div>
								{hasSubmissionError && (
									<div
										className={classNames(
											styles.errorMessage,
											styles.submissionErrorMessage,
										)}
									>
										{t('submissionErrorMessage')}
									</div>
								)}
							</div>
						);
					}}
				</MailForm>
			)}
		</WithBrand>
	);
}

function mapStateToProps(state) {
	return {
		dealerId: getDealerId(state),
	};
}

const withHocs = combineHOCs([
	withRouter,
	localize(namespace),
	withStyles(styles),
	connect(mapStateToProps),
]);

export const ServiceAppointmentForm = (withHocs(
	ServiceAppointmentFormComponent,
): ReactComponent<EmptyProps>);
