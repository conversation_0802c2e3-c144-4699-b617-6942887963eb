/* @flow */
import { createSelector, defaultMemoize } from 'reselect';
import { Map, List, fromJS } from 'immutable';

import { prepareBrandLogos } from '../brands/utils';
import {
	prepareMergedLocations,
	sortLocationsByHeadQuarter,
	sortLocationsByAmountOfServicesRelevantForSorting,
	filterRelevantLocationsForBrand,
} from './prepareLocations';
import { filterDuplicatedLocations } from '../contact/filterDuplicatedLocations';
import { getConfigState } from '../../../../modules/config/selectors';
import { getBrand } from '../config/selectors';

import {
	NEW_RETAIL_PAGE_DEALERS_WHITELIST,
	NEW_RETAIL_PAGE_GROUPS_WHITELIST,
	OCCASION_PLUS_CONTRACTS,
} from '../constants';

const AOC_GROUP_ID = 'CHE003';
const ACC_GROUP_ID = 'CHE004';

const STATE_IDENTIFIER = 'dealerConfig';

function getState(state: ReduxState) {
	return state && state.get(STATE_IDENTIFIER, fromJS({}));
}

const getDealer = createSelector(
	getState,
	state => state && state.get('dealer', fromJS({})),
);

const getDealerBrandWebsites = createSelector(
	getState,
	state => state && state.get('dealer-brand-websites', fromJS({})),
);

const getDealerId = createSelector(
	getDealer,
	state => state.get('dealer-id'),
);

const getDealerGroupLabels = createSelector(
	getState,
	state => state.getIn(['dealer', 'dealer-group-labels']) || fromJS([]),
);

const getDealerGroupWhitelistedLabel = createSelector(
	getState,
	state => {
		const labels = state.getIn(['dealer', 'dealer-group-labels']) || fromJS([]);
		return labels.find(label =>
			NEW_RETAIL_PAGE_GROUPS_WHITELIST.includes(label),
		);
	},
);

const getDealerLabel = createSelector(
	getDealer,
	state => state.get('dealer-label'),
);

const getDealerIsAmagRetailer = createSelector(
	getDealer,
	state => state.get('amagretailer'),
);

const getDealerIsAmagAoc = createSelector(
	getDealer,
	state => state.getIn(['config', 'imported', 'group-id']) === AOC_GROUP_ID,
);

const getDealerIsAmagAcc = createSelector(
	getDealer,
	state => state.getIn(['config', 'imported', 'group-id']) === ACC_GROUP_ID,
);

const getDealerIsAmag = (state: ReduxState) =>
	state.getIn([STATE_IDENTIFIER, 'dealer', 'flags', 'amag'], false);

const getDealerContracts = createSelector(
	getDealer,
	state => {
		const contracts = state.get('contracts');
		if (!contracts.has('cupra')) {
			return contracts.set('cupra', contracts.get('seat'));
		}
		return contracts;
	},
);

const getPreparedDealerLocations = createSelector(
	getDealer,
	getBrand,
	(state, brand) => {
		const locations = state.get('merged-location-details', new List());
		const managedLocations = state.getIn(
			['config', 'managed', 'location-details'],
			new List(),
		);

		const allMergedLocations = prepareMergedLocations(
			locations,
			managedLocations,
		);

		const filterLocations =
			Object.entries(allMergedLocations).length > 0 ? allMergedLocations : [];

		const mergedLocations = filterLocations.map(location =>
			location.update('departments', departments => {
				const inactiveDepartmentKeys = departments
					.keySeq()
					.filter(depKey => !departments.getIn([depKey, 'active']));
				return inactiveDepartmentKeys.reduce(
					(actualDepartments, inactiveDepKey) =>
						actualDepartments.delete(inactiveDepKey),
					departments,
				);
			}),
		);

		if (!brand) {
			const defaultHeadquarter = state.getIn([
				'config',
				'managed',
				'headquarter',
				'default',
			]);
			return sortLocationsByHeadQuarter(mergedLocations, defaultHeadquarter);
		}

		return sortLocationsByAmountOfServicesRelevantForSorting(
			filterRelevantLocationsForBrand(mergedLocations, brand),
			brand,
		);
	},
);

const getDealerLocations = createSelector(
	getPreparedDealerLocations,
	dealerLocations => dealerLocations.filter(loc => loc.get('show-on-contact')),
);

const getDealerLocationsHaveCarAdvisor = createSelector(
	getDealerLocations,
	dealerLocations =>
		dealerLocations &&
		dealerLocations.some(location => Boolean(location.get('car-advisor-id'))),
);

const getDealerMapLocations = createSelector(
	getPreparedDealerLocations,
	dealerLocations =>
		filterDuplicatedLocations(
			dealerLocations.filter(loc => loc.get('show-on-map')),
		),
);

const getDealerMainLocation = createSelector(
	getDealerLocations,
	getDealerId,
	(dealerLocations, dealerId) => {
		switch (dealerId) {
			case 'CHE024':
				return (
					dealerLocations.find(
						location => location.get('id') === '0000000430',
						new Map(),
					) || new Map()
				);
			case 'CHE026':
				return (
					dealerLocations.find(
						location => location.get('id') === '0000000184',
						new Map(),
					) || new Map()
				);
			default:
				return Object.entries(dealerLocations).length === 0
					? null
					: dealerLocations.first();
		}
	},
);

const getDealerAddress = createSelector(
	getDealerMainLocation,
	state => state && state.getIn(['contact-info', 'address']),
);

const getDealerPhone = createSelector(
	getDealerMainLocation,
	state => state && state.getIn(['contact-info', 'phone']),
);

const getDealerFax = createSelector(
	getDealerMainLocation,
	state => state && state.getIn(['contact-info', 'fax']),
);

const getDealerEmail = createSelector(
	getDealerMainLocation,
	state => state && state.getIn(['contact-info', 'email']),
);

const getDealerWebsite = createSelector(
	getDealerMainLocation,
	state => state && state.getIn(['www']),
);

const getDealerMainCustomerNr = createSelector(
	getDealerMainLocation,
	state => state && state.getIn(['customer-nr']),
);

const DEALER_NAME_OVERRIDE_MAP = {
	CHE021: {
		cupra: "Garage Olympic Rte d'Aproz Sion SA",
	},
	CHE048: {
		audi: 'Küry Park Side AG',
		volkswagen: 'Küry Automobile AG',
		'volkswagen-nutzfahrzeuge': 'Küry Automobile AG',
	},
};

// eslint-disable-next-line no-unused-vars
const retrieveDealerName = (
	locale: Locale,
	dealer,
	isFooter?: boolean = false,
	brand?: string,
) => {
	const dealerNameOverride =
		DEALER_NAME_OVERRIDE_MAP[dealer.get('dealer-id')] &&
		DEALER_NAME_OVERRIDE_MAP[dealer.get('dealer-id')][brand];

	if (dealerNameOverride) {
		return dealerNameOverride;
	}

	const BASE_PATH = ['merged-location-details', 0, 'contact-info', 'address'];

	const isAmagRetailer = dealer.get('amagretailer');

	if (isFooter || !isAmagRetailer) {
		return `${dealer.getIn([...BASE_PATH, 'name1', locale], '') ||
			''}\n${dealer.getIn([...BASE_PATH, 'name2', locale], '') || ''}`;
	}

	return dealer.getIn([...BASE_PATH, 'name2', locale], '') || '';
};

const getDealerName = (
	state: ReduxState,
	locale: Locale,
	isFooter?: boolean,
	brand?: string,
) => retrieveDealerName(locale, getDealer(state), isFooter, brand);

const makeGetDealerName = () =>
	createSelector(
		getDealerMainLocation,
		getDealer,
		getBrand,
		(location, dealer, brand) =>
			defaultMemoize((locale: Locale, isFooter?: boolean) =>
				retrieveDealerName(locale, dealer, isFooter, brand),
			),
	);

const getDealerSocial = createSelector(
	getDealer,
	state =>
		state
			.getIn(['config', 'managed', 'social-networks'], new List())
			.filter(socialNetwork => socialNetwork.get('url')),
);

const getDealerLanguages = createSelector(
	getDealer,
	state =>
		state
			.getIn(['config', 'managed', 'languages'], new Map())
			.filter(language => language === true)
			.keySeq(),
);

const getHasDealerEnglishActivated = createSelector(
	getDealerLanguages,
	state => state.includes('en'),
);

const getDealerWebsiteConfig = createSelector(
	getState,
	state => state && state.getIn(['dealer-website', 'config']),
);

const getDealerWebsiteTemplate = createSelector(
	getDealerWebsiteConfig,
	state => state && state.get('template'),
);

const getDealerWebsiteFavicon = createSelector(
	getDealerWebsiteConfig,
	state =>
		fromJS({
			default: state.getIn(['favicon', 'default', 'raw', 'secure_url']),
			apple: state.getIn(['favicon', 'apple', 'raw', 'secure_url']),
		}),
);

const getDealerWebsiteLogo = createSelector(
	getDealerWebsiteConfig,
	state => state && state.get('header-logo'),
);

const getDealerWebsiteColors = createSelector(
	getDealerWebsiteConfig,
	state => state && state.get('colors'),
);

const getDealerWebsiteHasLivechat = createSelector(
	getDealerWebsiteConfig,
	state => state && state.get('has-livechat'),
);

const makeGetDealerWebsiteSiteTitle = () =>
	createSelector(
		getDealerWebsiteConfig,
		state =>
			defaultMemoize((locale: Locale) => state.getIn(['site-titles', locale])),
	);

const makeGetDealerWebsiteSiteDescription = () =>
	createSelector(
		getDealerWebsiteConfig,
		state =>
			defaultMemoize((locale: Locale) =>
				state.getIn(['site-descriptions', locale]),
			),
	);

const makeGetDealerWebsiteUsedCarsUrl = () =>
	createSelector(
		getDealerWebsiteConfig,
		state =>
			defaultMemoize((locale: Locale) =>
				state.getIn(['used-cars-urls', locale]),
			),
	);

const makeGetDealerWebsiteDamageReportUrl = () =>
	createSelector(
		getDealerWebsiteConfig,
		state =>
			defaultMemoize((locale: Locale) =>
				state.getIn(['damage-report', locale]),
			),
	);

const makeGetDealerWebsiteVisitAppointmentUrl = () =>
	createSelector(
		getDealerWebsiteConfig,
		state =>
			defaultMemoize(
				(locale: Locale) =>
					state && state.getIn(['visit-appointment-urls', locale], ''),
			),
	);

const getHasDealerWebsite = createSelector(
	getState,
	state => state && state.getIn(['dealer-website', 'activated']),
);

const getActiveDealerBrandWebsites = createSelector(
	getDealerBrandWebsites,
	state =>
		state
			.filter(brandWebsite => brandWebsite.get('activated'))
			.keySeq()
			.toList(),
);

const getDealerHasNewRetailerLayout = createSelector(
	getDealer,
	state => {
		const labels = state.get('dealer-group-labels') || fromJS([]);
		const dealerID = state.get('dealer-id');

		return (
			NEW_RETAIL_PAGE_DEALERS_WHITELIST.includes(dealerID) &&
			NEW_RETAIL_PAGE_GROUPS_WHITELIST.some(i => labels.includes(i))
		);
	},
);

const makeGetDealerIsRetail = () =>
	createSelector(
		getDealer,
		state =>
			defaultMemoize((brand: ?string) =>
				(brand ? state.getIn(['contracts', brand, 'retail']) : false),
			),
	);

const makeGetDealerIsServicePartner = () =>
	createSelector(
		getDealer,
		state =>
			defaultMemoize((brand: ?string) =>
				(brand ? state.getIn(['contracts', brand, 'service']) : false),
			),
	);

const makeGetDealerIsDWAPArtner = () =>
	createSelector(
		getDealer,
		state =>
			defaultMemoize((brand: ?string) =>
				state.getIn(['contracts', brand, 'used'], false),
			),
	);

const makeGetDealerIsAOPPartner = () => createSelector(
		getDealer,
		state => defaultMemoize((brand: ?string) => (brand && brand === 'audi' ? state.getIn(['contracts', brand, 'used'], false) : false)),
	);

const makeGetDealerBrandServices = () => createSelector(
		getDealer,
		state => defaultMemoize((brand: ?string) => (brand ? state.getIn(['config', 'imported', 'brands', brand, 'services'], new List()) : new List())),
	);

const makeGetDealerHasEMobility = () =>
	createSelector(
		getDealer,
		state =>
			defaultMemoize((brand: ?string) => {
				const services = brand
					? state.getIn(
							['config', 'imported', 'brands', brand, 'services'],
							new List(),
					)
					: new List();
				return services.includes('E_SALES');
			}),
	);

const makeGetDealerHasServiceOnly = () =>
	createSelector(
		getDealer,
		state =>
			defaultMemoize((brand: ?string) => {
				const services = brand
					? state.getIn(
							['config', 'imported', 'brands', brand, 'services'],
							new List(),
					)
					: new List();
				return services.includes('SERVICE_ONLY');
			}),
	);

const makeGetDealerBrandWebsiteConfig = () =>
	createSelector(
		getDealerBrandWebsites,
		state =>
			defaultMemoize((brand: ?string) =>
				(brand ? state.getIn([brand, 'config']) : undefined),
			),
	);

const makeGetDealerBrandWebsiteSiteDescription = () => {
	const getDealerBrandWebsiteConfig = makeGetDealerBrandWebsiteConfig();

	return createSelector(
		getState,
		state => getDealerBrandWebsiteConfig(state),
		(state, getConfigForBrand) =>
			defaultMemoize((brand: ?string, locale: Locale) => {
				if (!brand) {
					return '';
				}

				const config = getConfigForBrand(brand) || new Map();
				return config.getIn(['site-descriptions', locale], '');
			}),
	);
};

const makeGetDealerBrandWebsiteHasLivechat = () =>
	createSelector(
		getDealerBrandWebsites,
		state =>
			defaultMemoize((brand: ?string) =>
				(brand
					? state && state.getIn([brand, 'config', 'has-livechat'], false)
					: false),
			),
	);

const makeGetDealerHasOccasionPlus = () =>
	createSelector(
		getDealer,
		state =>
			defaultMemoize(brand => {
				const brandServices = state.getIn(
					['config', 'imported', 'brands', brand, 'services'],
					new List(),
				);

				return brand
					? OCCASION_PLUS_CONTRACTS.some(contract =>
							brandServices.includes(contract),
					)
					: false;
			}),
	);

const getDealerWebsiteBrandLogos = createSelector(
	getDealerWebsiteConfig,
	getDealerLabel,
	getDealerContracts,
	getDealerLanguages,
	getActiveDealerBrandWebsites,
	(
		config = new Map(),
		dealerLabel,
		contracts = new Map(),
		languages,
		activeBrands = new List(),
	) => {
		const brandLogos = config.get('brandLogos');
		return prepareBrandLogos(
			brandLogos,
			dealerLabel,
			contracts,
			languages,
			activeBrands.toJS(),
		);
	},
);

const getHasDealerWebsiteBrandLogos = createSelector(
	getDealerWebsiteBrandLogos,
	logos => logos && logos.some(logo => logo.get('activated')),
);

const getDealerIsCustomDomain = createSelector(
	getState,
	state => state && state.get('isCustomDomain'),
);

const getDealerIsUmbrellaPage = createSelector(
	getState,
	state => state && state.get('isUmbrellaPage'),
);

const getDealerHasCustomDomain = createSelector(
	getState,
	state => state && state.get('hasCustomDomain'),
);

const getDealersBasePath = createSelector(
	getDealerIsCustomDomain,
	getDealerLabel,
	(isCustomDomain, dealerLabel) => (isCustomDomain ? '' : `/${dealerLabel}`),
);

const getGoogle = createSelector(
	getConfigState,
	state => state.get('google', fromJS({})),
);

const getGoogleTagManager = createSelector(
	getGoogle,
	state => state.get('tagManager', fromJS({})),
);

// TODO: @IZ refactor and get this from tenantConfig => getThirdPartyConfigGoogle
const getGoogleTagManagerDataLayer = createSelector(
	getGoogleTagManager,
	getDealerGroupLabels,
	(tagManager, dealerGroupLabels) => {
		const dataLayer = tagManager.get('dataLayer', fromJS([]));
		return [...dataLayer.toJS(), { dealerGroupLabels }];
	},
);

export {
	getDealer as getDealerConfig,
	getDealerId,
	getDealerGroupLabels,
	getDealerGroupWhitelistedLabel,
	getDealerIsCustomDomain,
	getDealerHasCustomDomain,
	getDealersBasePath,
	getDealerName,
	makeGetDealerName,
	getDealerLabel,
	getDealerIsAmagRetailer,
	getDealerIsAmagAoc,
	getDealerIsAmagAcc,
	getDealerIsAmag,
	getDealerContracts,
	getDealerMainLocation,
	getDealerAddress,
	getDealerPhone,
	getDealerFax,
	getDealerEmail,
	getDealerWebsite,
	getDealerSocial,
	getDealerMainCustomerNr,
	getDealerLocations,
	getDealerMapLocations,
	getDealerLocationsHaveCarAdvisor,
	getDealerLanguages,
	getHasDealerEnglishActivated,
	getDealerBrandWebsites,
	getDealerWebsiteConfig,
	getDealerWebsiteTemplate,
	getDealerWebsiteFavicon,
	getDealerWebsiteLogo,
	getDealerWebsiteColors,
	getDealerWebsiteHasLivechat,
	getDealerWebsiteBrandLogos,
	getHasDealerWebsiteBrandLogos,
	makeGetDealerWebsiteSiteTitle,
	makeGetDealerWebsiteSiteDescription,
	makeGetDealerWebsiteUsedCarsUrl,
	makeGetDealerWebsiteDamageReportUrl,
	makeGetDealerWebsiteVisitAppointmentUrl,
	getHasDealerWebsite,
	getActiveDealerBrandWebsites,
	getDealerHasNewRetailerLayout,
	makeGetDealerIsRetail,
	makeGetDealerIsServicePartner,
	makeGetDealerIsDWAPArtner,
	makeGetDealerIsAOPPartner,
	makeGetDealerBrandServices,
	makeGetDealerHasEMobility,
	makeGetDealerHasServiceOnly,
	makeGetDealerBrandWebsiteConfig,
	makeGetDealerBrandWebsiteSiteDescription,
	makeGetDealerBrandWebsiteHasLivechat,
	getGoogleTagManagerDataLayer,
	getDealerIsUmbrellaPage,
	makeGetDealerHasOccasionPlus,
};
