/* @flow */
import {
	<PERSON>OL<PERSON>WAGEN_NUTZFAHR<PERSON>EUGE_BRAND,
	VOLKSWAGEN_BRAND,
	AUDI_BRAND,
	SEAT_BRAND,
	SKODA_BRAND,
	CUPRA_BRAND,
	STOPGO_BRAND,
} from '../../../middleware/constants';
import { onUnselect } from './utils';

// if a parent is unselected all of it's children are unselected AND hidden from view

const OCCASION_PLUS_CONTRACTS = ['OCCASION_PLUS', 'OCCASION_PLUS_AFS'];

export const VW_NF_FILTERS = [
	{
		label: 'sales',
		onUnselect,
		children: [
			{
				label: 'newCars',
				tags: ['VP', 'VAS', 'NEW_CV_COMMERCIAL', 'NEW_CV_PASSENGER'],
				children: [
					{
						label: 'tour',
						tags: ['CALIFORNIA'],
					},
				],
			},
			{ label: 'usedCars', tags: ['USED_CV'] },
			{ label: 'fleetCenter', tags: ['FLEET_CENTER'] },
			{ label: 'fleetSpecialist', tags: ['FLEET_SPECIALIST'] },
			{ label: 'occasionPlus', tags: OCCASION_PLUS_CONTRACTS },
		],
	},
	{
		label: 'service',
		onUnselect,
		tags: ['SERVICE'],
		children: [
			{ label: 'collisionRepair', tags: ['COLLISION_REPAIR'] },
			{ label: 'gasService', tags: ['GAS_SERVICE'] },
			{ label: 'totalRepair', tags: ['TOTAL_REPAIR'] },
		],
	},
];

export const VW_FILTERS = [
	{
		label: 'sales',
		children: [
			{ label: 'newCars', tags: ['VP', 'VAS', 'NEW_PC'] },
			{ label: 'usedCars', tags: ['USED_PC'] },
			{ label: 'eSales', tags: ['E_SALES'] },
			{ label: 'hybrid', tags: ['HYBRID'] },
			{ label: 'fleetCenter', tags: ['FLEET_CENTER'] },
			{ label: 'fleetSpecialist', tags: ['FLEET_SPECIALIST'] },
			{ label: 'occasionPlus', tags: OCCASION_PLUS_CONTRACTS },
		],
	},
	{
		label: 'service',
		tags: ['SERVICE'],
		children: [
			{ label: 'collisionRepair', tags: ['COLLISION_REPAIR'] },
			{ label: 'eService', tags: ['E_SERVICE'] },
			{ label: 'gasService', tags: ['GAS_SERVICE'] },
			{ label: 'totalRepair', tags: ['TOTAL_REPAIR'] },
		],
	},
];

export const SEAT_FILTERS = [
	{
		label: 'sales',
		tags: ['VP', 'VAS', 'NEW_PC', 'USED_PC'],
		children: [
			{ label: 'newCars', tags: ['VP', 'VAS', 'NEW_PC'] },
			{ label: 'usedCars', tags: ['USED_PC'] },
			{ label: 'fleetCenter', tags: ['FLEET_CENTER'] },
			{ label: 'fleetSpecialist', tags: ['FLEET_SPECIALIST'] },
			{ label: 'occasionPlus', tags: OCCASION_PLUS_CONTRACTS },
		],
	},
	{
		label: 'service',
		tags: ['SERVICE'],
		children: [
			{ label: 'totalRepair', tags: ['TOTAL_REPAIR'] },
		],
	},
];

export const SKODA_FILTERS = [
	{
		label: 'sales',
		tags: ['VP', 'VAS', 'NEW_PC', 'USED_PC'],
		children: [
			{ label: 'fleetCenter', tags: ['FLEET_CENTER'] },
			{ label: 'fleetSpecialist', tags: ['FLEET_SPECIALIST'] },
			{ label: 'occasionPlus', tags: OCCASION_PLUS_CONTRACTS },
		],
	},
	{
		label: 'service',
		tags: ['SERVICE'],
		children: [
			{ label: 'totalRepair', tags: ['TOTAL_REPAIR'] },
		],
	},
];

export const AUDI_FILTERS = [
	{
		label: 'sales',
		onUnselect,
		children: [
			{ label: 'newCars', tags: ['VP', 'VAS', 'NEW_PC'] },
			{ label: 'usedCars', tags: ['USED_PC'] },
			{ label: 'aop', tags: ['AOP'] },
			{ label: 'fleetCenter', tags: ['FLEET_CENTER'] },
			{ label: 'fleetSpecialist', tags: ['FLEET_SPECIALIST'] },
		],
	},
	{
		label: 'service',
		tags: ['SERVICE'],
		children: [
			{ label: 'totalRepair', tags: ['TOTAL_REPAIR'] },
		],
	},
];

export const CUPRA_FILTERS = [
	{
		label: 'sales',
		tags: ['NEW_PC'],
		children: [
			{ label: 'fleetCenter', tags: ['FLEET_CENTER'] },
			{ label: 'fleetSpecialist', tags: ['FLEET_SPECIALIST'] },
			{ label: 'cupraApproved', tags: OCCASION_PLUS_CONTRACTS },
		],
	},
	{
		label: 'specialist',
		tags: ['CUPRA_SPECIALIST'],
		children: [
			{ label: 'totalRepair', tags: ['TOTAL_REPAIR'] },
		],
	},
];

export const STOPGO_FILTERS = [
	{
		label: 'sales',
		children: [
			{ label: 'newCars', tags: ['VP', 'VAS', 'NEW_PC'] },
			{ label: 'usedCars', tags: ['USED_PC'] },
			{ label: 'eSales', tags: ['E_SALES'] },
			{ label: 'hybrid', tags: ['HYBRID'] },
			{ label: 'fleetCenter', tags: ['FLEET_CENTER'] },
			{ label: 'fleetSpecialist', tags: ['FLEET_SPECIALIST'] },
			{ label: 'occasionPlus', tags: OCCASION_PLUS_CONTRACTS },
		],
	},
	{
		label: 'service',
		tags: ['SERVICE'],
		children: [
			{ label: 'collisionRepair', tags: ['COLLISION_REPAIR'] },
			{ label: 'eService', tags: ['E_SERVICE'] },
			{ label: 'gasService', tags: ['GAS_SERVICE'] },
			{ label: 'totalRepair', tags: ['TOTAL_REPAIR'] },
		],
	},
];

export const getFilter = (brand: ?string = ''): Array<Object> => {
	switch (brand) {
		case VOLKSWAGEN_NUTZFAHRZEUGE_BRAND:
			return VW_NF_FILTERS;
		case VOLKSWAGEN_BRAND:
			return VW_FILTERS;
		case AUDI_BRAND:
			return AUDI_FILTERS;
		case SEAT_BRAND:
			return SEAT_FILTERS;
		case SKODA_BRAND:
			return SKODA_FILTERS;
		case CUPRA_BRAND:
			return CUPRA_FILTERS;
		case STOPGO_BRAND:
			return STOPGO_FILTERS;
		default:
			return [];
	}
};

export const links = {
	de: {
		appointmentUrl: 'https://www.amag.ch/de/service/otv.html',
	},
	fr: {
		appointmentUrl: 'https://www.amag.ch/fr/services/otv.html',
	},
	it: {
		appointmentUrl: 'https://www.amag.ch/it/servizi/otv.html',
	},
};

export const PROXIMITY_SEARCH_SETTINGS = {
	LIMIT: 5,
};

export const ORIGIN_PIN = 'ORIGIN_PIN';

// the higher the further in it will zoom
export const MAX_ZOOM = 16;

export const GEOLOCATE_USER = false;
