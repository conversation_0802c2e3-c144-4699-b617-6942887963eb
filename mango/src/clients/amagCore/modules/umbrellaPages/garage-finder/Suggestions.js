/* @flow */
import React from 'react';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { List, Map } from 'immutable';

import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import combineHOCs from '../../../../../tango/hoc/combineHOCs';
import { namespace } from '../garage-finder/i18n';
import styles from './Suggestions.css';
import { Suggestion } from './Suggestion';
import Pin from '../svg/Pin';

type Props = {
	className?: string,
	foundLocations: List<Map<string, any>>,
	selectedLocation: ?string,
	onLocationSelected: (id: ?string) => void,
	cities?: any, // ToDo: fix this type
};

type InternalProps = {
	brand: string,
};

function SuggestionsComponent(props: Props & LocalizeProps & InternalProps) {
	const {
		className,
		foundLocations,
		selectedLocation,
		onLocationSelected,
		t,
		cities,
	} = props;

	if (foundLocations.size === 0) {
		return (
			<ul className={classNames(className, styles.suggestions)}>
				<li className={styles.noResults}>{t('noResults')}</li>
			</ul>
		);
	}

	return (
		<span className={classNames(className, styles.suggestions)}>
			<ul>
				{cities.length > 0 && (
					<span>
						<label className={styles.location}>Standort</label>
						<hr />
					</span>
				)}
				{cities.map(city => (
					<li
						key={city.id}
						className={classNames(styles.result, {
							[styles.selected]: city.id === selectedLocation,
						})}
					>
						<button
							type="button"
							onClick={() => onLocationSelected(city.name)}
							className={styles.button}
						>
							<div className={styles.image}>
								<Pin />
							</div>
							<div className={styles.innerButton}>
								<strong>{city.name}</strong>
							</div>
						</button>
					</li>
				))}
				{foundLocations.map(location => (
					<Suggestion
						key={location.get('id')}
						location={location}
						selectedLocation={selectedLocation}
						onLocationSelected={onLocationSelected}
					/>
				))}
			</ul>
		</span>
	);
}

const withHocs = combineHOCs([localize(namespace), withStyles(styles)]);

export const Suggestions = (withHocs(
	SuggestionsComponent,
): ReactComponent<Props>);
