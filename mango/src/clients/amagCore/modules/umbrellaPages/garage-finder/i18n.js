/* @flow */
const namespace = 'amagCore.modules.umbrellaPages.garage-finder';
const phrases = {
	de: {
		sale: 'Verkauf',
		service: 'Service',
		specialist: 'Specialist',
		placeholder: 'PLZ, Ort oder Name',
		noResults: 'Keine Händler gefunden.',
		noAmagHereYet: 'Hier finden Sie die nächstgelegenen Garagen für Ihre Suche:',
		distance: 'Distanz:',
		appointmentLabel: 'Servicetermin',
		stopGoAppointmentLabel: 'Terminanfrage',
		testDriveLabel: 'Probefahrt anfragen',
		gotoDefault: 'Zur Webseite',
		closed: 'geschlossen',
	},
	fr: {
		sale: 'Vente',
		service: 'Service',
		specialist: 'Spécialiste',
		placeholder: 'Code postal, lieu ou nom',
		noResults: 'Aucune marchand n\'a été trouvé.',
		noAmagHereYet: 'Vous trouverez ici les garages les plus proches pour votre recherche:',
		distance: 'distance:',
		appointmentLabel: 'Prendre des rendez-vous',
		stopGoAppointmentLabel: 'Demande de rendez-vous',
		testDriveLabel: 'Essai',
		gotoDefault: 'Vers le site web',
		closed: 'fermé',
	},
	it: {
		sale: 'Vendita',
		service: 'Servizio',
		specialist: 'Specialista',
		placeholder: 'Codice postale, località o nome',
		noResults: 'Nessun concessionario trovato.',
		noAmagHereYet: 'Qui trovate i garage più vicini per la vostra ricerca:',
		distance: 'Distanza:',
		appointmentLabel: 'Fissare appuntamenti',
		stopGoAppointmentLabel: 'Richiesta di appuntamento',
		testDriveLabel: 'Giro di prova',
		gotoDefault: 'Al sito',
		closed: 'chiuso',
	},
};

export {
	phrases,
	namespace,
};
