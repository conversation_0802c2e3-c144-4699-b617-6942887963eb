/* @flow */
// From
// https://tools.wmflabs.org/geohack/geohack.php?pagename=Geographical_centre_of_Switzerland&params=46_48_4_N_8_13_36_E_type:landmark_region:CH-OW_source:dewiki
export const DEFAULT_CENTER = { lat: 46.9, lng: 8.5 };
export const DEFAULT_ZOOM = 9;
export const DEFAULT_BOUNDS = {
	ne: { lat: 47, lng: 10 },
	nw: { lat: 47, lng: 7 },
	se: { lat: 46, lng: 10 },
	sw: { lat: 46, lng: 7 },
};

export const DEFAULTS = {
	center: DEFAULT_CENTER,
	zoom: DEFAULT_ZOOM,
	bounds: DEFAULT_BOUNDS,
};

export const AMAGStyles = [
	{
		featureType: 'all',
		elementType: 'all',
		stylers: [{ hue: '#3b3d3f' }, { lightness: 10 }, { visibility: 'simplified' }],
	},
	{
		featureType: 'water',
		elementType: 'geometry',
		stylers: [{ color: '#DBEBFA' }],
	},
	{
		featureType: 'road.highway',
		elementType: 'geometry.fill',
		stylers: [{ color: '#BCD0EA' }],
	},
	{
		featureType: 'road.highway',
		elementType: 'geometry.stroke',
		stylers: [{ color: '#9CB4D0' }],
	},
	{
		featureType: 'poi',
		elementType: 'labels',
		stylers: [{ visibility: 'off' }],
	},
	{
		featureType: 'landscape',
		elementType: 'labels',
		stylers: [{ visibility: 'off' }],
	},
	{
		featureType: 'transit',
		elementType: 'labels',
		stylers: [{ visibility: 'off' }],
	},
];

export const LAT_ROOT_PATH = ['contact-info', 'address', 'geo-location', 'lat'];
export const LNG_ROOT_PATH = ['contact-info', 'address', 'geo-location', 'lng'];

export const DEFAULT_MULTIPLIER = 55;

export const GESTURE_HANDLING = 'greedy';
