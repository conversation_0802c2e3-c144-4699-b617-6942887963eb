/* @flow */
import React from 'react';
import { Map, List, is } from 'immutable';
import GoogleMap from 'google-map-react';
import LocationMarker from './LocationMarker';
import { OriginMarker } from './OriginMarker';
import { ORIGIN_PIN, MAX_ZOOM } from '../../garage-finder/constants';
import { API_KEY } from '../../../googleMap/constants';
import {
	DEFAULT_CENTER,
	LAT_ROOT_PATH,
	LNG_ROOT_PATH,
	DEFAULTS,
	DEFAULT_MULTIPLIER,
	GESTURE_HANDLING,
} from './constants';
import { calculateCenter } from '../../../../../../modules/googleMaps/calculateCenter';
import { normalizeLocations } from '../../../googleMap/convertData';
import { mapStyles } from './mapStyles';
import config from '../../../../../../tango/config';

export type LocationMarkerProps = {
	location: Map<any, any>,
	isOpen: boolean,
	onClose: Function,
};

type Props = {
	locations: List<Map<string, any>>,
	onLocationSelected: (id: ?string) => void,
	selectedLocation: ?string,
	onLocationClick: (location: Map<string, any>) => void,
	getLocationUrl: (id: string) => ?string,
	linkLabel: string,
	locale: string,
	customMarker?: any,
};
class LocationMap extends React.PureComponent {
	props: Props;
	state: {
		center?: Object,
		bounds?: Object,
		size?: Object,
		zoom?: number,
	};
	googleMapDomRef: any;

	constructor(props: Props) {
		super(props);

		const { center, zoom, bounds } = this.calculateCenter(props.locations);
		this.state = { center, zoom, bounds, isMobile: false };
	}

	componentWillMount() {
		if (config.isBrowser) {
			if (window.innerWidth < 434) {
				this.setState({ isMobile: true });
			}
		}
	}

	componentWillReceiveProps(nextProps: Props) {
		if (!is(nextProps.locations, this.props.locations)) {
			this.updateCenter(nextProps.locations);
		}

		if (!is(nextProps.selectedLocation, this.props.selectedLocation)) {
			const location = nextProps.locations.find(
				loc => loc.get('id') === nextProps.selectedLocation,
			);
			if (location) {
				this.centerLocation(location);
			}
		}
	}

	handleOnChange = ({ size: nextSize, bounds: nextBounds }) => {
		this.setState({
			size: nextSize,
			bounds: nextBounds,
		});
	};

	getDimensions = (): any => {
		/* eslint-disable no-underscore-dangle */
		const googleDiv = this.googleMapDomRef.googleMapDom_;
		/* eslint-disable no-underscore-dangle */

		if (googleDiv) {
			const boundingRect = googleDiv.getBoundingClientRect();
			if (boundingRect) {
				const height = parseFloat(boundingRect.height);
				const width = parseFloat(boundingRect.width);
				if (typeof height === 'number' && typeof width === 'number') {
					return {
						height: height - 150, // add padding to prevent pins underneath the input
						width,
					};
				}
			}
		}
		return undefined;
	};

	calculateCenter = (locations: List<Map<string, any>>) => {
		if (locations.size > 0 && this.googleMapDomRef && window && window.google) {
			return calculateCenter(
				normalizeLocations(locations),
				DEFAULTS,
				this.getDimensions,
			);
		}
		return DEFAULTS;
	};

	updateCenter = (locations: List<Map<string, any>>) => {
		const { center, zoom, bounds } = this.calculateCenter(locations);
		this.setState({ center, zoom, bounds });
	};

	centerLocation = (selectedLocation: ?Map<string, any>) => {
		let center = DEFAULT_CENTER;
		if (selectedLocation) {
			const { bounds, size } = this.state;

			const latOffset =
				bounds && bounds.nw && bounds.sw && size
					? (DEFAULT_MULTIPLIER * (bounds.nw.lat - bounds.se.lat)) / size.height
					: 0;

			const lat = selectedLocation.getIn(LAT_ROOT_PATH, 0);
			const lng = selectedLocation.getIn(LNG_ROOT_PATH, 0);
			center = { lat: lat + latOffset, lng };
		}
		this.setState({ center });
	};

	handleGoogleApiLoaded = () => {
		this.updateCenter(this.props.locations);
	};

	handleLocationSelected = (key: string) => this.props.onLocationSelected(key);
	handleLocationUnselected = () => this.props.onLocationSelected(undefined);
	handleLocationClick = (location: Map<string, any>) =>
		this.props.onLocationClick(location);

	createMapConfig = maps => ({
		scrollwheel: false,
		maxZoom: MAX_ZOOM,
		styles: mapStyles,
		fullscreenControl: true,
		fullscreenControlOptions: {
			position: maps.ControlPosition.BOTTOM_LEFT,
		},
		clickableIcons: false,
		gestureHandling: GESTURE_HANDLING,
	});

	render() {
		const {
			locations,
			selectedLocation,
			linkLabel,
			getLocationUrl,
			locale,
			customMarker,
		} = this.props;

		const { center, zoom, isMobile } = this.state;

		const mapConfig = {
			bootstrapURLKeys: {
				key: API_KEY,
				language: locale,
			},
		};

		const zoomValue = isMobile ? 7 : zoom;

		return (
			<GoogleMap
				ref={ref => (this.googleMapDomRef = ref)}
				center={center}
				zoom={zoomValue}
				onChildClick={this.handleLocationSelected}
				onChange={this.handleOnChange}
				onGoogleApiLoaded={this.handleGoogleApiLoaded}
				yesIWantToUseGoogleMapApiInternals
				resetBoundsOnResize
				options={this.createMapConfig}
				{...mapConfig}
			>
				{locations.map(location => {
					const id = location.get('id');
					const lat = location.getIn(LAT_ROOT_PATH);
					const lng = location.getIn(LNG_ROOT_PATH);
					/*
						key has to be id
						otherwise this.handleMarkerClicked will not work correctly
					*/
					if (location.get('type') === ORIGIN_PIN) {
						return (
							<OriginMarker key={id} lat={lat} lng={lng} location={location} />
						);
					}
					return (
						<LocationMarker
							key={id}
							id={id}
							lat={lat}
							lng={lng}
							location={location}
							isOpen={selectedLocation === id}
							onClose={this.handleLocationUnselected}
							onLocationClick={this.handleLocationClick}
							getLocationUrl={getLocationUrl}
							linkLabel={linkLabel}
							customMarker={customMarker}
						/>
					);
				})}
			</GoogleMap>
		);
	}
}

export default (LocationMap: ReactComponent<Props>);
