/* eslint-disable max-len */
/* @flow */
import React from 'react';
import { Map, List } from 'immutable';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { connect } from 'react-redux';

import { localize, type LocalizeProps } from '../../../../../../modules/i18n';
import { getUrlWithHttp } from '../../../../../../tango/link';
import combineHOCs from '../../../../../../tango/hoc/combineHOCs';
import containsRetailService from '../../../dealer-config/containsRetailService';
import containsServiceService from '../../../dealer-config/containsServiceService';
import { namespace } from '../i18n';
import ExternalLink from '../../../../../../modules/external-link';
import OpeningHours from '../../../contact/OpeningHours';
import { getBrand } from '../../../config/selectors';
import { info } from '../../../../../../tango/logger';
import { getCustomConfig } from '../../../../../../modules/config/selectors';
import { Address } from './Address';
import { getBrandUrl } from './util';
import { getDealerHasNewRetailerLayout } from '../../../DealerConfigProvider/selectors';
import { prepareTestDriveLink } from '../../../../../amagSkoda2020/helpers';

import styles from './InfoWindow.css';

type Props = {
	location: Map<string, any>,
	onClose: () => void,
	onClick: () => void,
	className?: string,
	linkLabel: string,
	getLocationUrl: (id: string) => ?string,
};

type InternalProps = {
	brand: string,
	customConfig: Map<string, any>,
	locale: string,
	hasNewRetailerLayout: boolean,
};

type State = {
	hasSales: boolean,
	activeTab: string,
};

const GENERIC_APPOINTMENT_PATHS = {
	de: 'servicetermin',
	fr: 'rendez-vous-au-service',
	it: 'appuntamento-di-servizio',
};

const SERVICE_APPOINTMENTS = {
	de: 'termin-anfrage',
	fr: 'demande-de-rendez-vous',
	it: 'richiesta-di-appuntamento',
};

const LANGUAGE_MAPPER = {
	D: 'de',
	F: 'fr',
	I: 'it',
};
const checkURL = (urlParam: string) => !/\//.test(urlParam);

const getFirstActiveLanguage = (visitLocations, language) => {
	if (visitLocations === undefined || visitLocations === null) {
		return LANGUAGE_MAPPER[language];
	}

	if (visitLocations.size > 0) {
		return visitLocations.keySeq().toArray()[0];
	}

	return LANGUAGE_MAPPER[language];
};

class InfoWindow extends React.PureComponent {
	props: Props & InternalProps & LocalizeProps;
	state: State;

	constructor(props: Props & InternalProps & LocalizeProps) {
		super(props);
		const { brand, location } = props;

		const services = location.getIn(['brands', brand, 'services'], new List());
		const hasSalesService = containsRetailService(services);
		const departments = location.get('departments');
		const hasSalesHours = departments.get('SALES');
		const hasSales = hasSalesService && hasSalesHours;

		this.state = {
			hasSales,
			activeTab: hasSales ? 'SALES' : 'SERVICE',
		};
	}

	setActiveTab = (activeTab: string) => {
		this.setState({ activeTab });
	};

	render() {
		const {
			location,
			onClose,
			onClick,
			className,
			linkLabel,
			getLocationUrl,
			t,
			customConfig,
			brand,
			hasNewRetailerLayout,
			locale,
		} = this.props;
		const { activeTab } = this.state;
		const id = location.get('id');
		const url = getLocationUrl(id);
		const departments = location.get('departments');
		const dealerLabel = location.get('dealer-label');
		const services = location.getIn(['brands', brand, 'services'], new List());
		let hasServiceService = containsServiceService(services);

		// CUPRA doesn't have it's own Service department, it uses the same as SEAT
		if (brand === 'cupra') {
			// check if they have a 'SERVICE' department under their SEAT brand
			const seatServices = location.getIn(
				['brands', 'seat', 'services'],
				new List(),
			);
			hasServiceService = containsServiceService(seatServices);
		}

		const hasServiceHours = departments.get('SERVICE');

		const hasSales = this.state.hasSales;
		const hasService = hasServiceService && hasServiceHours;

		/*
		 * on the retailer page we want to always show a testDrive url if the dealer has one,
		 *	hence we need to check for all the languages
		 */
		const FALLBACK_LOCALES = ['de', 'fr', 'it'].filter(loc => loc !== locale);
		const testDriveUrl = location.getIn(
			['dealer-brand-websites', brand, 'urls', 'test-drive', locale],
			location.getIn(
				[
					'dealer-brand-websites',
					brand,
					'urls',
					'test-drive',
					FALLBACK_LOCALES[0],
				],
				location.getIn(
					[
						'dealer-brand-websites',
						brand,
						'urls',
						'test-drive',
						FALLBACK_LOCALES[1],
					],
					'',
				),
			),
		);

		const finalTestDriveUrl =
			brand === 'skoda'
				? prepareTestDriveLink(
						location.get('customer-nr') || location.get('additional-nr'),
						locale,
						[''],
						testDriveUrl,
				)
				: testDriveUrl;

		if (!departments) {
			info(location);
		}
		const customDomainConfig = !hasNewRetailerLayout ? customConfig : new Map();
		const brandSiteIsActive = location.getIn(
			['dealer-brand-websites', brand, 'activated'],
			false,
		);
		const brandUrl = getBrandUrl(
			dealerLabel,
			customDomainConfig,
			url,
			brandSiteIsActive,
		);

		const visitLocations = location.getIn(
			['visit-appointments', brand],
			undefined,
		);

		const firstActiveLanguage = getFirstActiveLanguage(
			visitLocations,
			location.getIn(['contact-info', 'language']),
		);

		const customFirstActiveLanguage =
			dealerLabel === 'dileomotors' ? 'it' : firstActiveLanguage;

		const appointmentUrl = location.getIn([
			'visit-appointments',
			brand,
			customFirstActiveLanguage,
		]);

		const tabs = departments
			? [
				...(hasSales ? [{ label: 'sale', id: 'SALES' }] : []),
				...(hasService ? [{ label: 'service', id: 'SERVICE' }] : []),
			]
			: [];

		const serviceAppointmentText =
			brand === 'stopgo' ? t('stopGoAppointmentLabel') : t('appointmentLabel');

		const getButtonStylingByBrand = (brandParam, stylesParam) => {
			switch (brandParam) {
				case 'skoda':
					return stylesParam.tertiaryButton;
				case 'audi':
					return stylesParam.audiSecondaryButton;
				case 'seat':
				case 'vw':
				case 'cupra':
					return stylesParam.secondaryButton;
				case 'stopgo':
					return stylesParam.stopGoSecondaryButton;
				default:
					return stylesParam.secondaryButton;
			}
		};

		const serviceAppointmentButton = (
			appointmentUrlParam,
			brandUrlParam,
			localeParam,
			brandParam,
			dealerLabelParam,
			firstActiveLanguageParam,
		) => {
			const buttonStyling = getButtonStylingByBrand(brandParam, styles);

			if (appointmentUrlParam) {
				return (
					<ExternalLink href={appointmentUrlParam} className={buttonStyling}>
						{serviceAppointmentText}
					</ExternalLink>
				);
			}

			const firstPart = getUrlWithHttp(window.location.origin);

			const fallbackAppointmentUrl =
				brandParam === 'stopgo'
					? `${firstPart}/${dealerLabelParam}/${firstActiveLanguageParam}/${
							SERVICE_APPOINTMENTS[firstActiveLanguageParam]
					}`
					: `${firstPart}/${dealerLabelParam}/${firstActiveLanguageParam}/${
							GENERIC_APPOINTMENT_PATHS[firstActiveLanguageParam]
					}`;

			return (
				<ExternalLink
					href={fallbackAppointmentUrl}
					className={buttonStyling}
					application
					onClick={() => onClick()}
				>
					{serviceAppointmentText}
				</ExternalLink>
			);
		};

		const getTestDriveUrl = (
			finalTestDriveUrlParam,
			dealerName,
			localeParam,
		) => {
			const firstPart = window.location.origin;

			if (checkURL(finalTestDriveUrlParam)) {
				return `${getUrlWithHttp(
					firstPart,
				)}/${dealerName}/${localeParam}/${finalTestDriveUrlParam}`;
			}

			return finalTestDriveUrlParam;
		};

		return (
			// eslint-disable-next-line jsx-a11y/no-static-element-interactions
			<div
				className={classNames(className, styles.infoWindow)}
				onClick={e => {
					e.stopPropagation();
				}}
			>
				<button className={styles.close} onClick={onClose} />
				<Address location={location} />
				{departments && (
					<React.Fragment>
						<div className={styles.tabs}>
							{tabs.map(tab => (
								<button
									key={tab.id}
									onClick={() => this.setActiveTab(tab.id)}
									className={classNames(styles.button, {
										[styles.active]: activeTab === tab.id,
									})}
								>
									{t(tab.label)}
								</button>
							))}
						</div>
						<OpeningHours
							department={departments.getIn([activeTab])}
							className={styles.openingHours}
							closedText={t('closed')}
						/>
					</React.Fragment>
				)}
				{hasNewRetailerLayout && finalTestDriveUrl && (
					<ExternalLink
						onClick={() => onClick()}
						href={getTestDriveUrl(
							finalTestDriveUrl,
							dealerLabel,
							customFirstActiveLanguage,
							url,
						)}
						className={styles.secondaryButton}
					>
						{t('testDriveLabel')}
					</ExternalLink>
				)}
				{appointmentUrl && hasNewRetailerLayout && (
					<ExternalLink
						href={appointmentUrl}
						className={styles.secondaryButton}
					>
						{serviceAppointmentText}
					</ExternalLink>
				)}
				<div className={styles.buttonsSection}>
					{brandSiteIsActive &&
						!hasNewRetailerLayout &&
						serviceAppointmentButton(
							appointmentUrl,
							brandUrl,
							locale,
							brand,
							dealerLabel,
							customFirstActiveLanguage,
						)}
					{brandUrl && (
						<ExternalLink
							onClick={() => onClick()}
							href={brandUrl}
							className={styles.mainButton}
							application={brandUrl.startsWith('/')}
						>
							{linkLabel || t('gotoDefault')}
						</ExternalLink>
					)}
				</div>
			</div>
		);
	}
}

const withHocs = combineHOCs([
	localize(namespace),
	withStyles(styles),
	connect((state: ReduxState) => ({
		brand: getBrand(state),
		customConfig: getCustomConfig(state),
		hasNewRetailerLayout: getDealerHasNewRetailerLayout(state),
	})),
]);

export default (withHocs(InfoWindow): ReactComponent<Props>);
