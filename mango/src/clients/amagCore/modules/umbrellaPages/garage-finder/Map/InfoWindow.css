@import theme(amagUmbrella/garageFinder);

:root {
	--cancelIcon: url('../../svg/close.svg');
}

.infoWindow {
	background-color: var(--white);
	box-sizing: border-box;
	padding: 20px;
	width: max-content;
	font-size: var(--default-font-size);
	line-height: 1.4;
	box-shadow: 0 17px 45px 0 color(var(--black) alpha(30%));
	font-family: var(--font-text);

	&:after {
		content: ' ';
		position: absolute;
		bottom: -12px;
		left: calc(50% - 12px);
		border-top: 12px var(--white) solid;
		border-left: 12px transparent solid;
		border-right: 12px transparent solid;
	}

	& .section {
		margin-top: 20px;
	}

	& a {
		color: var(--primary-color);
		transition: all 0.3s ease;

		&:hover {
			color: color(var(--primary-color) lightness(30%));
		}
	}

	& .mainButton {
		position: relative;
		box-sizing: border-box;
		text-align: center;
		padding: 15px 20px;
		border: none;
		width: 100%;
		color: var(--white);
		background-color: var(--primary-color);
		font-size: var(--default-font-size);
		cursor: pointer;
		display: inline-block;
		margin: 20px 0 0 0;
		transition: all 0.3s ease;
		text-decoration: none;
		white-space: nowrap;

		&:hover {
			background: color(var(--primary-color) lightness(30%));
			color: var(--white);
		}
	}

	& .buttonsSection {
		display: flex;
		justify-content: space-between;
		gap: 10px;
	}

	& .secondaryButton {
		color: var(--secondary-color);
		background-color: var(--white);
		font-size: 17px;
		
		position: relative;
		box-sizing: border-box;
		text-align: center;
		padding: 15px 20px;
		border: 2px solid var(--secondary-color);
		width: 100%;
		cursor: pointer;
		display: inline-block;
		margin: 20px 0 0 0;
		transition: all 0.3s ease;
		text-decoration: none;
		white-space: nowrap;

		&:hover {
			background: var(--primary-color);
			color: var(--white);
		}
	}

	& .audiSecondaryButton {
		color: var(--secondary-color);
		background-color: var(--white);
		font-size: 17px;
		
		position: relative;
		box-sizing: border-box;
		text-align: center;
		padding: 15px 20px;
		border: 2px solid var(--secondary-color);
		width: 100%;
		cursor: pointer;
		display: inline-block;
		margin: 20px 0 0 0;
		transition: all 0.3s ease;
		text-decoration: none;
		white-space: nowrap;

		&:hover {
			background: var(--secondary-color);
			color: var(--white);
		}
	}

	& .stopGoSecondaryButton {
		color: var(--primary-color);
		background-color: var(--white);
		font-size: 17px;
		
		position: relative;
		box-sizing: border-box;
		text-align: center;
		padding: 15px 20px;
		border: 2px solid var(--primary-color);
		width: 100%;
		cursor: pointer;
		display: inline-block;
		margin: 20px 0 0 0;
		transition: all 0.3s ease;
		text-decoration: none;
		white-space: nowrap;

		&:hover {
			background: var(--primary-color);
			color: var(--white);
		}
	}

	& .tertiaryButton {
		color: var(--ultra-dark-gray, #373737);
		background-color: var(--super-light-gray, f8f8f8);
		font-size: 17px;
		white-space: nowrap;
		position: relative;
		box-sizing: border-box;
		text-align: center;
		padding: 15px 20px;
		border: 2px solid var(--ultra-dark-gray, #373737);
		border-radius: 40px;
		width: 100%;
		cursor: pointer;
		display: inline-block;
		margin: 20px 0 0 0;
		transition: all 0.3s ease;
		text-decoration: none;
		font-weight: 700;

		&:hover {
			background: var(--primary-color-hover);
			color: var(--ultra-dark-gray, #373737);
		}
	}

	& .close {
		appearance: none;
		position: absolute;
		top: 20px;
		right: 20px;
		height: 16px;
		width: 16px;
		padding: 0;
		background: none;
		border: none;
		background-image: url('../../svg/close.svg');
		background-repeat: no-repeat;
		background-position: center center;
		background-size: 16px;
		transition: all 0.3s ease;
		outline: none;
		cursor: pointer;

		&:hover {
			transform: rotate(90deg);
		}
	}

	& .tabs {
		display: flex;
		border: solid 1px var(--light-gray);
		border-bottom: none;

		& .button {
			all: initial;
			font-family: var(--font-text);
			font-size: var(--default-font-size);
			color: var(--black);
			text-align: center;
			box-sizing: border-box;
			padding: 5px;
			flex-basis: 50%;
			cursor: pointer;
			transition: all 0.3s ease;
			text-transform: capitalize;
			background: color(var(--light-gray) alpha(20%));

			&:hover {
				background: color(var(--light-gray) alpha(50%));
			}

			&:last-child {
				border-left: solid 1px var(--light-gray);
			}

			&:only-of-type {
				text-align: left;
				padding-left: 10px;
				border-left: none;
			}

			&.active {
				background: var(--white);
				font-weight: bold;
				color: var(--primary-color);
			}
		}
	}

	& .openingHours {
		border: solid 1px var(--light-gray);
		padding: 10px;

		& h3 {
			margin: 0 0 5px;

			&:empty {
				display: none;
			}
		}
	}

	& p {
		color: var(--medium-gray);

		&:first-of-type {
			margin-top: 0;
		}

		& strong {
			color: var(--black);
			padding: 0 0 10px 0;
			margin: 0;
			display: inline-block;
		}
	}

	@media (--screen-mobile) {
		& .tabs,
		& .openingHours {
			display: none;
		}

		width: fit-content;
		min-width: 300px;

		& strong {
			width: min-content;
		}
	}

	@apply --amagCoreInfoWindow;
}
