/* @flow */
const deg2rad = (deg: number) => deg * (Math.PI / 180);

type Location = {
	lat: number,
	lng: number,
};

const calculateDistance = (loc1: Location, loc2: Location) => {
	const R = 6371; // Radius of the earth in km
	const dLat = deg2rad(loc2.lat - loc1.lat);
	const dLon = deg2rad(loc2.lng - loc1.lng);
	const a =
		(Math.sin(dLat / 2) * Math.sin(dLat / 2)) +
		(Math.cos(deg2rad(loc1.lat))
			* Math.cos(deg2rad(loc2.lat))
			* Math.sin(dLon / 2)
			* Math.sin(dLon / 2));

	const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
	const d = R * c; // Distance in km
	return d;
};


export {
	calculateDistance,
};
