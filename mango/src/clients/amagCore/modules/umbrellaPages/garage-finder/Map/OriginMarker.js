/* @flow */
import React from 'react';
import { Map } from 'immutable';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import classNames from 'classnames';

import { localize, type LocalizeProps } from '../../../../../../modules/i18n';
import combineHOCs from '../../../../../../tango/hoc/combineHOCs';
import { namespace } from '../i18n';
import styles from './LocationMarker.css';
import infoStyles from './InfoWindow.css';
import markerIcon from './proximity.png';

type Props = {
	location: Map<string, any>,
	isOpen: boolean,
	onClose: () => void,
	onLocationClick: () => void,
};

function OriginMarkerComponent(props: Props & LocalizeProps) {
	const { location, locale } = props;

	return (
		<div className={styles.markerContainer}>
			<img
				className={classNames(styles.marker, styles.dumbMarkerImg)}
				src={markerIcon}
				alt={location.getIn(['name', locale])}
			/>
			<div className={classNames(styles.infoWindow, infoStyles.infoWindow, styles.dumbMarkerText)}>
				{location.get('name')}
			</div>
		</div>
	);
}

const withHocs = combineHOCs([localize(namespace), withStyles(styles, infoStyles)]);

export const OriginMarker = withHocs(OriginMarkerComponent);
