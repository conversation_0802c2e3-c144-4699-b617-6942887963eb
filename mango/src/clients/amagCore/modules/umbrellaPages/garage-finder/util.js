/* @flow */
import { List, Map } from 'immutable';
import { Search } from 'js-search';

import { namespace } from './i18n';
import { getSaleBrands, getServiceBrands } from '../../brands/utils';
import containsRetailService from '../../dealer-config/containsRetailService';
import containsServiceService from '../../dealer-config/containsServiceService';
import { namespace as brandNamespace } from '../../brands/i18n';
import { error } from '../../../../../tango/logger';

const LANGUAGES = ['de', 'fr', 'it'];

function getBrandNamesForBrand(brand: string, t: TranslateFunction) {
	switch (brand) {
		case 'volkswagen':
		case 'volkswagen-nutzfahrzeuge':
			return [
				'Volkswagen',
				t(brand, undefined, brandNamespace),
			];
		case 'skoda':
			return [
				'SKODA',
				t(brand, undefined, brandNamespace),
			];
		default:
			return [
				t(brand, undefined, brandNamespace),
			];
	}
}

function makeSortByName(locale: string) {
	return (locationA: Object, locationB: Object) => {
		if (typeof locationA.name === 'undefined') {
			error('Corrupted location found', locationA);
		}
		if (typeof locationB.name === 'undefined') {
			error('Corrupted location found', locationB);
		}
		const nameA = locationA.name ? locationA.name[locale] : locationA['dealer-label'];
		const nameB = locationB.name ? locationB.name[locale] : locationB['dealer-label'];
		if (nameA < nameB) return -1;
		if (nameA > nameB) return 1;
		return 0;
	};
}

function prepareLocationsForSearch(locations: List<Map<string, any>>, t: TranslateFunction) {
	return locations.map((location) => {
		const salesBrands = getSaleBrands(location.get('brands', new Map()));
		const serviceBrands = getServiceBrands(location.get('brands', new Map()));
		const brandsText = salesBrands.concat(serviceBrands)
			.toSet()
			.map(brand => getBrandNamesForBrand(brand, t))
			.reduce((acc, names) => acc.concat(names), [])
			.join(' ');

		const hasSalesBrands = salesBrands.size !== 0;
		const hasSerivceBrands = serviceBrands.size !== 0;
		const saleText = hasSalesBrands ? t('sale', undefined, namespace) : '';
		const serviceText = hasSerivceBrands ? t('service', undefined, namespace) : '';

		const name = LANGUAGES.map(lang => location.getIn(['name', lang])).join(' ');
		const name1 = LANGUAGES.map(lang => location.getIn(['contact-info', 'address', 'name1', lang])).join(' ');
		const name2 = LANGUAGES.map(lang => location.getIn(['contact-info', 'address', 'name2', lang])).join(' ');

		return location
			.setIn(['search', 'brands'], brandsText)
			.setIn(['search', 'contracts'], `${saleText} ${serviceText}`)
			.setIn(['search', 'names'], `${name} ${name2} ${name1}`);
	});
}

function hasLocationRequiredBrands(
	location: Map<string, any>,
	salesBrandFilter: ?Array<string>,
	serviceBrandFilter: ?Array<string>,
): boolean {
	const hasRequiredsalesBrandFilter = salesBrandFilter ? location.get('brands', new Map())
		.keySeq()
		.some((brand) => {
			const services = location.getIn(['brands', brand, 'services'], new List());
			const isSale = containsRetailService(services);
			return isSale && salesBrandFilter && salesBrandFilter.includes(brand);
		})
		: true;

	const hasRequiredserviceBrandFilter = serviceBrandFilter ? location.get('brands', new Map())
		.keySeq()
		.some((brand) => {
			const services = location.getIn(['brands', brand, 'services'], new List());
			const isService = containsServiceService(services);
			return isService && serviceBrandFilter && serviceBrandFilter.includes(brand);
		})
		: true;

	return (hasRequiredsalesBrandFilter || hasRequiredserviceBrandFilter);
}

function initializeSearch(locale: string): Object {
	const search = new Search('id');
	search.addIndex(['name', locale]);
	search.addIndex(['contact-info', 'address', 'street']);
	search.addIndex(['contact-info', 'address', 'name1', locale]);
	search.addIndex(['contact-info', 'address', 'name2', locale]);
	search.addIndex(['contact-info', 'address', 'place']);
	search.addIndex(['contact-info', 'address', 'zip-code']);
	search.addIndex(['search', 'names']);
	search.addIndex(['search', 'brands']);
	search.addIndex(['search', 'contracts']);
	search.addIndex('www');
	return search;
}

export {
	makeSortByName,
	prepareLocationsForSearch,
	hasLocationRequiredBrands,
	initializeSearch,
};
