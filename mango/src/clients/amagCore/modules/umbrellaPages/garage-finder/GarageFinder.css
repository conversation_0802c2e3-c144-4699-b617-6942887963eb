@import theme(amagCoreGarageFinder/master);

.map {
	height: 100%;
}

.garagefinder {
	position: relative;
	height: 100%;

	& input {
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		line-height: var(--amag-core-garagefinder-input-height);
		height: var(--amag-core-garagefinder-input-height);
		top: var(--amag-core-garagefinder-input-position-top);
		width: var(--amag-core-garagefinder-input-width);
		font-size: var(--amag-core-garagefinder-input-font-size);
		max-width: 750px;

		&[type="checkbox"] {
			position: relative;
			max-width: 20px;
		}

		&::placeholder {
			color: var(--medium-gray);
		}
	}

	& .filtersToggle {
		position: absolute;
		left: 65%;
		top: 5%;
		width: 100px;
	}

	& .filtersContainer {
		position: absolute;
		left: 30%;
		top: 15%;
		display: none;
		background: white;
		padding: 10px;
		
		&.filterIsOpen {
			display: block;
		}
	}

	& .suggestions {
		display: none;

		&.filterIsOpen {
			top: 400px;
		}

		&_opened {
			display: block;
		}
	}
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(359deg);
	}
}

.loaderContainer {
	position: relative;
	height: 200px;

	& .loader {
		position: absolute;
		background: color(white alpha(0%));
		z-index: 5;
		display: inline-block;
		left: 50%;
		top: 50%;
		transform: translate(-50%, 50%);
		border-radius: 50%;
		width: 25px;
		height: 25px;
		border: .25rem solid color(var(--amag-core-garagefinder-primary-color) alpha(-80%));
		border-top-color: white;
		animation: spin 750ms infinite linear;
		z-index: 5;
	}
}
