/* @flow */
import { calculateDistance } from './Map/calculateDistance';
import { objectSortByKey } from '../../../../../modules/post/documents/util';
import { PROXIMITY_SEARCH_SETTINGS as CONFIG, ORIGIN_PIN } from './constants';
import { error } from '../../../../../tango/logger';

// id is needed as the Map compont uses it as a selector
// if it's undefined this will be focuesd if nothing is selected
const googleResultToAmag = result => {
	if (result[0]) {
		const location = result[0];
		return {
			type: ORIGIN_PIN,
			id: location.formatted_address,
			name: location.formatted_address,
			'contact-info': {
				address: {
					'geo-location': {
						lat: location.geometry.location.lat(),
						lng: location.geometry.location.lng(),
					},
				},
			},
		};
	}

	return {
		type: ORIGIN_PIN,
		id: result.formatted_address,
		name: result.formatted_address,
		'contact-info': {
			address: {
				'geo-location': {
					lat: result.geometry.location.lat(),
					lng: result.geometry.location.lng(),
				},
			},
		},
	};
};

function safelyGetProperty(parent: Object, key: string, locale: string = 'de') {
	return (parent[key] && parent[key][locale]) || '';
}

// test if the name of the location contains the query
// but do not match if address contains query as that would prioritize garages with the name of a location above others near that location
function compareName(location: Object, query: string, locale: string = 'de') {
	const address = location['contact-info'].address;
	const name =
		safelyGetProperty(address, 'name1', locale) +
		safelyGetProperty(address, 'name2', locale);
	return name.toLowerCase().includes(query.toLowerCase());
}

function matchQueryToLocation(location: Object, query: string) {
	if (query.toLocaleLowerCase() === 'zug') {
		return true;
	}
	const address = location['contact-info'].address;
	const locationName = `${address.place} ${address.street} ${
		address['zip-code']
	}`;
	return locationName.toLowerCase().includes(query.toLowerCase());
}

const TEXT_SEARCH_ENABLED = true;

export const proximitySearch = (
	query: string,
	locations: Array<Object>,
	region?: string = 'CH',
	locale?: string = 'de',
) => {
	const promise = new Promise((resolve, reject) => {
		if (
			window &&
			window.google &&
			window.google.maps &&
			window.google.maps.Geocoder
		) {
			const queryIsLocation = locations.some(loc =>
				matchQueryToLocation(loc, query),
			);
			const foundLocations = locations.filter(loc =>
				compareName(loc, query, locale),
			);
			if (
				TEXT_SEARCH_ENABLED &&
				!queryIsLocation &&
				foundLocations.length > 0
			) {
				resolve(foundLocations);
			} else {
				const geocoder = new window.google.maps.Geocoder();
				geocoder.geocode(
					{ address: `${query}, switzerland`, region },
					(results, status) => {
						if (status === window.google.maps.GeocoderStatus.OK) {
							// we use the first result, sometimes google returns a second one, but that doesn't respect the region bounds
							const result = results[0];
							const searchLocation = result.geometry.location;
							const searchCoordinates = {
								lat: searchLocation.lat(),
								lng: searchLocation.lng(),
							};
							const locationsByDistance = locations
								.map(location => ({
									...location,
									distance: calculateDistance(
										searchCoordinates,
										location['contact-info'].address['geo-location'],
									),
								}))
								.sort((a, b) => objectSortByKey(a, b, 'distance'))
								.slice(0, CONFIG.LIMIT);

							resolve([googleResultToAmag(result), ...locationsByDistance]);
						} else {
							reject(status);
						}
					},
				);
			}
		} else {
			const errorText = 'Google Geocoder not loaded';
			error(errorText);
			reject(errorText);
		}
	});
	return promise;
};
