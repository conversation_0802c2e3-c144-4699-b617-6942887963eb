/* @flow */
import React from 'react';
import { Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { connect } from 'react-redux';
import { is, fromJS, List, Map } from 'immutable';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import combineHOCs from '../../../../../tango/hoc/combineHOCs';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import { withAsyncReduxData } from '../../../../../tango/withAsyncReduxData';
import { GarageFinder } from './GarageFinder';
import styles from './GarageFinder.css';
import { proximitySearch } from '../garage-finder/proximitySearch';
import { GEOLOCATE_USER } from '../garage-finder/constants';
import { findLocationsByTags } from '../../dealer/actions';
import {
	getLocationsByTags,
	getFindLocationsByTagsIsCompleted,
	getFindLocationsByTagsIsFetching,
} from '../../dealer/selectors';
import { trackUmbrellaSearchClickOnCTA } from '../tracking/tracking';
import {
	makeSortByName,
	prepareLocationsForSearch,
	hasLocationRequiredBrands,
	initializeSearch,
} from '../garage-finder/util';
import { getLocationUrl } from './utils';
import { infoDev } from '../../../../../tango/logger';
import { type SelectedTag } from './types';

type Props = {
	className?: string,
	prefilledSearch?: ?string,
	locationTags: Array<string>,
	linkLabel: string,
	salesBrandFilter?: Array<string>,
	serviceBrandFilter?: Array<string>,
	customLocationFilter?: (location: Map<string, any>) => boolean,
	onLocationClick?: (location: Map<string, any>) => void,
	customMarker?: any,
	controlsClassName?: string,
	showFilters?: boolean,
};

type InternalProps = LocalizeProps & {
	locations: List<Map<string, any>>,
	isCompleted: boolean,
	isFetching: boolean,
	findLocations: (tags: Array<string>) => void,
};

type State = {
	locations: Array<any>,
	foundLocations: Array<any>,
	selectedLocation: ?string,
	query: string,
	isFetching: boolean,
	userLocation?: Object,
	serviceFilters: Array<SelectedTag>,
	salesFilters: Array<SelectedTag>,
};

class GarageFinderContainerComponent extends React.PureComponent {
	props: Props & InternalProps;
	state: State;
	search: any;
	debouncedSearch: any;
	sortByName: (locationA: Object, locationB: Object) => number;

	constructor(props: Props & InternalProps) {
		super(props);
		const { locale, prefilledSearch } = props;

		this.state = {
			isFetching: true,
			locations: [],
			foundLocations: [],
			selectedLocation: undefined,
			query: prefilledSearch || '',
			userLocation: undefined,
			serviceFilters: [],
			salesFilters: [],
		};

		this.debouncedSearch = new Subject();

		this.initialiseSearch();

		this.sortByName = makeSortByName(locale);
		if (
			GEOLOCATE_USER &&
			window &&
			window.navigator &&
			window.navigator.geolocation
		) {
			window.navigator.geolocation.getCurrentPosition(position => {
				this.setState({
					userLocation: {
						lat: position.coords.latitude,
						lng: position.coords.longitude,
					},
				});
			});
		}
	}

	componentDidMount() {
		this.debouncedSearch.pipe(debounceTime(200)).subscribe(this.handleSearch);
	}

	componentWillReceiveProps(nextProps: Props & InternalProps) {
		const nextIsCompleted = nextProps.isCompleted;
		const prevIsCompleted = this.props.isCompleted;
		const nextSalesBrandFilter = nextProps.salesBrandFilter;
		const prevSalesBrandFilter = this.props.salesBrandFilter;
		const nextServiceBrandFilter = nextProps.serviceBrandFilter;
		const prevServiceBrandFilter = this.props.serviceBrandFilter;

		if (nextIsCompleted && !prevIsCompleted && nextProps.locations) {
			const preparedLocations = this.prepareLocations(nextProps);
			this.setState({
				locations: preparedLocations,
				foundLocations: preparedLocations.sort(this.sortByName),
			});
			this.search.addDocuments(preparedLocations);
		}

		if (
			!is(fromJS(nextSalesBrandFilter), fromJS(prevSalesBrandFilter)) ||
			!is(fromJS(nextServiceBrandFilter), fromJS(prevServiceBrandFilter))
		) {
			const preparedLocations = this.prepareLocations(nextProps);

			this.initialiseSearch();
			this.search.addDocuments(preparedLocations);
			this.setState({ locations: preparedLocations }, () =>
				this.handleSearch(this.state.query),
			);
		}
	}

	initialiseSearch = () => {
		const { locale } = this.props;
		this.search = initializeSearch(locale);
	};

	prepareLocations = (props: Props & InternalProps) => {
		const {
			locations,
			salesBrandFilter,
			serviceBrandFilter,
			customLocationFilter = () => true,
			t,
		} = props;

		const preparedLocations = prepareLocationsForSearch(
			locations
				.filter(loc => Boolean(loc.get('contact-info')))
				.filter(customLocationFilter)
				.filter(loc =>
					hasLocationRequiredBrands(loc, salesBrandFilter, serviceBrandFilter),
				),
			t,
		).toJS();
		return preparedLocations;
	};

	handleSearch = (query: ?string) => {
		if (!query || query.length >= 3) {
			const foundLocations = query
				? this.search.search(query)
				: this.state.locations;

			if (query) {
				// GOOGLE SEARCH
				proximitySearch(
					query,
					this.state.locations,
					undefined,
					this.props.locale,
				)
					.then(locations => {
						this.setState({ foundLocations: locations });

						// when user searches for a location which cannot be found on Switzerland it returns `Schweiz`
						if (locations[0].id === 'Schweiz') {
							const sortedFoundLocations = foundLocations.sort(this.sortByName);
							this.setState({ foundLocations: sortedFoundLocations });
						}
					})
					.catch(() => this.setState({ foundLocations: [] }));
			} else {
				const sortedFoundLocations = foundLocations.sort(this.sortByName);
				this.setState({ foundLocations: sortedFoundLocations });
			}
		}
	};

	updateQuery = (query: string) => {
		this.setState({ query, selectedLocation: undefined });
		this.debouncedSearch.next(query);
	};

	updateSelectedLocation = (id: ?string) => {
		this.setState({ selectedLocation: id });
	};

	getLoctionById = (id: string) =>
		this.state.foundLocations.find(loc => loc.id === id);

	render() {
		const {
			onLocationClick,
			className,
			isFetching,
			linkLabel,
			salesBrandFilter = [],
			customMarker,
			controlsClassName,
			showFilters,
		} = this.props;

		const {
			foundLocations,
			selectedLocation,
			query,
			salesFilters,
			serviceFilters,
		} = this.state;

		if (isFetching) {
			return (
				<div className={styles.loaderContainer}>
					<div className={styles.loader} />
				</div>
			);
		}

		infoDev(
			foundLocations.length,
			'locations vs filtered',
			foundLocations.length,
			'(umbrella)',
		);

		return (
			<GarageFinder
				className={className}
				query={query}
				foundLocations={foundLocations}
				selectedLocation={selectedLocation}
				onQueryChanged={this.updateQuery}
				onLocationSelected={this.updateSelectedLocation}
				onLocationClick={(location: Map<string, any>) => {
					const url = getLocationUrl(location);
					if (url) {
						trackUmbrellaSearchClickOnCTA(url);
					}

					if (onLocationClick && typeof onLocationClick === 'function') {
						onLocationClick(location);
					}
				}}
				getLocationUrl={(id: string) =>
					getLocationUrl(fromJS(this.getLoctionById(id)))
				}
				linkLabel={linkLabel}
				onSalesFiltersChanged={newFilters =>
					this.setState({ salesFilters: newFilters })
				}
				onServiceFiltersChanged={newFilters =>
					this.setState({ serviceFilters: newFilters })
				}
				customMarker={customMarker}
				controlsClassName={controlsClassName}
				showFilters={showFilters}
				salesBrandFilter={salesBrandFilter}
				salesFilters={salesFilters}
				serviceFilters={serviceFilters}
			/>
		);
	}
}

function mapStateToProps(state: ReduxState, { locationTags }: Props) {
	return {
		locations: getLocationsByTags(state, locationTags),
		isCompleted: getFindLocationsByTagsIsCompleted(state, locationTags),
		isFetching: getFindLocationsByTagsIsFetching(state, locationTags),
	};
}

function mapDispatchToProps(dispatch: Function) {
	return {
		findLocations: (tags: Array<string>) => dispatch(findLocationsByTags(tags)),
	};
}

function requestData({ findLocations, locationTags }: Props & InternalProps) {
	findLocations(locationTags);
}

function isDone({ isCompleted }: Props & InternalProps) {
	return isCompleted;
}

const withHocs = combineHOCs([
	withAsyncReduxData(requestData, isDone, mapStateToProps, mapDispatchToProps),
	connect(
		mapStateToProps,
		mapDispatchToProps,
	),
	localize(),
	withStyles(styles),
]);

export const GarageFinderContainer = (withHocs(
	GarageFinderContainerComponent,
): ReactComponent<Props>);
