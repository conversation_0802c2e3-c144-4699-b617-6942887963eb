/* eslint-disable jsx-a11y/no-static-element-interactions */
import React from 'react';
import { connect } from 'react-redux';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { Map, fromJS } from 'immutable';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import combineHOCs from '../../../../../tango/hoc/combineHOCs';
import ToggleContent from '../../../../../modules/toggle/fac/Content';
import { namespace } from '../garage-finder/i18n';
import GoogleMap from './Map/Map';
import styles from './Umbrella.css';
import { Suggestions } from './Suggestions';
import { close as closeAction, open as openAction } from './toggle/actions';
import {
	trackUmbrellaSearchClickOnSearchfield,
	trackUmbrellaSearchClickOnPin,
	trackUmbrellaSearchClickOnSearchResult,
} from '../tracking/tracking';
import { Filters } from './Filters';
import { type SelectedTag } from './types';
import { filterLocationByTag } from './utils';
import { getFilter, ORIGIN_PIN } from './constants';
import { getBrand } from '../../config/selectors';
import { Close, Back } from '../svg';

type Props = {
	className?: string,
	foundLocations: any, // ToDo: fix this type
	selectedLocation: ?string,
	query: string,
	onQueryChanged: (query: string) => void,
	onLocationSelected: (id: ?string) => void,
	onLocationClick: (location: Map<string, any>) => void,
	getLocationUrl: (id: string) => ?string,
	linkLabel: string,
	showFilters?: boolean,
	onSalesFiltersChanged: (tags: Array<SelectedTag>) => void,
	onServiceFiltersChanged: (tags: Array<SelectedTag>) => void,
	customMarker?: any,
	controlsClassName?: string,
	salesBrandFilter?: Array<string>,
};

type InternalProps = LocalizeProps & {
	closeTrigger: () => void,
	openTrigger: () => void,
	closeFilters: () => void,
	openFilters: () => void,
};

const TOGGLE_NAME = 'amag/garagefinder/suggestions';
const SALES_FILTER_TOGGLE = 'amag/garagefinder/filters/sales';
const SERVICE_FILTER_TOGGLE = 'amag/garagefinder/filters/service';

const SELECTION_ORIGIN_MAP = 'map';
const SELECTION_ORIGIN_SUGGESTION = 'suggestion';

function GarageFinderComponent(props: Props & InternalProps) {
	const {
		className,
		onQueryChanged,
		onLocationSelected,
		onLocationClick,
		getLocationUrl,
		selectedLocation,
		foundLocations,
		query,
		linkLabel,
		openTrigger,
		closeSalesFilters,
		openSalesFilters,
		closeServiceFilters,
		openServiceFilters,
		locale,
		t,
		onSalesFiltersChanged,
		onServiceFiltersChanged,
		customMarker,
		controlsClassName,
		salesBrandFilter,
		config,
		salesFilters,
		serviceFilters,
		showFilters,
	} = props;

	const allowFilters = typeof showFilters !== 'undefined' ? showFilters : true;

	const handleOnLocationSelected = (
		id: ?string,
		origin: typeof SELECTION_ORIGIN_MAP | typeof SELECTION_ORIGIN_SUGGESTION,
	) => {
		onLocationSelected(id);
		if (id) {
			const url = getLocationUrl(id);
			if (url) {
				if (origin === SELECTION_ORIGIN_MAP) {
					trackUmbrellaSearchClickOnPin(url);
				}
				if (origin === SELECTION_ORIGIN_SUGGESTION) {
					trackUmbrellaSearchClickOnSearchResult(url);
				}
			}
		}
	};

	const handleSuggestionLocationSelect = (id: ?string) => {
		handleOnLocationSelected(id, SELECTION_ORIGIN_SUGGESTION);
	};

	const handleMapLocationSelect = (id: ?string) =>
		handleOnLocationSelected(id, SELECTION_ORIGIN_MAP);

	const handleOnSearchInputFocus = () => {
		onLocationSelected(undefined);
		openTrigger();
		trackUmbrellaSearchClickOnSearchfield();
	};

	const getLocationServices = (brands: Object = {}): Array<string> => {
		const activeBrands = Object.keys(brands).filter(key =>
			salesBrandFilter.includes(key),
		);
		const services = activeBrands.reduce(
			(acc: Array<string>, key: string) => [...acc, ...brands[key].services],
			[],
		);

		return services.includes('SERVICE') ? services : [...services, 'SERVICE'];
	};

	const gMapLocations = foundLocations.filter(
		location => location.type === ORIGIN_PIN,
	);

	const newFilters = [...salesFilters, ...serviceFilters];

	const filteredLocations =
		newFilters.length > 0
			? foundLocations
					// eslint-disable-next-line arrow-body-style
					.filter(location => {
						return (
							location.type === ORIGIN_PIN ||
							filterLocationByTag(
								getLocationServices(location.brands),
								newFilters,
							)
						);
					})
			: foundLocations;

	return (
		<div className={classNames(className, styles.garagefinder)}>
			<div className={classNames(styles.controls, controlsClassName)}>
				<div className={styles.searchInputSection}>
					<input
						className={styles.searchInput}
						type="text"
						value={query}
						onChange={event => onQueryChanged(event.target.value)}
						onClick={handleOnSearchInputFocus}
						placeholder={t('placeholder')}
					/>
					{query.length >= 3 && (
						<span
							className={styles.closeSearch}
							onClick={() => {
								onQueryChanged('');
							}}
						>
							<Close />
						</span>
					)}

					<ToggleContent
						name={TOGGLE_NAME}
						openPerDefault={false}
						closeOnClickOutside
					>
						{isOpen => {
							const actualClassName = classNames(styles.suggestions, {
								[styles.suggestionsIsOpen]: query.length >= 3,
								[styles.suggestionsIsClosed]:
									query.length < 3 || isOpen === false,
							});

							return (
								<Suggestions
									className={actualClassName}
									foundLocations={fromJS(filteredLocations)}
									selectedLocation={selectedLocation}
									onLocationSelected={handleSuggestionLocationSelect}
									cities={gMapLocations}
								/>
							);
						}}
					</ToggleContent>
				</div>

				<ToggleContent
					name={SALES_FILTER_TOGGLE}
					openPerDefault={false}
					closeOnClickOutside
				>
					{salesFilterIsOpen => {
						const filterClassName = classNames(styles.filtersContainer, {
							[styles.filterIsOpen]: salesFilterIsOpen,
						});
						return (
							allowFilters && (
								<div className={styles.filterSectionFront}>
									<button
										onClick={
											salesFilterIsOpen ? closeSalesFilters : openSalesFilters
										}
										className={classNames(styles.filtersToggle, {
											[styles.active]: salesFilterIsOpen,
										})}
									>
										<Back />
										<p>
											<strong>
												{t('sale')} ({salesFilters.length})
											</strong>
										</p>
									</button>
									<div className={filterClassName}>
										<Filters
											onChange={onSalesFiltersChanged}
											config={config[0]}
										/>
									</div>
								</div>
							)
						);
					}}
				</ToggleContent>
				<ToggleContent
					name={SERVICE_FILTER_TOGGLE}
					openPerDefault={false}
					closeOnClickOutside
				>
					{serviceFilterIsOpen => {
						const filterClassName = classNames(styles.filtersContainer, {
							[styles.filterIsOpen]: serviceFilterIsOpen,
						});
						return (
							allowFilters && (
								<div className={styles.filterSection}>
									<button
										onClick={
											serviceFilterIsOpen
												? closeServiceFilters
												: openServiceFilters
										}
										className={classNames(styles.filtersToggle, {
											[styles.active]: serviceFilterIsOpen,
										})}
									>
										<Back />
										<p>
											<strong>
												{t('service')} ({serviceFilters.length})
											</strong>
										</p>
									</button>
									<div className={filterClassName}>
										<Filters
											onChange={onServiceFiltersChanged}
											config={config[1]}
										/>
									</div>
								</div>
							)
						);
					}}
				</ToggleContent>
			</div>

			<div className={styles.map}>
				<GoogleMap
					locations={fromJS(filteredLocations)}
					selectedLocation={selectedLocation}
					onLocationSelected={handleMapLocationSelect}
					onLocationClick={onLocationClick}
					linkLabel={linkLabel}
					getLocationUrl={getLocationUrl}
					locale={locale}
					customMarker={customMarker}
				/>
			</div>
		</div>
	);
}

function mapDispatchToProps(dispatch: Function) {
	return {
		closeTrigger: () => dispatch(closeAction(TOGGLE_NAME)),
		openTrigger: () => dispatch(openAction(TOGGLE_NAME)),
		closeSalesFilters: () => dispatch(closeAction(SALES_FILTER_TOGGLE)),
		openSalesFilters: () => dispatch(openAction(SALES_FILTER_TOGGLE)),
		closeServiceFilters: () => dispatch(closeAction(SERVICE_FILTER_TOGGLE)),
		openServiceFilters: () => dispatch(openAction(SERVICE_FILTER_TOGGLE)),
	};
}

const withHocs = combineHOCs([
	connect(
		(state: ReduxState) => ({
			config: getFilter(getBrand(state)),
		}),
		mapDispatchToProps,
	),
	localize(namespace),
	withStyles(styles),
]);

export const GarageFinder = (withHocs(
	GarageFinderComponent,
): ReactComponent<any>);
