/* @flow */
import React from 'react';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { Map } from 'immutable';

import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import combineHOCs from '../../../../../tango/hoc/combineHOCs';
import { namespace } from '../garage-finder/i18n';
import { ORIGIN_PIN } from '../garage-finder/constants';
import styles from './Suggestions.css';
import PartnerPin from '../svg/PartnerPin';
import { getName } from './utils';

type Props = {
	location: Map<string, any>,
	selectedLocation: ?string,
	onLocationSelected: (id: ?string) => void,
};

function Suggestion(props: Props & LocalizeProps) {
	const {
		location,
		selectedLocation,
		onLocationSelected,
		locale,
		t,
	} = props;

	const name = getName(location, locale);
	const id = location.get('id');

	if (location.get('type') === ORIGIN_PIN) {
		return (
			<li className={classNames(styles.result, styles.noAmagHere)} >
				{t('noAmagHereYet')}
			</li>
		);
	}

	return (
		<li
			className={classNames(styles.result, {
				[styles.selected]: id === selectedLocation,
			})}
		>
			<button
				type="button"
				onClick={() => onLocationSelected(id)}
				className={styles.button}
			>
				<div className={styles.image}>
					<PartnerPin />
				</div>
				<div className={styles.innerButton}>
					<strong>{name}</strong>
				</div>
			</button>
		</li>
	);
}

const SuggestionEnhanced = (combineHOCs([
	localize(namespace),
	withStyles(styles),
])(Suggestion): ReactComponent<Props>);

export {
	SuggestionEnhanced as Suggestion,
};
