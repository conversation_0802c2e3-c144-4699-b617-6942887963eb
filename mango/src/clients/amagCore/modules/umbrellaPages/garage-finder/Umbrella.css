@import theme(amagUmbrella/garageFinder);
@import '../../../../../../node_modules/normalize.css/normalize.css';
@import theme(amagCoreGarageFinder/master);

:global {
	& #onetrust-consent-sdk,
	& #onetrust-banner-sdk {
		font-family: var(--font-text);
	}
}

.garagefinder {
	@custom-media --screen-mobile (max-width: 640px);
	font-family: var(--font-text);

	width: 100%;
	position: relative;
	margin: auto;
	z-index: 1;

	& .map {
		height: var(--amag-core-garagefinder-map-height, 75vh);
	}

	& .wrapper {
		max-width: 1300px;
		margin: auto;
	}

	& .controls {
		background: var(--white);
		box-sizing: border-box;
		display: flex;
		gap: 40px;

		width: 100%;
		box-shadow: 0 17px 45px 0 color(var(--white) alpha(30%));
		padding: 30px 40px 40px;
		flex-wrap: wrap;
		max-width: 100%;

		@media (--screen-mobile) {
			flex-wrap: wrap;
			top: 20px;
			left: 20px;
			max-width: 100%;
			gap: 20px;
			padding: 20px;
		}

		& .searchInputSection {
			position: relative;
			display: flex;
			border: 1px solid #aaa;
			align-items: center;

			box-sizing: border-box;
			outline: none;
			text-overflow: ellipsis;
			hyphens: auto;
			height: unset;
			font-size: unset;
			line-height: 1.15;
			border: 1px solid #E5E8ED;
			border-radius: 42px;
			z-index: 100;
			width: var(--amag-core-garagefinder-input-width);
			padding: var(--amag-core-garagefinder-input-padding, 0);
		}

		& .searchInput {
			all: unset;
			flex: 1;
			border-radius: 0;
			padding-left: 40px;
			background: url("../svg/search.svg") no-repeat;
			background-position: 5% center;
			background-size: 16px;
			transition: all 0.3s ease;
			color: var(--black);
			position: relative;
			z-index: 65;
			width: 90%;
			min-height: 50px;

			@media (--screen-phablet) {
				width: 100%;
				max-width: 100%;
			}

			&::placeholder {
				color: var(--skoda-gray-100, #7c7d7e);
			}

			font-weight: var(--amagUmbrella-font-weight);
			font-family: var(--font-regular);
			font-size: var(--amagUmbrella-strong-font-size);
			line-height: var(--amagUmbrella-strong-line-height);
		}

		& .closeSearch {
			padding: 0px 20px;
			cursor: pointer;
		}

		& .searchIcon {
			padding-left: 20px;
		}

		& .searchInput::placeholder {
			color: var(--black);
		}

		& .filtersToggle {
			width: var(--amag-core-garagefinder-filter-width);
			height: var(--amag-core-garagefinder-filter-height);
			border-radius: 42px;
			border: solid 1px #E5E8ED;
			outline: none;
			cursor: pointer;
			transition: all 0.3s ease;
			background: none;
			margin: 0;
			display: flex;
			align-items: center;
			padding: 0px 20px;
			gap: 10px;
			position: relative;
			z-index: 2;

			&.active {
				background: var(--white);

				& svg {
					transform: rotate(90deg);
				}
			}

			& svg {
				transform: rotate(-90deg);
			}

			& label {
				color: var(--black);
			}

			& label:hover {
				cursor: pointer;
			}

			max-width: var(--amag-core-garagefinder-filter-width);
			max-height: var(--amag-core-garagefinder-filter-height);

			& p {
				color: var(--medium-gray);
		
				& strong {
					color: var(--black);
					font-weight: var(--amagUmbrella-font-weight);
					font-family: var(--font-regular);
					font-size: var(--amagUmbrella-strong-font-size);
					line-height: var(--amagUmbrella-strong-line-height);
				}
			}
		}

		& .filtersContainer {
			transition: all 0.5s ease;
			max-height: 0;
			overflow: hidden;
			width: var(--amag-core-garagefinder-filter-width);
			box-shadow: 0 17px 45px 0 color(var(--black) alpha(30%));
			display: none;

			@media (--screen-mobile) {
				max-width: calc(100% - 40px);
			}

			&.filterIsOpen {
				padding: 60px 0px 10px 0px;
				display: block;
				position: absolute;
				border-top: 1px solid var(--amag-core-garagefinder-suggestions-bottom-border-bottom-color);
				max-height: 450px;
				width: var(--amag-core-garagefinder-filter-width);
				z-index: 1;
				border-radius: 25px;
				background-color: #fff;

				top: 0px;
			}
		}

		& .suggestions {
			transition: none;
			box-shadow: 0 17px 45px 0 color(var(--black) alpha(30%));
			transition: all 0.3s ease;
			max-height: 450px;
			background: var(--white);
			z-index: -1;

			border-radius: 25px;
			overflow: auto;
			position: absolute;
			top: calc(5px + 100%);

			@media (--screen-mobile) {
				max-height: 0;
			}

			&.suggestionsIsOpen {
				max-height: 450px;
				display: block;
				margin-top: -5px;

				@media (--screen-mobile) {
					max-height: 434px;
				}
			}

			&.suggestionsIsClosed {
				display: none;
			}

			& ul {
				list-style: none;
				margin: 0;
				padding: 0;
				overflow: auto;
				max-height: 400px;
			}

			& li {
				& button {
					outline: none;
					border-radius: unset;
				}
			}
		}
	}

	& .filterSection {
		position: relative;
	}

	& .filterSectionFront {
		position: relative;
		z-index: 20;
	}

	@apply --amagCoreUmbrella;
}
