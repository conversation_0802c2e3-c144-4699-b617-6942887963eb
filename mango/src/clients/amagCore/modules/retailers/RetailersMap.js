/* @flow */
import React from 'react';
import { Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { connect } from 'react-redux';
import { is, fromJS, List, Map } from 'immutable';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import { localize, type LocalizeProps } from '../../../../modules/i18n';
import { withAsyncReduxData } from '../../../../tango/withAsyncReduxData';
import { GarageFinder } from '../umbrellaPages/garage-finder/GarageFinder';
import styles from './RetailersMap.css';
import {
	GEOLOCATE_USER,
	ORIGIN_PIN,
} from '../umbrellaPages/garage-finder/constants';
import { proximitySearch } from '../umbrellaPages/garage-finder/proximitySearch';
import { findDealerGroupByLabelWithDealersAction } from '../../../amagAdmin/modules/dealer/actions';
import {
	getCurrentDealersLocationsByTag,
	getCurrentDealersLocationByTagIsCompleted,
	getCurrentDealersLocationByTagIsFetching,
} from '../dealer/selectors';
import {
	makeSortByName,
	prepareLocationsForSearch,
	hasLocationRequiredBrands,
	initializeSearch,
} from '../umbrellaPages/garage-finder/util';
import {
	filterLocationByTag,
	getLocationUrl,
} from '../umbrellaPages/garage-finder/utils';
import { infoDev } from '../../../../tango/logger';

type SelectedTag = {
	label: string,
	tags: Array<string>,
};

type Props = {
	className?: ?string,
	brand: string,
	label: string,
	prefilledSearch?: ?string,
	locationTags: Array<string>,
	salesBrandFilter?: Array<string>,
	serviceBrandFilter?: Array<string>,
	customLocationFilter?: (location: Map<string, any>) => boolean,
	onLocationClick?: (location: Map<string, any>) => void,
	linkLabel: string,
};

type InternalProps = LocalizeProps & {
	locations: List<Map<string, any>>,
	isCompleted: boolean,
	isFetching: boolean,
	get: () => void,
};

type State = {
	locations: Array<any>,
	foundLocations: Array<any>,
	selectedLocation: ?string,
	query: string,
	userLocation?: Object,
	filters: Array<SelectedTag>,
};

class RetailersMap extends React.PureComponent {
	props: Props & InternalProps;
	state: State;
	search: any;
	debouncedSearch: any;
	sortByName: (locationA: Object, locationB: Object) => number;

	constructor(props: Props & InternalProps) {
		super(props);
		const { locale, prefilledSearch } = props;

		this.state = {
			locations: [],
			foundLocations: [],
			selectedLocation: undefined,
			query: prefilledSearch || '',
			userLocation: undefined,
			filters: [],
		};

		this.debouncedSearch = new Subject();

		this.initialiseSearch();

		this.sortByName = makeSortByName(locale);
		if (
			GEOLOCATE_USER &&
			window &&
			window.navigator &&
			window.navigator.geolocation
		) {
			window.navigator.geolocation.getCurrentPosition(position => {
				this.setState({
					userLocation: {
						lat: position.coords.latitude,
						lng: position.coords.longitude,
					},
				});
			});
		}
	}

	componentDidMount() {
		this.debouncedSearch.pipe(debounceTime(200)).subscribe(this.handleSearch);
	}

	componentWillReceiveProps(nextProps: Props & InternalProps) {
		const nextIsCompleted = nextProps.isCompleted;
		const prevIsCompleted = this.props.isCompleted;
		const nextIsFetching = nextProps.isFetching;
		const prevIsFetching = this.props.isFetching;
		const nextsalesBrandFilter = nextProps.salesBrandFilter;
		const prevsalesBrandFilter = this.props.salesBrandFilter;
		const nextserviceBrandFilter = nextProps.serviceBrandFilter;
		const prevserviceBrandFilter = this.props.serviceBrandFilter;

		if (
			!prevIsCompleted &&
			nextIsCompleted &&
			prevIsFetching &&
			!nextIsFetching &&
			nextProps.locations
		) {
			const preparedLocations = this.prepareLocations(nextProps);
			this.setState({
				locations: preparedLocations,
				foundLocations: preparedLocations.sort(this.sortByName),
			});
			this.search.addDocuments(preparedLocations);
		}

		if (
			!is(fromJS(nextsalesBrandFilter), fromJS(prevsalesBrandFilter)) ||
			!is(fromJS(nextserviceBrandFilter), fromJS(prevserviceBrandFilter))
		) {
			const preparedLocations = this.prepareLocations(nextProps);

			this.initialiseSearch();
			this.search.addDocuments(preparedLocations);
			this.setState({ locations: preparedLocations }, () =>
				this.handleSearch(this.state.query),
			);
		}
	}

	initialiseSearch = () => {
		const { locale } = this.props;
		this.search = initializeSearch(locale);
	};

	prepareLocations = (props: Props & InternalProps) => {
		const {
			locations,
			salesBrandFilter,
			serviceBrandFilter,
			customLocationFilter = () => true,
			t,
		} = props;

		const preparedLocations = prepareLocationsForSearch(
			locations
				.filter(loc => Boolean(loc.get('contact-info')))
				.filter(customLocationFilter)
				.filter(loc =>
					hasLocationRequiredBrands(loc, salesBrandFilter, serviceBrandFilter),
				),
			t,
		).toJS();
		return preparedLocations;
	};

	handleSearch = (query: ?string) => {
		const foundLocations = query
			? this.search.search(query)
			: this.state.locations;
		// if foundLocations.length === 0 then do google search
		if (foundLocations.length === 0 && query) {
			// adding a postfix will restrict the search better than setting a region
			proximitySearch(query, this.state.locations, undefined, this.props.locale)
				.then(locations => this.setState({ foundLocations: locations }))
				.catch(() => this.setState({ foundLocations: [] }));
		} else {
			const sortedFoundLocations = foundLocations.sort(this.sortByName);
			this.setState({ foundLocations: sortedFoundLocations });
		}
	};

	updateQuery = (query: string) => {
		this.setState({ query, selectedLocation: undefined });
		this.debouncedSearch.next(query);
	};

	updateSelectedLocation = (id: ?string) => {
		this.setState({ selectedLocation: id });
	};

	getLoctionById = (id: string) =>
		this.state.foundLocations.find(loc => loc.id === id);

	render() {
		const {
			onLocationClick,
			isFetching = true,
			isCompleted = false,
			className = '',
		} = this.props;

		const { foundLocations, selectedLocation, query, filters } = this.state;

		const getLocationServices = (location: Map<any, any>): Array<string> => {
			const activeBrands = location
				.get('dealer-brand-websites')
				.filter(brand => brand.get('activated'))
				.keySeq()
				.toList();

			const brands = location.get('brands', Map());

			return activeBrands.reduce(
				(acc: Array<string>, brand: string) => [
					...acc,
					...brands.getIn([brand, 'services'], []),
				],
				[],
			);
		};

		const filteredLocations = foundLocations.filter(
			location =>
				location.type === ORIGIN_PIN ||
				filterLocationByTag(getLocationServices(fromJS(location)), filters),
		);

		infoDev(
			'locations vs filtered',
			foundLocations.length,
			filteredLocations.length,
		);

		if (isFetching && !isCompleted) {
			return (
				<div className={styles.loaderContainer}>
					<div className={styles.loader} />
				</div>
			);
		}

		return (
			<div className={classNames(styles.mapContainer, className)}>
				<GarageFinder
					query={query}
					foundLocations={fromJS(filteredLocations)}
					selectedLocation={selectedLocation}
					onQueryChanged={this.updateQuery}
					onLocationSelected={this.updateSelectedLocation}
					onLocationClick={(location: Map<string, any>) => {
						if (onLocationClick && typeof onLocationClick === 'function') {
							onLocationClick(location);
						}
					}}
					getLocationUrl={(id: string) =>
						getLocationUrl(fromJS(this.getLoctionById(id)))
					}
					onFiltersChanged={newFilters =>
						this.setState({ filters: newFilters })
					}
					linkLabel={''}
					salesFilters={[]}
					serviceFilters={[]}
					showFilters={false}
				/>
			</div>
		);
	}
}

function mapStateToProps(state: ReduxState, { brand }: Props) {
	return {
		locations: getCurrentDealersLocationsByTag(
			state,
			`umbrella_${brand}`,
			brand,
		),
		isCompleted: getCurrentDealersLocationByTagIsCompleted(state),
		isFetching: getCurrentDealersLocationByTagIsFetching(state),
	};
}

function mapDispatchToProps(dispatch: Function, props: Props) {
	return {
		get: () => dispatch(findDealerGroupByLabelWithDealersAction(props.label)),
	};
}

function requestData({ get }: Props & InternalProps) {
	get();
}

function isDone({ isCompleted }: Props & InternalProps) {
	return isCompleted;
}

export default combineHOCs([
	withAsyncReduxData(requestData, isDone, mapStateToProps, mapDispatchToProps),
	connect(
		mapStateToProps,
		mapDispatchToProps,
	),
	localize(),
	withStyles(styles),
])(RetailersMap);
