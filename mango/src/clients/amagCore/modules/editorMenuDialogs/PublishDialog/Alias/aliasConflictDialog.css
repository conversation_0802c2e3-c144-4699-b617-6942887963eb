@import theme(editorMenu/publishDialog);

.conflictModal {
	font-family: var(--editorMenu-publishDialog-fontFamily);
	font-size: var(--editorMenu-publishDialog-fontSize);
	line-height: var(--editorMenu-publishDialog-lineHeight);

	background: white;
	width: 50%;
	transform: translateX(50%);
	top: 100px;
	padding: 20px;
	position: relative;
	border-radius: 5px;
}

.inputContainer {
	margin-top: 10px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.input {
	width: 85%;
	display: inline-block;
}

.aliasStatus {
	width: 15%;
	display: inline-block;
	text-align: right;
}

.buttonWrapper {
	margin-top: 10px;
	display: flex;
	justify-content: center;
}

.conflictModalButton {
	margin: 0 10px;
	display: inline-block;
	max-width: 100%;

	&:first-child {
		margin-left: 0;
	}

	&:last-child {
		margin-right: 0;
	}
}