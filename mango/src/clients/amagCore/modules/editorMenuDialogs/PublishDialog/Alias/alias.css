@import theme(editorMenu/publishDialog);

.container {
	padding: 10px 0;
}

.twoColumnContainer {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.leftColumn {
	width: 85%;
	display: inline-block;
}

.rightColumn {
	width: 15%;
	display: inline-block;
	text-align: right;
}

.deleteButton {
	background: var(--editorMenu-publishDialog-deleteButtonIcon) no-repeat center var(--editorMenu-publishDialog-deleteButtonIconBackground);
	display: inline-block;
	text-indent: -9999px;
	height: 32px;
	width: 32px;
	border-radius: 100%;
	cursor: pointer;
	margin: 0;
	padding: 0;
	vertical-align: sub;

	&:hover, &.active {
		background: var(--editorMenu-publishDialog-deleteButtonIcon) no-repeat center var(--editorMenu-publishDialog-hoverDeleteButtonIconBackground);
	}
}

.urlInput {
	margin: 15px 0 0 0;
	@apply --editorMenu-publishDialog-input;
}