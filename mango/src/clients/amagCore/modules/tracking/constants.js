/* @flow */
export const DEALER_SEARCH_ACTION_PREFIX = 'dealerSearch';
export const CONTACT_FORM_ACTION_PREFIX = 'contactform';
export const SERVICE_APPOINTMENT_FORM_ACTION_PREFIX = 'serviceappointmentform';

export const DEALER_SEARCH_CLICK_ON_SEARCHFIELD_ACTION = `${DEALER_SEARCH_ACTION_PREFIX}-clickOnSearchfield`;
export const DEALER_SEARCH_CLICK_ON_SEARCH_RESULT_ACTION = `${DEALER_SEARCH_ACTION_PREFIX}-clickOnSearchResult`;
export const DEALER_SEARCH_CLICK_ON_PIN_ACTION = `${DEALER_SEARCH_ACTION_PREFIX}-clickOnPin`;
export const DEALER_SEARCH_CLICK_ON_CTA_ACTION = `${DEALER_SEARCH_ACTION_PREFIX}-clickOnCTA`;
export const DEALER_SEARCH_NO_RESULTS_ACTION = `${DEALER_SEARCH_ACTION_PREFIX}-noResults`;

export const CONTACT_FORM_SUBMITTED_ACTION = `${CONTACT_FORM_ACTION_PREFIX}-submitted`;
export const CONTACT_FORM_SUBMISSION_FAILED_ACTION = `${CONTACT_FORM_ACTION_PREFIX}-submissionFailed`;

export const SERVICE_APPOINTMENT_FORM_SUBMITTED_ACTION = `${SERVICE_APPOINTMENT_FORM_ACTION_PREFIX}-submitted`;
export const SERVICE_APPOINTMENT_FORM_SUBMISSION_FAILED_ACTION = `${SERVICE_APPOINTMENT_FORM_ACTION_PREFIX}-submissionFailed`;

export const SERVICE_APPOINTMENT_LINK_CONVERSION_ID = '';

export const AUDI_SERVICE_APPOINTMENT_LINK_CONVERSION_ID = 'AW-834924361/QEzRCNy5kYwBEMnej44D';

export const GTM_CLASS_MAPPING = {
	configurator: 'gtm_configurator',
	testdrive: 'gtm_testDrive',
	testDrive: 'gtm_testDrive',
	'test-drive': 'gtm_testDrive',
	usedcars: 'gtm_usedCars',
	usedCars: 'gtm_usedCars',
	'used-cars': 'gtm_usedCars',
	newcars: 'gtm_newCars',
	newCars: 'gtm_newCars',
	'new-cars': 'gtm_newCars',
	webshop: 'gtm_webShop',
	webShop: 'gtm_webShop',
	damageReport: 'gtm_damageReport',
};

export function getGtmClass(key?: string) {
	if (!key) {
		return '';
	}

	return GTM_CLASS_MAPPING[key];
}
