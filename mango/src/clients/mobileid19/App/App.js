/* @flow */
import React from 'react';
import { Helmet } from 'react-helmet-async';
import { List } from 'immutable';
import { Switch, Route, withRouter } from 'react-router-dom';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { connect } from 'react-redux';
import { RoutingProvider, Routes, Redirect } from '../../../tango/routing';
import guessLocale from '../../../tango/client-locale/guessLocale';
import type { RouteProps } from '../../../tango/routing/types';
import combineHOCs from '../../../tango/hoc/combineHOCs';
import { LocaleProvider } from '../../../modules/i18n';
import { getClientLocales } from '../../../modules/config/selectors';
import { phrases } from '../assets/phrases';
import g from './globals.css';
import routingConfig from './routingConfig';
import Footer from '../modules/Footer';
import { HtmlLocaleHelmet } from '../../../modules/helmet/HtmlLocaleHelmet';
import { CookieDisclaimer } from '../modules/CookieDisclaimer/CookieDisclaimer';
import { ResultModal } from '../modules/DashboardProcess/ResultModal/ResultModal';
import { CancelRequestedLoginImmediately } from '../../../modules/auth0/CancelRequestedLoginImmediately';
import { ScrollToTop } from '../modules/ScrollToTop';
import { LocalizedRedirectWithQueryParams } from './LocalizedRedirectWithQueryParams';
import { RestoreExistingLogin } from './RestoreExistingLogin';
import { DataProtection } from '../views/DataProtection/DataProtection';
import { getIsMobileAppIntegration } from '../views/MyMobileId/utils';

type Props = {
	nonce: string,
	clientLocales: List<string>,
};

const appDefaultLocale = 'de';
export const availableLocales = ['de', 'fr', 'it', 'en'];
const helmetLink = [
	{
		rel: 'icon',
		type: 'image/x-icon',
		href:
			'https://res.cloudinary.com/deep-impact-ag/image/upload/v1505130595/mobileid/favicon.ico',
	},
	{
		rel: 'icon',
		href:
			'https://res.cloudinary.com/deep-impact-ag/image/upload/v1506635413/mobileid/android-icon.png',
		sizes: '192x192',
	},
	{
		rel: 'apple-touch-icon-precomposed',
		href:
			'https://res.cloudinary.com/deep-impact-ag/image/upload/v1506635413/mobileid/ios-icon.png',
	},
];

function App({ nonce, clientLocales }: Props) {
	const defaultLocale = guessLocale(
		availableLocales,
		clientLocales,
		appDefaultLocale,
	);
	return (
		<React.Fragment>
			<RoutingProvider config={routingConfig} />
			<CancelRequestedLoginImmediately />
			<RestoreExistingLogin />
			<Switch>
				<Route
					exact
					path="/"
					render={() => (
						<Redirect to="home" params={{ locale: defaultLocale }} />
					)}
				/>
				<Route
					exact
					path="/app-privacy-policy"
					render={() => (
						<LocaleProvider
							routerLocale="en"
							defaultLocale="en"
							availableLocales={availableLocales}
							phrases={phrases}
						>
							<DataProtection />
						</LocaleProvider>
					)}
				/>
				<Route
					exact
					path="/privacy-policy"
					render={() => (
						<LocaleProvider
							routerLocale="en"
							defaultLocale="en"
							availableLocales={availableLocales}
							phrases={phrases}
						>
							<DataProtection />
						</LocaleProvider>
					)}
				/>
				{/* This redirect is required to enable the start of specific workflows in the mobileid dashboard.
				Check the redirects in the tenant config.*/}
				<Route
					exact
					path="/login"
					render={() => (
						<LocalizedRedirectWithQueryParams
							path="/login"
							guessedLocale={defaultLocale}
						/>
					)}
				/>
				<Route
					exact
					path="/sim-order"
					render={() => (
						<Redirect to="sim-order" params={{ locale: defaultLocale }} />
					)}
				/>
				<Route
					exact
					path="/help"
					render={() => (
						<Redirect to="faq" params={{ locale: defaultLocale }} />
					)}
				/>
				<Route
					exact
					path="/recove"
					render={() => (
						<Redirect to="faq" params={{ locale: defaultLocale }} />
					)}
				/>
				<Route
					exact
					path="/postfinance"
					render={() => (
						<Redirect to="postfinance" params={{ locale: defaultLocale }} />
					)}
				/>
				<Route
					exact
					path="/business"
					render={() => (
						<Redirect to="businessClients" params={{ locale: defaultLocale }} />
					)}
				/>
				<Route
					path="/:locale"
					render={({ match, location }: RouteProps) => (
						<LocaleProvider
							routerLocale={match.params.locale}
							defaultLocale={defaultLocale}
							availableLocales={availableLocales}
							phrases={phrases}
						>
							<div>
								<HtmlLocaleHelmet />
								<Helmet defaultTitle="Mobile ID" link={helmetLink} />
								<ScrollToTop />
								<ResultModal />
								<Routes config={routingConfig} />
								{!getIsMobileAppIntegration(location.search) && (
									<CookieDisclaimer nonce={nonce} />
								)}
								{!getIsMobileAppIntegration(location.search) && <Footer />}
							</div>
						</LocaleProvider>
					)}
				/>
			</Switch>
		</React.Fragment>
	);
}

function mapStateToProps(state: State) {
	return {
		clientLocales: getClientLocales(state),
	};
}

const withHOCs = combineHOCs([
	withRouter,
	connect(mapStateToProps),
	withStyles(g),
]);

export default withHOCs(App);
