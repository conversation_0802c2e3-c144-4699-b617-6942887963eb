/* @flow */
import React from 'react';
import { Redirect } from 'react-router';

import AboutUs from '../views/AboutUs';
import ActivationContactProvider from '../views/Activation/ContactProvider';
import BusinessClients from '../views/BusinessClients';
import Clients from '../views/Clients';
import Contact from '../views/Contact';
import { Documents } from '../views/Documents';
import Error404 from '../views/Error404';
import Faq from '../views/Faq';
import { HealthCheck } from '../views/HealtCheck/HealthCheck';
import Home from '../views/Home';
import { LoginContainer } from '../views/Login/LoginContainer';
import { MyMobileIdContainer } from '../views/MyMobileId/MyMobileIdContainer';
import { Test } from '../views/MyMobileId/Test';
import { NewWorld } from '../views/NewWorld';
import Partners from '../views/Partners';
import PostFinance from '../views/PostFinance';
import Press from '../views/Press';
import PrivateClients from '../views/PrivateClients';
import SimOrder from '../views/SimOrder';
import { Styleguide } from '../views/Styleguide/Styleguide';

import { type RouteProps } from '../../../tango/routing';

export const contactRoute = {
	name: 'contact',
	path: {
		de: '/:locale/kontakt',
		en: '/:locale/contact',
		fr: '/:locale/contact',
		it: '/:locale/contatti',
	},
	render: () => <Contact routeName="contact" />,
};

// /app-privacy-policy and /privacy-policy (and other routes without /:locale) are handled in App.js
const routingConfig: Array<Object> = [
	{
		name: 'styleguide',
		path: '/:locale/styleguide',
		component: Styleguide,
		noindex: true,
	},
	{
		name: 'home',
		path: '/:locale',
		render: () => <Home routeName="home" />,
	},
	{
		name: 'status',
		path: '/:locale/status',
		component: HealthCheck,
	},
	{
		name: 'privateClients',
		path: {
			de: '/:locale/privatkunden',
			en: '/:locale/residential-customers',
			fr: '/:locale/clients-prives',
			it: '/:locale/clienti-privati',
		},
		render: () => <PrivateClients routeName="privateClients" />,
	},
	{
		name: 'businessClients',
		path: {
			de: '/:locale/geschaeftskunden',
			en: '/:locale/business-customers',
			fr: '/:locale/clients-commerciaux',
			it: '/:locale/clienti-commerciali',
		},
		render: () => <BusinessClients routeName="businessClients" />,
	},
	{
		name: 'partners',
		path: {
			de: '/:locale/partner',
			en: '/:locale/partners',
			fr: '/:locale/partenaires',
			it: '/:locale/partner',
		},
		render: () => <Partners routeName="partners" />,
	},
	{
		name: 'aboutus',
		path: {
			de: '/:locale/ueber-uns',
			en: '/:locale/about-us',
			fr: '/:locale/about-us',
			it: '/:locale/about-us',
		},
		render: () => <AboutUs routeName="aboutus" />,
	},
	{
		name: 'press',
		path: {
			de: '/:locale/presse',
			en: '/:locale/press',
			fr: '/:locale/communication-avec-la-presse',
			it: '/:locale/stampa',
		},
		render: () => <Press routeName="press" />,
	},
	contactRoute,
	{
		name: 'faq',
		path: {
			de: '/:locale/faq',
			en: '/:locale/faq',
			fr: '/:locale/faq',
			it: '/:locale/faq',
		},
		render: () => <Faq routeName="faq" />,
	},
	{
		name: 'sim-order',
		path: {
			de: '/:locale/sim-bestellen',
			en: '/:locale/sim-order',
			fr: '/:locale/commander-sim',
			it: '/:locale/comessa-sim',
		},
		render: () => <SimOrder routeName="sim-order" />,
	},
	{
		name: 'clients',
		path: {
			de: '/:locale/kunden',
			en: '/:locale/clients',
			fr: '/:locale/clients',
			it: '/:locale/clienti',
		},
		render: () => <Clients routeName="clients" />,
	},
	{
		name: 'activation.contact-provider',
		path: {
			de: '/:locale/aktivierung/error',
			en: '/:locale/activation/error',
			fr: '/:locale/activation/error',
			it: '/:locale/attivazione/error',
		},
		noindex: true,
		render: () => <ActivationContactProvider />,
	},
	{
		name: 'postfinance',
		path: {
			de: '/:locale/pf1',
			en: '/:locale/pf1',
			fr: '/:locale/pf1',
			it: '/:locale/pf1',
		},
		noindex: true,
		render: () => <PostFinance routeName="postfinance" />,
	},
	{
		name: 'postfinance2',
		path: {
			de: '/:locale/pf2',
			en: '/:locale/pf2',
			fr: '/:locale/pf2',
			it: '/:locale/pf2',
		},
		noindex: true,
		render: () => <PostFinance part2 routeName="postfinance2" />,
	},
	{
		name: 'newWorld',
		path: {
			de: '/:locale/neue-möglichkeiten',
			en: '/:locale/new-possibilities',
			fr: '/:locale/possibilités-nouvelles',
			it: '/:locale/nuove-possibilità',
		},
		render: () => <NewWorld routeName="newWorld" />,
	},
	{
		name: 'documents',
		path: {
			de: '/:locale/dokumente',
			en: '/:locale/documents',
			fr: '/:locale/documents',
			it: '/:locale/documenti',
		},
		noindex: true,
		render: () => <Documents routeName="documents" />,
	},
	{
		name: 'login',
		path: {
			de: '/:locale/login',
			en: '/:locale/login',
			fr: '/:locale/login',
			it: '/:locale/login',
		},
		render: ({ location }: RouteProps) => <LoginContainer routeName="login" search={location.search} />,
	},
	{
		name: 'myMobileId',
		path: {
			de: '/:locale/my-mobile-id',
			en: '/:locale/my-mobile-id',
			fr: '/:locale/my-mobile-id',
			it: '/:locale/my-mobile-id',
		},
		render: () => <MyMobileIdContainer routeName="myMobileId" />,
	},
	{
		name: 'dataProtection',
		path: {
			de: '/:locale/datenschutz-app',
			en: '/:locale/datenschutz-app',
			fr: '/:locale/datenschutz-app',
			it: '/:locale/datenschutz-app',
		},
		render: () => <Redirect to="/app-privacy-policy" />,
	},
	{
		name: 'test',
		path: {
			de: '/:locale/test',
			en: '/:locale/test',
			fr: '/:locale/test',
			it: '/:locale/test',
		},
		render: () => <Test routeName="test" />,
	},
	{
		name: 'error.not-found',
		component: Error404,
		noindex: true,
	},
];

export default routingConfig;
