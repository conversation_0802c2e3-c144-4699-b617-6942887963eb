/* @flow */
import React from 'react';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import { localize, LocalizeProps } from '../../../../modules/i18n';
import styles from './ProgressIndicator.css';
import { namespace } from './i18n';
import { getIsMobileAppIntegration } from '../../views/MyMobileId/utils';

type OwnProps = {
	className?: string,
	steps: Array<any>,
	activeStep: number,
};

function ProgressIndicator(props: OwnProps & LocalizeProps) {
	const { className, steps = [], activeStep = 1, t } = props;

	const stepsCount = steps.length;
	const isMobileApp = getIsMobileAppIntegration();
	if (isMobileApp) {
		return null;
	}
	return (
		<div className={classNames(styles.outerWrapper, className)}>
			<div className={styles.baseLine}>
				<div
					className={classNames(
						styles.progressIndicator,
						styles[`_${activeStep}_${stepsCount}`],
					)}
				/>
			</div>
			<ul className={classNames(styles.descriptionContainer)}>
				{steps.map((step, index) => (
					<li key={`prog_${index}`} className={styles[`_${stepsCount}`]}>
						{t(step)}
					</li>
				))}
			</ul>
		</div>
	);
}

const withHOCs = combineHOCs([withStyles(styles), localize(namespace)])(
	ProgressIndicator,
);

export { withHOCs as ProgressIndicator };
