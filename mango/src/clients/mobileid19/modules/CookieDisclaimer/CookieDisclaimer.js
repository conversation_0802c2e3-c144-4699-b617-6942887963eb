import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { withCookies, Cookies } from 'react-cookie';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import combineHocs from '../../../../tango/hoc/combineHOCs';
import { localize } from '../../../../modules/i18n';
import type { LocalizeProps } from '../../../../modules/i18n/types';
import { namespace } from './i18n';
import styles from './CookieDisclaimer.css';
import {
	COOKIE_MAX_AGE,
	CONSENT_ACCEPT_PERFORMANCE_COOKIES,
	DISCLAIMER_ACCEPTED_COOKIE,
	COOKIE_DISCLAIMER_CURRENT_VERSION,
} from './constants';
import Button, { THEME_GHOST, THEME_SECONDARY } from '../Button';
import ExternalLink from '../../../../modules/external-link';
import { CookieModal, COOKIE_MODAL_NAME } from './CookieModal';
import { close, open } from '../../../../modules/modal/actions';
import GTM from '../../../../modules/googleTagManager';

type OwnProps = { nonce: string };

type CookieProps = {
	cookies?: Cookies,
};

type ConnectedProps = {
	openModal: Function,
};

function CookieDisclaimerComponent({
	cookies,
	openModal,
	closeModal,
	t,
	nonce,
}: OwnProps & CookieProps & LocalizeProps & ConnectedProps) {
	const [display, setDisplay] = React.useState(false);

	useEffect(() => {
		if (cookies) {
			const version = cookies && cookies.get(DISCLAIMER_ACCEPTED_COOKIE);
			if (version !== COOKIE_DISCLAIMER_CURRENT_VERSION) {
				setDisplay(true);
			}
		}
	}, [cookies]);

	const acceptAllCookies = () => {
		setDisplay(false);
		cookies.set(CONSENT_ACCEPT_PERFORMANCE_COOKIES, true, {
			path: '/',
			maxAge: COOKIE_MAX_AGE,
		});
		cookies.set(DISCLAIMER_ACCEPTED_COOKIE, COOKIE_DISCLAIMER_CURRENT_VERSION, {
			path: '/',
			maxAge: COOKIE_MAX_AGE,
		});
	};

	const handleClickEdit = () => {
		openModal(COOKIE_MODAL_NAME);
		setDisplay(false);
	};

	const savePerformanceCookies = (acceptPerformanceCookies: Boolean) => {
		cookies.set(CONSENT_ACCEPT_PERFORMANCE_COOKIES, acceptPerformanceCookies, {
			path: '/',
			maxAge: COOKIE_MAX_AGE,
		});

		cookies.set(DISCLAIMER_ACCEPTED_COOKIE, COOKIE_DISCLAIMER_CURRENT_VERSION, {
			path: '/',
			maxAge: COOKIE_MAX_AGE,
		});

		closeModal(COOKIE_MODAL_NAME);
	};

	const performaceCookiesAccepted = cookies.get(
		CONSENT_ACCEPT_PERFORMANCE_COOKIES,
	);

	return (
		<React.Fragment>
			<CookieModal
				cookies={cookies}
				savePerformanceCookies={savePerformanceCookies}
			/>
			{performaceCookiesAccepted === 'true' && (
				<GTM nonce={nonce} id="GTM-NGWTXNF" />
			)}
			{display && (
				<div className={styles.cookieDisclaimerOuterContainer}>
					<div className={styles.innerContainer}>
						<div className={styles.paragraphContainer}>
							<p>{t('text')}</p>
							<ExternalLink
								href={t('privacyStatementLink')}
								className={styles.privacyLink}
								application
							>
								{t('privacyStatement')}
							</ExternalLink>
						</div>
						<div className={styles.btnContainer}>
							<Button
								className={styles.editButton}
								theme={THEME_GHOST}
								onClick={handleClickEdit}
							>
								{t('edit')}
							</Button>
							<Button
								className={styles.acceptButton}
								theme={THEME_SECONDARY}
								onClick={acceptAllCookies}
							>
								{t('acceptAll')}
							</Button>
						</div>
					</div>
				</div>
			)}
		</React.Fragment>
	);
}

function mapDispatchToProps(dispatch: Function) {
	return {
		openModal: name => dispatch(open(name)),
		closeModal: name => dispatch(close(name)),
	};
}

const withHocs = combineHocs([
	connect(
		undefined,
		mapDispatchToProps,
	),
	localize(namespace),
	withStyles(styles),
	withCookies,
]);

export const CookieDisclaimer = (withHocs(
	CookieDisclaimerComponent,
): ReactComponent<EmptyProps>);
