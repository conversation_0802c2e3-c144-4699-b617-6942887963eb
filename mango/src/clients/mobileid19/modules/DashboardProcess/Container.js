/* @flow */
import React from 'react';
import { connect } from 'react-redux';
import { DashboardProcess } from './DashboardProcess';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import {
	getProcessStateData,
	getProcessState,
	getProcessOptions,
	getProcessName,
	getProgressIndicatorSteps,
	getQrCodeUrl,
	getPossibleRecoveryMethods,
} from '../mobileid-processes/selectors';
import {
	processOptionsChanged,
	returnedToPreviousStep,
	processFinished, processStateChanged,
} from '../mobileid-processes/actions';
import { getPhoneNumber, getProfile } from '../mobileid-session/selectors';
import { rcProvided } from '../mobileid-processes/recovery-code/actions';
import type { ProcessOptions } from '../mobileid-processes/types';

export type ChildProps = {
	processName?: string,
	processState?: string,
	processStateData?: Object,
	processOptions?: ProcessOptions,
	changeProcessOptions: Function,
	changeProcessState: Function,
	profile?: Object,
	provideRecoveryCode: Function,
	returnToPreviousStep: Function,
	indicatorSteps?: Array<string>,
	qrCodeUrl?: string,
	possibleRecoveryMethods?: Array<string>,
	finishProcess: Function,
	phoneNumber?: string,
};

type Props = ChildProps;

function ContainerComponent({
	processName,
	processState,
	processStateData,
	processOptions,
	changeProcessOptions,
	profile,
	provideRecoveryCode,
	returnToPreviousStep,
	indicatorSteps,
	qrCodeUrl,
	possibleRecoveryMethods,
	finishProcess,
	phoneNumber,
	changeProcessState,
}: Props) {
	return (
		<DashboardProcess
			processName={processName}
			processState={processState}
			processStateData={processStateData}
			profile={profile}
			processOptions={processOptions}
			changeProcessOptions={changeProcessOptions}
			provideRecoveryCode={provideRecoveryCode}
			returnToPreviousStep={returnToPreviousStep}
			indicatorSteps={indicatorSteps}
			qrCodeUrl={qrCodeUrl}
			possibleRecoveryMethods={possibleRecoveryMethods}
			finishProcess={finishProcess}
			phoneNumber={phoneNumber}
			changeProcessState={changeProcessState}
		/>
	);
}

function mapStateToProps(state: ReduxState) {
	return {
		processName: getProcessName(state),
		processState: getProcessState(state),
		processStateData: getProcessStateData(state),
		profile: getProfile(state),
		processOptions: getProcessOptions(state),
		indicatorSteps: getProgressIndicatorSteps(state),
		qrCodeUrl: getQrCodeUrl(state),
		possibleRecoveryMethods: getPossibleRecoveryMethods(state),
		phoneNumber: getPhoneNumber(state),
	};
}

function mapDispatchToProps(dispatch: Function) {
	return {
		changeProcessOptions: processOptions =>
			dispatch(processOptionsChanged(processOptions)),
		provideRecoveryCode: (processName, recoveryCode) =>
			dispatch(rcProvided(processName, recoveryCode)),
		returnToPreviousStep: () => dispatch(returnedToPreviousStep()),
		finishProcess: () => dispatch(processFinished()),
		changeProcessState: processState => dispatch(processStateChanged(processState)),
	};
}

const withHOCs = combineHOCs([
	connect(
		mapStateToProps,
		mapDispatchToProps,
	),
]);

const Container = withHOCs(ContainerComponent);

export { Container };
