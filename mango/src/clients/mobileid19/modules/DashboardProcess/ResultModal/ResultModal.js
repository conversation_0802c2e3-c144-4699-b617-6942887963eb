/* @flow */
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import React from 'react';
import classNames from 'classnames';
import { connect } from 'react-redux';
import { withRouter } from 'react-router';
import { localize, LocalizeProps } from '../../../../../modules/i18n';
import Modal from '../../../../../modules/modal';
import combineHOCs from '../../../../../tango/hoc/combineHOCs';
import { IllustrationProcessFailed } from '../../../assets/svg/IllustrationProcessFailed';
import { IllustrationProcessSuccess } from '../../../assets/svg/IllustrationProcessSuccess';
import {
	ProcessState,
	ProcessName,
	METHOD_APP,
} from '../../mobileid-processes/constants';
import {
	getNewRecoveryCode,
	getProcessName,
	getProcessOptions,
	getProcessState,
	getProcessStateData,
} from '../../mobileid-processes/selectors';
import { H4, H6 } from '../../titles';
import { namespace } from '../i18n';
import styles from './ResultModal.css';
import Close from '../../../assets/svg/Close';
import { close, open } from '../../../../../modules/modal/actions';
import { processFinished } from '../../mobileid-processes/actions';
import { getProfile } from '../../mobileid-session/selectors';
import type { TypeProfile } from '../../mobileid-session/constants';
import { parseIssuer } from './util';
import { type RouteProps } from '../../../../../tango/routing';
import { getIsMobileAppIntegration } from '../../../views/MyMobileId/utils';
import { DESIRED_WORKFLOW } from '../../../views/MyMobileId/constants';
import { getHashParam } from '../../../views/Login/utils';
import { MobileEnrollSuccess } from './MobileEnrollSuccess';
import { MobileEnrollTitle } from '../../../views/Login/MobileEnrollTitle';
import { MobileEnrollText } from '../../../views/Login/MobileEnrollText';
import type { ProcessOptions } from '../../mobileid-processes/types';

export const RESULT_MODAL_NAME = 'RESULT_MODAL';

type ConnectedProps = {
	finishProcess: Function,
	closeModal: Function,
	openModal: Function,
	recoveryCode?: string,
	processStateData?: Object,
	processName?: string,
	processState?: string,
	profile: TypeProfile,
	processOptions: ProcessOptions,
};

type Props = ConnectedProps & LocalizeProps & RouteProps;

const prefix = `processes.${ProcessState.Result}`;

function processError(error: Object | string) {
	if (typeof error === 'string') {
		return error.split('/');
	}
	if (error.type) {
		return error.type.split('/');
	}
	return 'unknown';
}

// errors in the whitelist should not be propagated to the url as they do not require a workflow restart
const errorWhitelist = [
	'recovery-code-invalid',
	'recovery-code-too-many-attempts',
];

function getNewState(
	processStateData: Object,
	recoveryCode: string,
	processState: string,
) {
	const hasError = Boolean(processStateData && processStateData.error);
	if (hasError) {
		// shorten error as the common prefix doesn't convey any additional information
		const parts = processError(processStateData.error);
		const error = parts[parts.length - 1];
		if (errorWhitelist.includes(error)) {
			return undefined;
		}

		// any state beginning with `error-` will cause the futurae app to display a restart workflow popup
		return `error-${error.replace('error-', '')}`;
	} else if (
		processState === ProcessState.Result
	) {
		return 'success';
	}
	return undefined;
}

class ResultModalComponent extends React.Component {
	props: Props;

	componentDidUpdate = () => {
		const {
			processStateData,
			history,
			locale,
			recoveryCode,
			processState,
		} = this.props;
		const isMobileApp = getIsMobileAppIntegration();
		if (isMobileApp) {
			const state = getNewState(processStateData, recoveryCode, processState);
			if (state && getHashParam('state') !== state) {
				history.push(
					`/${locale}/my-mobile-id?desired-workflow=${
						DESIRED_WORKFLOW.APP_ENROLL_MOBILE_ONLY
					}#state=${state}`,
				);
			}
		}
	};

	handleClose = () => {
		const { finishProcess, closeModal, history, locale, location } = this.props;
		closeModal(RESULT_MODAL_NAME);
		finishProcess();
		// we redirect to my-mobile-id once test has completed on Test.js
		// we need to use locatin here as match seems to be stuck to /de as the ResultModal is at App level
		if (location.pathname === `/${locale}/test`) {
			history.push(`/${locale}/my-mobile-id`);
		}
	};

	render() {
		const {
			processName,
			processStateData,
			recoveryCode,
			t,
			profile,
			processOptions,
		} = this.props;
		const hasError = Boolean(processStateData && processStateData.error);
		const hasSerialNo = Boolean(processStateData && processStateData.serialNo);
		const hasOldRecoveryCode = profile && profile.hasRecoveryCode;
		const isTestSignatureError =
			hasError && processName === ProcessName.TestSignature;
		const isTextSignatureInfo =
			processName === ProcessName.TestSignature &&
			processOptions &&
			processOptions.method === METHOD_APP;
		const isDeactivation =
			!hasError &&
			(processName === ProcessName.AppDeactivation ||
				processName === ProcessName.SimDeactivation);
		const isRecoveryCodeSuccess =
			recoveryCode &&
			!hasError &&
			!(processStateData && processStateData.hideRecoveryCode);

		const outcome = hasError ? 'error' : 'success';
		const isMobileAppIntegration = getIsMobileAppIntegration();

		if (isMobileAppIntegration && isRecoveryCodeSuccess) {
			return <MobileEnrollSuccess recoveryCode={recoveryCode} />;
		}

		const title = {
			default: t(`${prefix}.${outcome}.${processName}.title`),
			mobileEnroll: t(`${prefix}.${outcome}.${processName}.title`),
		};

		const text = {
			default: t(`${prefix}.${outcome}.${processName}.subtitle`),
			mobileEnroll: t(`${prefix}.${outcome}.${processName}.subtitle`),
		};

		// Only show recovery code in these cases
		const showRecoveryCodeOnDesktop = [
			ProcessName.GenerateRecoveryCode,
			ProcessName.SimActivation,
			ProcessName.ResetPin,
		].includes(processName);

		return (
			<Modal
				className={classNames(styles.modal, {
					[styles.mobileEnroll]: isMobileAppIntegration,
				})}
				overlayClassName={styles.modalOverlay}
				name={RESULT_MODAL_NAME}
				closeOnOverlayClick={false}
				closeTimeoutInMilliseconds={0}
			>
				{!isMobileAppIntegration && (
					<button
						id="STATIC_ID_PROCESSES_RESULT_CLOSE_MODAL"
						className={styles.closeButton}
						onClick={this.handleClose}
						type="button"
					>
						<Close className={styles.closeIcon} />
					</button>
				)}
				{hasError ? (
					<IllustrationProcessFailed />
				) : (
					<IllustrationProcessSuccess />
				)}
				<MobileEnrollTitle title={title} center />
				<MobileEnrollText text={text} center />
				{isTestSignatureError && (
					<React.Fragment>
						<H4 align="center" size="like-h5" secondary>
							{t(`${prefix}.error.${processStateData.error}.title`)}
						</H4>
						<p>{t(`${prefix}.error.${processStateData.error}.subtitle`)}</p>
					</React.Fragment>
				)}
				{isTextSignatureInfo && (
					<p className={styles.appInformation}>
						{t(`${prefix}.appInformation`)}
					</p>
				)}
				{isDeactivation && (
					<React.Fragment>
						<p className={styles.appInformation}>
							{t(`${prefix}.success.${processName}.text1`)}
						</p>
						<p className={styles.appInformation}>
							{t(`${prefix}.success.${processName}.text2`)}
						</p>
					</React.Fragment>
				)}
				{hasSerialNo && (
					<div className={styles.serialNoContainer}>
						<p className={styles.serialNoLabel}>
							{t(`${prefix}.serialNoLabel`)}
						</p>
						<p className={styles.serialNoLabel}>
							{processStateData.serialNo}{' '}
							{t(`${prefix}.issuer`, {
								issuer: parseIssuer(processStateData.issuer),
							})}
						</p>
					</div>
				)}
				{isRecoveryCodeSuccess && showRecoveryCodeOnDesktop && (
					<React.Fragment>
						<div className={styles.informationParagraph}>
							<p>{t(`${prefix}.recoveryCodeText`)}</p>
							<p className={styles.bold}>{t(`${prefix}.recoveryCodeFat`)}</p>
						</div>
						<p className={styles.recoveryCodeInstructions}>
							{t(`${prefix}.recoveryCodeInstruction`)}
						</p>
						{hasOldRecoveryCode && <p>{t(`${prefix}.replacingInformation`)}</p>}

						<div className={styles.recoveryCodeContainer}>
							<p className={styles.recoveryCodeLabel}>
								{t(`${prefix}.recoveryCodeLabel`)}
							</p>
							<div className={styles.innerWrapper}>
								<input
									id="STATIC_ID_RESULT_MODAL_NEW_RECOVERY_CODE_INPUT"
									className={styles.recoveryCode}
									readOnly
									type="text"
									value={recoveryCode}
								/>
							</div>
						</div>
						<div className={styles.printQuestions}>
							<H6 align="center">{t(`${prefix}.question1`)}</H6>
							<p>{t(`${prefix}.answer1`)}</p>
							<H6 align="center">{t(`${prefix}.question2`)}</H6>
							<p>{t(`${prefix}.answer2`)}</p>
						</div>
					</React.Fragment>
				)}
			</Modal>
		);
	}
}

function mapStateToProps(state: ReduxState) {
	return {
		recoveryCode: getNewRecoveryCode(state),
		processStateData: getProcessStateData(state),
		processName: getProcessName(state),
		processState: getProcessState(state),
		processOptions: getProcessOptions(state),
		profile: getProfile(state),
	};
}

function mapDispatchToProps(dispatch: Function) {
	return {
		finishProcess: () => dispatch(processFinished()),
		closeModal: name => dispatch(close(name)),
		openModal: name => dispatch(open(name)),
	};
}

const withHOCs = combineHOCs([
	withRouter,
	connect(
		mapStateToProps,
		mapDispatchToProps,
	),
	localize(namespace),
	withStyles(styles),
]);

const ResultModal = withHOCs(ResultModalComponent);

export { ResultModal };
