/* @flow */
import React from 'react';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import classNames from 'classnames';
import { localize, LocalizeProps } from '../../../../../modules/i18n';
import combineHOCs from '../../../../../tango/hoc/combineHOCs';
import styles from './DeactivateMethod.css';
import { namespace } from '../i18n';
import Button, { THEME_PRIMARY, THEME_SECONDARY } from '../../Button';
import { H3, H2, H4 } from '../../titles';
import { ContinueButton } from '../../ProgressButton';
import { ProcessState } from '../../mobileid-processes/constants';
import { ErrorMessage } from '../../ErrorMessage';
import { Tooltip } from '../../Form/Tooltip';
import type { ProcessOptions } from '../../mobileid-processes/types';

type Props = {
	returnToPreviousStep: Function,
	changeProcessOptions: Function,
	processName?: string,
	processOptions?: ProcessOptions,
	profile?: Object,
};

type State = {
	deactivationConfirmed: boolean,
	error?: string,
	showWarning: boolean,
	recoveryConfirmed: boolean,
};

const ERROR_NO_CONFIRMATION = 'ERROR_NO_CONFIRMATION';
const prefix = `processes.${ProcessState.DeactivationConfirmation}`;

class DeactivateMethodComponent extends React.Component {
	props: Props & LocalizeProps;
	state: State = {
		deactivationConfirmed: false,
		recoveryConfirmed: false,
		error: undefined,
		showWarning: false,
	};

	componentDidMount = () => {
		window.scrollTo(0, 0);
	};

	handleOnContinue = () => {
		const { deactivationConfirmed, recoveryConfirmed } = this.state;
		if (deactivationConfirmed && recoveryConfirmed) {
			this.setState({ showWarning: true, error: undefined });
		} else {
			this.setState({ error: ERROR_NO_CONFIRMATION });
		}
	};

	handleOnBack = () => {
		this.setState({
			showWarning: false,
			deactivationConfirmed: false,
			recoveryConfirmed: false,
			error: undefined,
		});
	};

	handleOnFinish = () => {
		const { changeProcessOptions, processOptions } = this.props;
		const { deactivationConfirmed } = this.state;

		if (!deactivationConfirmed) {
			this.setState({ error: ERROR_NO_CONFIRMATION });
		} else {
			changeProcessOptions({ ...processOptions, deactivationConfirmed });
		}
	};

	handleOnDeactivatonConfirmed = (event) => {
		if (event && event.target) {
			this.setState({
				deactivationConfirmed: Boolean(event.target.checked),
				error: undefined,
			});
		}
	};

	handleOnRecoveryConfirmed = (event) => {
		if (event && event.target) {
			this.setState({
				recoveryConfirmed: Boolean(event.target.checked),
				error: undefined,
			});
		}
	};

	render() {
		const { t, returnToPreviousStep, processName, profile } = this.props;
		const { error, showWarning } = this.state;

		const hasRecoveryCode: boolean = profile && profile.hasRecoveryCode;

		if (showWarning) {
			return (
				<div>
					<H2 margin="huge">{t(processName)}</H2>
					<H3 margin="medium" size="like-h4" secondary>
						{t(`${prefix}.subtitle.${processName}`)}
					</H3>
					<H4 className={styles.warningTitle}>
						{t(`${prefix}.warningTitle.${processName}`)}
					</H4>
					<p>{t(`${prefix}.warningText.${processName}`)}</p>
					<p>{t(`${prefix}.warningQuestion.${processName}`)}</p>
					{error && (
						<ErrorMessage>
							{t(`${prefix}.errors.ERROR_NO_CONFIRMATION`)}
						</ErrorMessage>
					)}
					<div className={styles.buttonContainer}>
						<Button
							id="STATIC_ID_PROCESSES_DEACTIVATE_METHOD_BACK"
							theme={THEME_SECONDARY}
							onClick={this.handleOnBack}
						>
							{t('back')}
						</Button>
						<Button
							id="STATIC_ID_PROCESSES_DEACTIVATE_METHOD_DELETE"
							className={styles.deleteButton}
							theme={THEME_PRIMARY}
							onClick={this.handleOnFinish}
						>
							{t('continue')}
						</Button>
					</div>
				</div>
			);
		}

		return (
			<div>
				<H2 margin="huge">{t(processName)}</H2>
				<H3 margin="medium" size="like-h4" secondary>
					{t(`${prefix}.subtitle.${processName}`)}
				</H3>
				<p>{t(`${prefix}.warning.${processName}`)}</p>
				<div className={styles.termsContainer}>
					<input
						id="STATIC_ID_PROCESSES_DEACTIVATE_METHOD_CONFIRMATION1"
						type="checkbox"
						onChange={this.handleOnDeactivatonConfirmed}
					/>
					<p>{t(`${prefix}.confirmationText.${processName}`)}</p>
				</div>
				<div className={classNames(styles.termsContainer, styles.bigMargin)}>
					<input
						id="STATIC_ID_PROCESSES_DEACTIVATE_METHOD_CONFIRMATION2"
						type="checkbox"
						onChange={this.handleOnRecoveryConfirmed}
					/>
					<p>
						{t(`${prefix}.confirmationText2`)}
						<span className={styles.recoveryHighlight}>
							{t(`${prefix}.recoveryCodeHighlight`)}
							<Tooltip
								className={styles.tooltipButton}
								classNameTooltip={styles.tooltip}
								tooltip={t(
									`${prefix}.tooltipText.${
										hasRecoveryCode ? 'hasRecoveryCode' : 'hasNoRecoveryCode'
									}`,
								)}
								customIcon={<span className={styles.invisibleTooltipButton} />}
							/>
						</span>

						{t(`${prefix}.confirmationText3`)}
					</p>
				</div>
				{error && (
					<ErrorMessage>
						{t(`${prefix}.errors.ERROR_NO_CONFIRMATION`)}
					</ErrorMessage>
				)}
				<div className={styles.buttonContainer}>
					<Button
						id="STATIC_ID_PROCESSES_DEACTIVATE_METHOD_ABORT"
						theme={THEME_SECONDARY}
						onClick={returnToPreviousStep}
						className={styles.backButton}
					>
						{t('back')}
					</Button>
					<ContinueButton
						staticId="STATIC_ID_PROCESSES_DEACTIVATE_METHOD_CONTINUE"
						buttonText={t('continue')}
						onContinue={this.handleOnContinue}
					/>
				</div>
			</div>
		);
	}
}

const withHOCs = combineHOCs([localize(namespace), withStyles(styles)]);

const DeactivateMethod = (withHOCs(
	DeactivateMethodComponent,
): ReactComponent<Props>);

export { DeactivateMethod };
