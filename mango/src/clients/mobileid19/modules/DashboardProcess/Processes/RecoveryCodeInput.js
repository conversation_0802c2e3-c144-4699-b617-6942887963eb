/* @flow */
import React from 'react';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { localize, LocalizeProps } from '../../../../../modules/i18n';
import combineHOCs from '../../../../../tango/hoc/combineHOCs';
import styles from './RecoveryCodeInput.css';
import { namespace } from '../i18n';
import Button, { THEME_SECONDARY } from '../../Button';
import { ContinueButton } from '../../ProgressButton';
import { BoxInput } from '../../BoxInput';
import { ProcessState } from '../../mobileid-processes/constants';
import { ErrorMessage } from '../../ErrorMessage';
import { Tooltip } from '../../Form/Tooltip';
import { MobileEnrollTitle } from '../../../views/Login/MobileEnrollTitle';
import { getIsMobileAppIntegration } from '../../../views/MyMobileId/utils';
import { MobileEnrollText } from '../../../views/Login/MobileEnrollText';

type Props = {
	processName?: string,
	processStateData?: Object,
	provideRecoveryCode: Function,
	returnToPreviousStep: Function,
};

type State = { recoveryCode: string, error?: string };

const ERROR_RECOVERY_CODE_INVALID = 'ERROR_RECOVERY_CODE_INVALID';

class RecoveryCodeInputComponent extends React.Component {
	props: Props & LocalizeProps;
	state: State = {
		recoveryCode: '',
		error: undefined,
	};

	componentDidMount = () => {
		window.scrollTo(0, 0);
	};

	handleRecoveryCodeChange = (newValue) => {
		this.setState({ recoveryCode: newValue, error: undefined });
	};

	handleOnContinue = () => {
		const { processName, provideRecoveryCode } = this.props;
		const { recoveryCode } = this.state;

		if (recoveryCode.length === 12) {
			const actualRecoveryCode = [
				recoveryCode.substring(0, 4),
				recoveryCode.substring(4, 8),
				recoveryCode.substring(8, 12),
			]
				.join('-')
				.toUpperCase();

			provideRecoveryCode(processName, actualRecoveryCode);
		} else {
			this.setState({ error: ERROR_RECOVERY_CODE_INVALID });
		}
	};

	handleOnKeyDownOnLastInputField = (event) => {
		if (event.key === 'Enter') {
			this.handleOnContinue();
		}
	};

	render() {
		const {
			processName,
			t,
			returnToPreviousStep,
			processStateData,
		} = this.props;
		const { recoveryCode, error } = this.state;

		const requestError = processStateData && processStateData.error;
		const isMobileAppIntegration = getIsMobileAppIntegration();

		const title = {
			default: t(processName),
			mobileEnroll: t('recovery'),
		};

		const text = {
			default: (
				<span className={styles.tooltipOuterText}>
					{t(`processes.${ProcessState.RecoveryCodeInput}.subtitle`)}
					<Tooltip
						className={styles.tooltipButton}
						classNameTooltip={styles.tooltip}
						tooltip={t(`processes.${ProcessState.RecoveryCodeInput}.tooltip`)}
					/>
				</span>
			),
			mobileEnroll: (
				<span className={styles.tooltipOuterText}>
					{t(`processes.${ProcessState.RecoveryCodeInput}.subtitle`)}
					<Tooltip
						className={styles.tooltipButton}
						classNameTooltip={styles.tooltip}
						tooltip={t(`processes.${ProcessState.RecoveryCodeInput}.tooltip`)}
					/>
				</span>
			),
		};

		const mobileAppIntegrationStyles = {
			[styles.mobileAppIntegrationContainer]: isMobileAppIntegration,
		};

		return (
			<div className={classNames(mobileAppIntegrationStyles)}>
				<MobileEnrollTitle title={title} />
				<MobileEnrollText text={text} />

				<BoxInput
					staticId="STATIC_ID_PROCESSES_RECOVERY_INPUT_INPUT"
					amountOfChars={12}
					currentString={recoveryCode}
					onChange={this.handleRecoveryCodeChange}
					autoFocus
					onKeyDownOnLastInputField={this.handleOnKeyDownOnLastInputField}
				/>

				{(error || requestError) && (
					<ErrorMessage className={styles.error}>
						{t(
							`processes.${ProcessState.RecoveryCodeInput}.errors.${error ||
							requestError}`,
						)}
					</ErrorMessage>
				)}
				<div className={styles.buttonContainer}>
					<Button
						id="STATIC_ID_PROCESSES_RECOVERY_INPUT_BACK"
						theme={THEME_SECONDARY}
						onClick={returnToPreviousStep}
					>
						{t('back')}
					</Button>
					<ContinueButton
						staticId="STATIC_ID_PROCESSES_RECOVERY_INPUT_CONTINUE"
						buttonText={t('continue')}
						onContinue={this.handleOnContinue}
						fetching={processStateData && processStateData.isFetching}
					/>
				</div>
			</div>
		);
	}
}

const withHOCs = combineHOCs([localize(namespace), withStyles(styles)]);

const RecoveryCodeInput = (withHOCs(
	RecoveryCodeInputComponent,
): ReactComponent<Props>);

export { RecoveryCodeInput };
