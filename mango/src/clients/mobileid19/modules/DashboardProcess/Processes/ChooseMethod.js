import React, { useEffect, useMemo } from 'react';
import { connect } from 'react-redux';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import classNames from 'classnames';

import { localize, LocalizeProps } from '../../../../../modules/i18n';
import combineHOCs from '../../../../../tango/hoc/combineHOCs';
import styles from './ChooseMethod.css';
import { namespace } from '../i18n';
import Button, { THEME_SECONDARY } from '../../Button';
import {
	METHOD_SIM,
	METHOD_APP,
	ProcessState,
	DesiredProcessName,
} from '../../mobileid-processes/constants';
import { ContinueButton } from '../../ProgressButton';
import { ResponsiveImage } from '../../../../../modules/files';
import { ErrorMessage } from '../../ErrorMessage';
import { getProfile, getCountry } from '../../mobileid-session/selectors';
import type { TypeProfile } from '../../mobileid-session/constants';
import LinkButton, { THEME_PRIMARY } from '../../LinkButton';
import { MobileEnrollTitle } from '../../../views/Login/MobileEnrollTitle';
import { MobileEnrollText } from '../../../views/Login/MobileEnrollText';
import type { ProcessOptions } from '../../mobileid-processes/types';
import { chooseMethodImgSize, imgApp, imgSim } from './constants';
import { ConfirmAppReset } from './ConfirmAppReset';

type ConnectedProps = {
	profile: TypeProfile,
};

type Props = {
	processName?: string,
	processOptions?: ProcessOptions,
	changeProcessOptions: Function,
	returnToPreviousStep: Function,
};

const ERROR_NO_METHOD = 'ERROR_NO_METHOD';

function getTranslations(desiredProcess: string, t: Function) {
	const title = {
		default: t(desiredProcess),
		mobileEnroll: t(desiredProcess),
	};

	const text = {
		default: t(
			`processes.${ProcessState.ChooseMethod}.subtitle.${desiredProcess}`,
		),
		mobileEnroll: t(
			`processes.${ProcessState.ChooseMethod}.subtitle.${desiredProcess}`,
		),
	};
	return {
		title,
		text,
	};
}

function getShouldShowConfirmation(
	profile: TypeProfile,
	processOptions: ProcessOptions,
) {
	return (
		profile.appStatus === 'ACTIVE' &&
		processOptions.desiredProcess === DesiredProcessName.Activation
	);
}

// workaround to display confirmation screen for non-CH numbers with active APP
function getShouldShowInitialConfirmation(country, profile, processOptions) {
	const isSwissNumber: boolean = country && country === 'CH';
	return !isSwissNumber && getShouldShowConfirmation(profile, processOptions);
}

function ChooseMethodComponent({
	changeProcessOptions,
	processOptions,
	returnToPreviousStep,
	country,
	t,
	profile,
}: Props & LocalizeProps & ConnectedProps) {
	const [method, setMethod] = React.useState(undefined);
	const [error, setError] = React.useState(undefined);
	const [showConfirmation, setShowConfirmation] = React.useState(
		getShouldShowInitialConfirmation(country, profile, processOptions),
	);

	useEffect(() => {
		window.scrollTo(0, 0);
	}, []);

	const handleOnContinue = () => {
		if (!method) {
			setError(ERROR_NO_METHOD);
		} else if (
			method === METHOD_APP &&
			getShouldShowConfirmation(profile, processOptions)
		) {
			setShowConfirmation(true);
		} else {
			changeProcessOptions({ ...processOptions, method });
		}
	};

	const { desiredProcess } = processOptions;
	const isSimCompatible = profile && profile.isSimCompatible;
	const { title, text } = useMemo(() => getTranslations(desiredProcess, t), [
		desiredProcess,
		t,
	]);

	function cancelConfirmation() {
		if (getShouldShowInitialConfirmation(country, profile, processOptions)) {
			returnToPreviousStep();
		} else {
			setShowConfirmation(false);
		}
	}

	if (showConfirmation) {
		return (
			<ConfirmAppReset
				processOptions={processOptions}
				changeProcessOptions={changeProcessOptions}
				setShowConfirmation={cancelConfirmation}
				title={title}
			/>
		);
	}

	return (
		<div>
			<MobileEnrollTitle title={title} />
			<MobileEnrollText text={text} />
			<div className={styles.imgButtonContainer}>
				<button
					id="STATIC_ID_PROCESSES_CHOOSE_METHOD_SIM"
					onClick={() => setMethod(METHOD_SIM)}
					className={classNames([
						styles.button,
						{ [styles.buttonActive]: method === METHOD_SIM },
					])}
				>
					<ResponsiveImage
						className={styles.chooseImg}
						image={imgSim}
						sizes={chooseMethodImgSize}
					/>
					<p>{t('sim')}</p>
				</button>
				<button
					id="STATIC_ID_PROCESSES_CHOOSE_METHOD_APP"
					onClick={() => setMethod(METHOD_APP)}
					className={classNames([
						styles.button,
						{ [styles.buttonActive]: method === METHOD_APP },
					])}
				>
					<ResponsiveImage
						className={styles.chooseImg}
						image={imgApp}
						sizes={chooseMethodImgSize}
					/>
					<p>{t('app')}</p>
				</button>
			</div>
			{method && (
				<p>
					{t(`processes.${ProcessState.ChooseMethod}.information.${method}`)}
				</p>
			)}
			{error && (
				<ErrorMessage>
					{t(`processes.${ProcessState.ChooseMethod}.errors.ERROR_NO_METHOD`)}
				</ErrorMessage>
			)}
			<div className={styles.buttonContainer}>
				<Button
					id="STATIC_ID_PROCESSES_CHOOSE_METHOD_BACK"
					theme={THEME_SECONDARY}
					onClick={returnToPreviousStep}
				>
					{t('back')}
				</Button>
				{method === METHOD_SIM && !isSimCompatible ? (
					<LinkButton
						className={styles.linkButton}
						to="sim-order"
						theme={THEME_PRIMARY}
					>
						{t(`processes.${ProcessState.ChooseMethod}.orderSim`)}
					</LinkButton>
				) : (
					<ContinueButton
						staticId="STATIC_ID_PROCESSES_CHOOSE_METHOD_CONTINUE"
						buttonText={t('continue')}
						onContinue={handleOnContinue}
					/>
				)}
			</div>
		</div>
	);
}

function mapStateToProps(state: ReduxState) {
	return {
		profile: getProfile(state),
		country: getCountry(state),
	};
}

const withHOCs = combineHOCs([
	connect(mapStateToProps),
	localize(namespace),
	withStyles(styles),
]);

const ChooseMethod = (withHOCs(ChooseMethodComponent): ReactComponent<Props>);

export { ChooseMethod };
