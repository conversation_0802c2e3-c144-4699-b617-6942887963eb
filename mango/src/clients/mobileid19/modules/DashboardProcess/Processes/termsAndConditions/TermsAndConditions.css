@import '../../../../assets/variables.css';

.buttonContainer {
	display: flex;
	justify-content: flex-end;
	flex-basis: 100%;
	margin-top: 32px;

	& .backButton {
		font-size: 12px;
		text-transform: uppercase;
		height: 50px;
		margin-right: 24px;

		@media (--screen-mobile) {
			margin-right: 0;
		}
	}
}

h5 {
	margin-top: 12px;
}

.termsContainer {
	display: box;
	margin-bottom: 12px;

	& p {
		display: inline;
		color: black;
	}

	& .termsLink {
		display: inline;

		& p {
			color: var(--primary-color)
		}
	}
}

.videoLink {
	padding: 30px 0 0;
	text-align: center;

	& a {
		font-size: 12px;
		line-height: 21px;
		text-align: center;
		text-decoration-line: underline;
		color: var(--primary-color)
	}
}
