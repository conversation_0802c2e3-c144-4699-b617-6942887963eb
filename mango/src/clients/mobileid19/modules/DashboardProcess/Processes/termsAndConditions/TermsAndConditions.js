/* @flow */
import React from 'react';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { localize, LocalizeProps } from '../../../../../../modules/i18n';
import combineHOCs from '../../../../../../tango/hoc/combineHOCs';
import styles from './TermsAndConditions.css';
import { namespace } from '../../i18n';
import Button, { THEME_SECONDARY } from '../../../Button';
import { ContinueButton } from '../../../ProgressButton';
import {
	ProcessState,
	METHOD_APP,
	RECOVERY_METHOD_NO_RECOVERY,
} from '../../../mobileid-processes/constants';
import ExternalLink from '../../../../../../modules/external-link';
import { ErrorMessage } from '../../../ErrorMessage';
import { getIsMobileAppIntegration } from '../../../../views/MyMobileId/utils';
import { MobileEnrollTitle } from '../../../../views/Login/MobileEnrollTitle';
import { MobileEnrollText } from '../../../../views/Login/MobileEnrollText';
import { AppLinks } from '../../../../views/MyMobileId/tiles/AppLinks';
import type { ProcessOptions } from '../../../mobileid-processes/types';

type Props = {
	returnToPreviousStep: Function,
	processOptions?: ProcessOptions,
	changeProcessOptions: Function,
	phoneNumber?: string,
};

type State = { termsAndConditionsAccepted: boolean, error?: string };

const ERROR_TERMS_NOT_ACCEPTED = 'ERROR_TERMS_NOT_ACCEPTED';

class TermsAndConditionsComponent extends React.Component {
	props: Props & LocalizeProps;
	state: State = {
		termsAndConditionsAccepted: false,
		error: undefined,
	};

	componentDidMount = () => {
		window.scrollTo(0, 0);
		const { method } = this.props.processOptions;
		if (getIsMobileAppIntegration()) {
			this.props.changeProcessOptions({
				...this.props.processOptions,
				termsAndConditionsAccepted: true,
			});
		} else if (method === METHOD_APP) {
			this.props.changeProcessOptions({
				desiredProcess: 'Activation',
				method: METHOD_APP,
				recoveryMethod: RECOVERY_METHOD_NO_RECOVERY,
				termsAndConditionsAccepted: true,
			});
			this.props.changeProcessState('TermsAndConditions');
		}
	};

	handleOnContinue = () => {
		const { changeProcessOptions, processOptions } = this.props;
		const { termsAndConditionsAccepted } = this.state;

		if (!termsAndConditionsAccepted) {
			this.setState({ error: ERROR_TERMS_NOT_ACCEPTED });
		} else {
			changeProcessOptions({
				...processOptions,
				termsAndConditionsAccepted: true,
			});
		}
	};

	handleOnChange = (event) => {
		if (event && event.target) {
			this.setState({
				termsAndConditionsAccepted: Boolean(event.target.checked),
				error: undefined,
			});
		}
	};

	render() {
		const {
			t,
			returnToPreviousStep,
			processOptions = {},
		} = this.props;
		const { error } = this.state;
		const { desiredProcess, method } = processOptions;
		const prefix = `processes.${ProcessState.TermsAndConditions}`;
		const termsHref = t(`${prefix}.termsAndConditionsLink.${method}`);
		const privacyHref = t(`${prefix}.termsAndConditionsLink2.${method}`);

		const title = {
			default: t(desiredProcess),
			mobileEnroll: t(desiredProcess),
		};

		const text = {
			default: t(`${prefix}.subtitle`),
			mobileEnroll: t(`${prefix}.subtitle`),
		};
		if (method === METHOD_APP) {
			return (
				<div>
					<MobileEnrollTitle title={title} />
					<MobileEnrollText
						text={t(
							'processes.Processing.qrCodeInstructions.generalInstructions.part3',
						)}
					/>
					<AppLinks />
					<div className={styles.videoLink}>
						<a href="https://youtu.be/NAwzy1_HGys" target="_blank" rel="noopener noreferrer">
							{t(
								'processes.Processing.qrCodeInstructions.generalInstructions.watchVideo',
							)}
						</a>
					</div>
					<div className={styles.buttonContainer}>
						<Button
							id="STATIC_ID_PROCESSES_TERMS_AND_CONDITIONS_INPUT_BACK"
							theme={THEME_SECONDARY}
							onClick={returnToPreviousStep}
						>
							{t('back')}
						</Button>
					</div>
				</div>
			);
		}
		return (
			<div>
				<MobileEnrollTitle title={title} />
				<MobileEnrollText text={text} />
				<div className={styles.termsContainer}>
					<input
						id="STATIC_ID_PROCESSES_TERMS_AND_CONDITIONS_CHECKBOX"
						type="checkbox"
						onChange={this.handleOnChange}
					/>
					<p>{t(`${prefix}.termsAndConditions1.${method}`)}</p>

					<ExternalLink className={styles.termsLink} href={termsHref}>
						<p>{t(`${prefix}.termsAndConditionsLinkLabel.${method}`)}</p>
					</ExternalLink>

					<p>{t(`${prefix}.termsAndConditions2.${method}`)}</p>
					{method === METHOD_APP && (
						<React.Fragment>
							<ExternalLink className={styles.termsLink} href={privacyHref}>
								<p>{t(`${prefix}.termsAndConditionsLinkLabel2.${method}`)}</p>
							</ExternalLink>

							<p>{t(`${prefix}.termsAndConditions3.${method}`)}</p>
						</React.Fragment>
					)}
				</div>
				{error && (
					<ErrorMessage>
						{t(`${prefix}.errors.ERROR_TERMS_NOT_ACCEPTED`)}
					</ErrorMessage>
				)}
				<div className={styles.buttonContainer}>
					<Button
						id="STATIC_ID_PROCESSES_TERMS_AND_CONDITIONS_INPUT_BACK"
						theme={THEME_SECONDARY}
						onClick={returnToPreviousStep}
					>
						{t('back')}
					</Button>
					<ContinueButton
						staticId="STATIC_ID_PROCESSES_TERMS_AND_CONDITIONS_INPUT_CONTINUE"
						buttonText={t('continue')}
						onContinue={this.handleOnContinue}
					/>
				</div>
			</div>
		);
	}
}

const withHOCs = combineHOCs([localize(namespace), withStyles(styles)]);

const TermsAndConditions = (withHOCs(
	TermsAndConditionsComponent,
): ReactComponent<Props>);

export { TermsAndConditions };
