@import '../../../assets/variables.css';

.deleteButton {
	background: var(--secondary-color) !important;
	margin-left: 20px;
}

.warningText {
	color: orange;
	margin-bottom: 24px;
}

.termsContainer {
	display: box;
	margin-bottom: 12px;

	&.bigMargin {
		margin-bottom: 32px;
	}

	& p {
		display: inline;
		color: black;
	}

	& .termsLink {
		display: inline;

		& p {
			color: var(--primary-color)
		}
	}
}

.warningTitle {
	margin-top: 12px;
}

.recoveryHighlight {
	color: var(--primary-color);
	text-decoration: underline;
	cursor: pointer;
	position: relative;
}

.tooltipButton {
	position: absolute;
	width: 100%;
	left: 0;
}

.tooltip {
	left: 50% !important;
	transform: translateX(-50%) translateY(-40%) !important;
	top: -12px;

	@media (--screen-mobile) {
		width: 100% !important;
		transform: translateX(-50%) translateY(-60%) !important;
	}

	&:after {
		right: unset !important;
		left: 50% !important;
	}
}

.backButton {
	margin-bottom: 12px;
}