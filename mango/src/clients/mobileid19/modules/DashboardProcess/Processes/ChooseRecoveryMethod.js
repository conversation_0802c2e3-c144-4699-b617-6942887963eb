/* @flow */
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import classNames from 'classnames';
import React from 'react';
import { localize, LocalizeProps } from '../../../../../modules/i18n';
import combineHOCs from '../../../../../tango/hoc/combineHOCs';
import { getIsMobileAppIntegration } from '../../../views/MyMobileId/utils';
import Button, { THEME_SECONDARY } from '../../Button';
import { ErrorMessage } from '../../ErrorMessage';
import {
	RECOVERY_METHOD_NO_RECOVERY,
	RECOVERY_METHOD_RECOVERY_CODE,
	RECOVERY_METHOD_SECOND_METHOD,
	ProcessState,
} from '../../mobileid-processes/constants';
import { PROFILE_TYPE_ACTIVE } from '../../mobileid-session/constants';
import { ContinueButton } from '../../ProgressButton';
import { ProgressSelect } from '../../ProgressSelect/ProgressSelect';
import { H4 } from '../../titles';
import { namespace } from '../i18n';
import styles from './ChooseRecoveryMethod.css';
import { MobileEnrollTitle } from '../../../views/Login/MobileEnrollTitle';
import { MobileEnrollText } from '../../../views/Login/MobileEnrollText';
import type { ProcessOptions } from '../../mobileid-processes/types';

type Props = {
	processName?: string,
	profile?: Object,
	processOptions?: ProcessOptions,
	changeProcessOptions: Function,
	returnToPreviousStep: Function,
	possibleRecoveryMethods?: Array<string>,
};

type State = {
	recoveryMethodToUse?: string,
	showWarning: boolean,
	error?: string,
};

const ERROR_NO_RECOVERY_METHOD = 'ERROR_NO_RECOVERY_METHOD';

class ChooseRecoveryMethodComponent extends React.Component {
	props: Props & LocalizeProps;
	state: State = {
		recoveryMethodToUse: undefined,
		showWarning: false,
		error: undefined,
	};

	componentDidMount = () => {
		window.scrollTo(0, 0);
	};

	handleChangeMethod = (newMethod: string) => {
		this.setState({ recoveryMethodToUse: newMethod, error: undefined });
	};

	getNoRecoveryText = () => {
		const { profile, t } = this.props;
		const { hasRecoveryCode, appStatus } = profile;

		if (appStatus === PROFILE_TYPE_ACTIVE && hasRecoveryCode) {
			return t(
				'processes.ChooseRecoveryMethod.noRecoveryReason.noneOfTheAbove',
			);
		}

		if (appStatus === PROFILE_TYPE_ACTIVE && !hasRecoveryCode) {
			return t('processes.ChooseRecoveryMethod.noRecoveryReason.cannotUseApp');
		}

		if (appStatus !== PROFILE_TYPE_ACTIVE && hasRecoveryCode) {
			return t(
				'processes.ChooseRecoveryMethod.noRecoveryReason.cannotUseRecovery',
			);
		}
		return t('processes.ChooseRecoveryMethod.noRecoveryReason.noneOfTheAbove');
	};

	handleOnContinue = () => {
		const { changeProcessOptions, processOptions } = this.props;
		const { recoveryMethodToUse, showWarning } = this.state;

		if (!recoveryMethodToUse) {
			this.setState({ error: ERROR_NO_RECOVERY_METHOD });
		} else if (
			recoveryMethodToUse === RECOVERY_METHOD_NO_RECOVERY &&
			!showWarning
		) {
			this.setState({ showWarning: true });
		} else {
			changeProcessOptions({
				...processOptions,
				recoveryMethod: recoveryMethodToUse,
			});
		}
	};

	handleOnBack = () => {
		const { returnToPreviousStep } = this.props;
		returnToPreviousStep();
	};

	render() {
		const { processOptions, t, possibleRecoveryMethods = [] } = this.props;
		const { recoveryMethodToUse, showWarning, error } = this.state;
		const { desiredProcess, method } = processOptions;
		const process = getIsMobileAppIntegration() ? 'recovery' : desiredProcess;

		const title = {
			default: t(process),
			mobileEnroll: t(process),
		};

		const text = {
			default: t(`processes.${ProcessState.ChooseRecoveryMethod}.subtitle`),
			mobileEnroll: t(`processes.${ProcessState.ChooseRecoveryMethod}.subtitle`),
		};

		const isMobileAppIntegration = getIsMobileAppIntegration();

		const mobileAppIntegrationStyles = {
			[styles.mobileAppIntegrationContainer]: isMobileAppIntegration,
		};

		if (showWarning) {
			return (
				<div className={classNames(mobileAppIntegrationStyles)}>
					<MobileEnrollTitle title={title} />
					<MobileEnrollText text={text} />
					<ProgressSelect
						staticId="STATIC_ID_PROCESSES_CHOOSE_RECOVERY_METHOD_PROGRESS_WARNING"
						active
						onClick={() => {}}
					>
						<p className={styles.progressSelectText}>
							{this.getNoRecoveryText()}
						</p>
					</ProgressSelect>
					<H4 className={styles.warningTitle}>
						{t(`processes.${ProcessState.ChooseRecoveryMethod}.warning.title`)}
					</H4>
					<p>
						{t(`processes.${ProcessState.ChooseRecoveryMethod}.warning.text`)}
					</p>

					<p>
						{t(
							`processes.${ProcessState.ChooseRecoveryMethod}.warning.question`,
						)}
					</p>
					<div className={styles.buttonContainer}>
						<ContinueButton
							staticId="STATIC_ID_PROCESSES_CHOOSE_RECOVERY_METHOD_CONFIRM"
							className={styles.warningProgressSelect}
							buttonText={t('continue')}
							onContinue={this.handleOnContinue}
						/>
						<Button
							id="STATIC_ID_PROCESSES_CHOOSE_RECOVERY_METHOD_RETURN"
							className={styles.switchedBackButton}
							theme={THEME_SECONDARY}
							onClick={() => this.setState({ showWarning: false })}
						>
							{t('back')}
						</Button>
					</div>
				</div>
			);
		}

		return (
			<div className={classNames(mobileAppIntegrationStyles)}>
				<MobileEnrollTitle title={title} />
				<MobileEnrollText text={text} />
				{possibleRecoveryMethods.includes(RECOVERY_METHOD_RECOVERY_CODE) && (
					<ProgressSelect
						staticId="STATIC_ID_PROCESSES_CHOOSE_RECOVERY_METHOD_CODE"
						active={recoveryMethodToUse === RECOVERY_METHOD_RECOVERY_CODE}
						onClick={() =>
							this.handleChangeMethod(RECOVERY_METHOD_RECOVERY_CODE)
						}
					>
						<p className={styles.progressSelectText}>
							{t(
								`processes.${
									ProcessState.ChooseRecoveryMethod
								}.useRecoveryMethod`,
							)}
						</p>
					</ProgressSelect>
				)}
				{possibleRecoveryMethods.includes(RECOVERY_METHOD_SECOND_METHOD) && (
					<ProgressSelect
						staticId="STATIC_ID_PROCESSES_CHOOSE_RECOVERY_METHOD_SECOND"
						active={recoveryMethodToUse === RECOVERY_METHOD_SECOND_METHOD}
						onClick={() =>
							this.handleChangeMethod(RECOVERY_METHOD_SECOND_METHOD)
						}
					>
						<p className={styles.progressSelectText}>
							{t(
								`processes.${
									ProcessState.ChooseRecoveryMethod
								}.useSecondMethod.${method}`,
							)}
						</p>
					</ProgressSelect>
				)}
				<ProgressSelect
					staticId="STATIC_ID_PROCESSES_CHOOSE_RECOVERY_METHOD_NORECOVERY"
					active={recoveryMethodToUse === RECOVERY_METHOD_NO_RECOVERY}
					onClick={() => this.handleChangeMethod(RECOVERY_METHOD_NO_RECOVERY)}
				>
					<p className={styles.progressSelectText}>
						{this.getNoRecoveryText()}
					</p>
				</ProgressSelect>
				{error && (
					<ErrorMessage className={styles.error}>
						{t(
							`processes.${
								ProcessState.ChooseRecoveryMethod
							}.errors.ERROR_NO_RECOVERY_METHOD`,
						)}
					</ErrorMessage>
				)}
				<div className={styles.buttonContainer}>
					{!getIsMobileAppIntegration() && <Button
						id="STATIC_ID_PROCESSES_CHOOSE_RECOVERY_METHOD_BACK"
						theme={THEME_SECONDARY}
						onClick={this.handleOnBack}
					>
						{t('back')}
					</Button>}
					<ContinueButton
						staticId="STATIC_ID_PROCESSES_CHOOSE_RECOVERY_METHOD_CONTINUE"
						buttonText={t('continue')}
						onContinue={this.handleOnContinue}
					/>
				</div>
			</div>
		);
	}
}

const withHOCs = combineHOCs([localize(namespace), withStyles(styles)]);

const ChooseRecoveryMethod = (withHOCs(
	ChooseRecoveryMethodComponent,
): ReactComponent<Props>);

export { ChooseRecoveryMethod };
