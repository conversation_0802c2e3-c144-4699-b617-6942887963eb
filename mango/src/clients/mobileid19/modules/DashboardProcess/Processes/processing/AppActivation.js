import withStyles from 'isomorphic-style-loader/lib/withStyles';
import React from 'react';
import { withRouter } from 'react-router';
import ExternalLink from '../../../../../../modules/external-link';
import { localize, type LocalizeProps } from '../../../../../../modules/i18n';
import combineHOCs from '../../../../../../tango/hoc/combineHOCs';
import Apple from '../../../../assets/img/logos/apple.svg';
import Play from '../../../../assets/img/logos/play.png';
import { getIsMobileAppIntegration } from '../../../../views/MyMobileId/utils';
import {
	ProcessState,
} from '../../../mobileid-processes/constants';
import { H3 } from '../../../titles';
import { namespace } from '../../i18n';
import styles from '../Processing.css';
import { ProcessingIcon } from '../ProcessingIcon';
import { getDeepLink } from '../util';
import type { RouteProps } from '../../../../../../tango/routing/types';
import { getSearch } from '../../../../views/Login/utils';
import { LoadingDashboardTile } from '../../../../views/MyMobileId/tiles/LoadingDashboardTile';

type Props = {
	processName: string,
	qrCodeUrl?: string,
	phoneNumber: string,
	type: any,
	method: any,
} & LocalizeProps &
	RouteProps;

const processesQRPrefix = `processes.${
	ProcessState.Processing
}.qrCodeInstructions.`;

const processesDLPrefix = `processes.${ProcessState.Processing}.download.`;

class AppActivationComponent extends React.Component {
	props: Props;

	componentDidUpdate = () => {
		const { qrCodeUrl, history, locale } = this.props;
		const deepLink = getDeepLink(qrCodeUrl);
		const isMobileApp = getIsMobileAppIntegration();
		if (isMobileApp) {
			const param = deepLink.substring(18);
			const searchParams = getSearch()
				.split('&')
				.filter(entry => !entry.startsWith('activation_code'))
				.join('&');
			history.push(`/${locale}/my-mobile-id${searchParams}&${param}`);
		}
	};

	render() {
		const { phoneNumber, t, type, processName, method, qrCodeUrl } = this.props;
		const deepLink = getDeepLink(qrCodeUrl);
		const isMobileApp = getIsMobileAppIntegration();

		if (isMobileApp) {
			return (
				<div className={styles.instructionsContainer}>
					<LoadingDashboardTile />
				</div>
			);
		}

		return (
			<div className={styles.instructionsContainer}>
				<H3 margin="medium" size="like-h5" secondary>
					{t(`${processesQRPrefix}generalInstructions.part1`)}
					{`+${phoneNumber}`}
					{t(`${processesQRPrefix}generalInstructions.part2`)}
				</H3>
				<div className={styles.instructionsParent}>
					<div className={styles.instructionsOne}>
						<p className={styles.instructionsTitle}>
							{t(`${processesQRPrefix}step1title`)}
						</p>
						<p className={styles.stepText}>{t(`${processesQRPrefix}step1`)}</p>
						<ExternalLink href={t(`${processesDLPrefix}dowloadLinkGooglePlay`)}>
							<img
								src={Play}
								alt="Android Play Store"
								className={styles.appStoreLogo}
							/>
						</ExternalLink>
						<ExternalLink href={t(`${processesDLPrefix}downloadLinkAppStore`)}>
							<img
								src={Apple}
								alt="Apple App Store"
								className={styles.appStoreLogo}
							/>
						</ExternalLink>
					</div>
					<div className={styles.instructionsTwo}>
						<p className={styles.instructionsTitle}>
							{t(`${processesQRPrefix}step2title`)}
						</p>
						<p className={styles.stepText}>{t(`${processesQRPrefix}step2a`)}</p>
						{type && (
							<ProcessingIcon
								type={type}
								method={method}
								processName={processName}
							/>
						)}
						<div className={styles.orContainer}>
							<div className={styles.orHalfLine} />
							<p className={styles.or}>{t(`${processesQRPrefix}step2b`)}</p>
							<div className={styles.orHalfLine} />
						</div>
						<p>{t(`${processesQRPrefix}step2c`)}</p>
						<a href={deepLink} className={styles.mobileIdLogo}>
							<svg
								className="app-svg"
								viewBox="0 0 105 104"
								fill="none"
								xmlns="http://www.w3.org/2000/svg"
							>
								<path
									d="M90.2 0.399902H15C7.30003 0.399902 0.900024 6.6999 0.900024 14.4999V89.6999C0.900024 97.3999 7.20003 103.8 15 103.8H90.2C97.9 103.8 104.3 97.4999 104.3 89.6999V14.3999C104.3 6.6999 98 0.399902 90.2 0.399902ZM24.4 23.4999C28.9 23.4999 32.6 27.1999 32.6 31.6999C32.6 36.1999 28.9 39.8999 24.4 39.8999C19.9 39.8999 16.2 36.1999 16.2 31.6999C16.2 27.1999 19.9 23.4999 24.4 23.4999ZM55.3 82.9999C44.5 82.9999 34.8 78.6999 27.9 70.7999C23 65.1999 19.7 57.9999 18.4 50.1999C17.7 46.1999 20.8 42.4999 24.9 42.4999C28.2 42.4999 30.9 44.8999 31.4 48.1999C33.5 61.1999 42.7 69.7999 55.3 69.7999C66.6 69.7999 75.8 60.5999 75.8 49.2999C75.8 40.9999 69 34.1999 60.7 34.1999H50V44.9999V45.0999V58.4999V62.8999C43.2 61.8999 36.8 53.5999 36.8 45.1999V20.9999H60.5C76.2 20.7999 89.1 33.5999 89.1 49.2999C89 67.8999 73.9 82.9999 55.3 82.9999Z"
									fill="#009490"
								/>
							</svg>
						</a>
					</div>
				</div>
			</div>
		);
	}
}

const withHOCs = combineHOCs([
	localize(namespace),
	withStyles(styles),
	withRouter,
]);

const AppActivation = withHOCs(AppActivationComponent);

export { AppActivation };
