/* @flow */
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import React from 'react';
import classNames from 'classnames';
import { localize, LocalizeProps } from '../../../../../modules/i18n';
import combineHOCs from '../../../../../tango/hoc/combineHOCs';
import {
	METHOD_APP,
	METHOD_SIM,
	ProcessState,
	RECOVERY_METHOD_SECOND_METHOD,
	ProcessName,
} from '../../mobileid-processes/constants';
import { H2 } from '../../titles';
import { namespace } from '../i18n';
import styles from './Processing.css';
import Close from '../../../assets/svg/Close';
import Loader from '../../Loader';
import { AppActivation } from './processing/AppActivation';
import { ProcessingIcon } from './ProcessingIcon';
import { getIsMobileAppIntegration } from '../../../views/MyMobileId/utils';
import { MobileEnrollTitle } from '../../../views/Login/MobileEnrollTitle';
import { MobileEnrollText } from '../../../views/Login/MobileEnrollText';
import type { ProcessOptions } from '../../mobileid-processes/types';

type Props = {
	processName?: string,
	processStateData?: Object,
	processOptions?: ProcessOptions,
	qrCodeUrl?: string,
	finishProcess?: Function,
	phoneNumber?: string,
};

const processingSubtitlePrefix = `processes.${
	ProcessState.Processing
}.subtitle.`;

const deviceKeyForRecoverySecondMethod = (type: string, method: string) => {
	const prefix = `${processingSubtitlePrefix}${type || method}`;
	if (type === RECOVERY_METHOD_SECOND_METHOD) {
		if (method === METHOD_SIM) {
			return `${prefix}.app`;
		}
		return `${prefix}.sim`;
	}
	return `${prefix}`;
};

class ProcessingComponent extends React.Component {
	props: Props & LocalizeProps;

	componentDidMount = () => {
		window.scroll(0, 0);
	};

	handleAbort = () => {
		const { t, finishProcess = () => {} } = this.props;
		// eslint-disable-next-line no-alert
		const result = window.confirm(
			t(`processes.${ProcessState.Processing}.abortPrompt`),
		);

		if (result) {
			finishProcess();
		}
	};

	render() {
		const {
			processName,
			t,
			processStateData,
			processOptions,
			qrCodeUrl,
			phoneNumber,
		} = this.props;

		const { type } = processStateData;
		const { method } = processOptions;

		const showAppActivation =
			type && type === METHOD_APP && processName === ProcessName.AppActivation;
		const appOrSimDeactivation = [
			ProcessName.AppDeactivation,
			ProcessName.SimDeactivation,
		].includes(processName);
		const isMobileAppIntegration = getIsMobileAppIntegration();

		const title = {
			default: t(`${processingSubtitlePrefix}${processName}`),
			mobileEnroll: t(`${processingSubtitlePrefix}${processName}`),
		};

		const recoveryTitle = {
			default: t('recovery'),
			mobileEnroll: t('recovery'),
		};

		const text = {
			default: t(deviceKeyForRecoverySecondMethod(type, method)),
			mobileEnroll: t(deviceKeyForRecoverySecondMethod(type, method)),
		};

		const mobileAppIntegrationStyles = {
			[styles.mobileAppIntegrationContainer]: isMobileAppIntegration,
		};

		return (
			<div className={classNames(mobileAppIntegrationStyles)}>
				{!isMobileAppIntegration && (
					<H2 margin="huge" className={styles.title}>
						{t(processName)}
						{!appOrSimDeactivation && (
							<button
								id="STATIC_ID_PROCESSES_ABORT_PROCESS"
								className={styles.closeButton}
								onClick={this.handleAbort}
								type="button"
							>
								<Close className={styles.closeIcon} />
							</button>
						)}
					</H2>
				)}

				{/* If you have an appActivation we show a different text than usual */}
				{showAppActivation ? (
					<AppActivation
						type={type}
						method={method}
						processName={processName}
						qrCodeUrl={qrCodeUrl}
						phoneNumber={phoneNumber}
					/>
				) : (
					<React.Fragment>
						{isMobileAppIntegration && (
							<MobileEnrollTitle title={recoveryTitle} />
						)}
						{type && <MobileEnrollText text={text} />}
					</React.Fragment>
				)}
				{type && (
					<ProcessingIcon
						type={type}
						method={method}
						processName={processName}
					/>
				)}

				{appOrSimDeactivation && (
					<React.Fragment>
						<MobileEnrollText text={title} />
						<Loader />
					</React.Fragment>
				)}
			</div>
		);
	}
}

const withHOCs = combineHOCs([localize(namespace), withStyles(styles)]);

const Processing = (withHOCs(ProcessingComponent): ReactComponent<Props>);

export { Processing };
