/* @flow */
import React from 'react';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { localize, LocalizeProps } from '../../../../modules/i18n';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import { namespace } from './i18n';
import type { ChildProps } from './Container';
import styles from './DashboardProcess.css';
import { ProcessState } from '../mobileid-processes/constants';
import { ChooseMethod } from './Processes/ChooseMethod';
import { ChooseRecoveryMethod } from './Processes/ChooseRecoveryMethod';
import { RecoveryCodeInput } from './Processes/RecoveryCodeInput';
import { Processing } from './Processes/Processing';
import { TermsAndConditions } from './Processes/termsAndConditions/TermsAndConditions';
import { DeactivateMethod } from './Processes/DeactivateMethod';
import Page from '../Page';
import Header from '../Header';
import { ProgressIndicator } from '../ProgressIndicator/ProgressIndicator';
import { IllustrationCloudLeft } from '../../assets/svg/IllustrationCloudLeft';
import { IllustrationCloudRight } from '../../assets/svg/IllustrationCloudRight';
import { getIsMobileAppIntegration } from '../../views/MyMobileId/utils';

type Props = ChildProps & LocalizeProps;

const ActiveScreen = ({
	processName,
	processState,
	processStateData,
	profile,
	processOptions,
	changeProcessOptions,
	provideRecoveryCode,
	returnToPreviousStep,
	qrCodeUrl,
	possibleRecoveryMethods,
	finishProcess,
	phoneNumber,
	changeProcessState,
}: ChildProps): any => {
	switch (processState) {
		case ProcessState.ChooseMethod:
			return (
				<ChooseMethod
					processName={processName}
					processOptions={processOptions}
					changeProcessOptions={changeProcessOptions}
					returnToPreviousStep={returnToPreviousStep}
				/>
			);
		case ProcessState.ChooseRecoveryMethod:
			return (
				<ChooseRecoveryMethod
					processName={processName}
					profile={profile}
					processOptions={processOptions}
					changeProcessOptions={changeProcessOptions}
					returnToPreviousStep={returnToPreviousStep}
					possibleRecoveryMethods={possibleRecoveryMethods}
				/>
			);
		case ProcessState.RecoveryCodeInput:
			return (
				<RecoveryCodeInput
					processName={processName}
					processStateData={processStateData}
					provideRecoveryCode={provideRecoveryCode}
					returnToPreviousStep={returnToPreviousStep}
				/>
			);
		case ProcessState.Processing:
			return (
				<Processing
					processName={processName}
					processStateData={processStateData}
					processOptions={processOptions}
					qrCodeUrl={qrCodeUrl}
					finishProcess={finishProcess}
					phoneNumber={phoneNumber}
				/>
			);
		case ProcessState.TermsAndConditions:
			return (
				<TermsAndConditions
					phoneNumber={phoneNumber}
					processOptions={processOptions}
					returnToPreviousStep={returnToPreviousStep}
					changeProcessOptions={changeProcessOptions}
					changeProcessState={changeProcessState}
				/>
			);
		case ProcessState.DeactivationConfirmation:
			return (
				<DeactivateMethod
					processName={processName}
					returnToPreviousStep={returnToPreviousStep}
					changeProcessOptions={changeProcessOptions}
					processOptions={processOptions}
					profile={profile}
				/>
			);
		default:
			return <div />;
	}
};

function DashboardProcessComponent(props: Props) {
	const {
		indicatorSteps,
		t,
		...activeScreenProps
	} = props;
	const { processState } = activeScreenProps;
	const progressClassForClouds = `_${indicatorSteps.indexOf(processState) +
	1}_${indicatorSteps.length}`;
	// hide header here
	const isMobileApp = getIsMobileAppIntegration();
	return (
		<Page
			className={styles.page}
			title={t('seo.title')}
			description={t('seo.description')}
		>
			<IllustrationCloudLeft
				className={classNames(styles.cloudLeft, styles[progressClassForClouds])}
			/>
			<IllustrationCloudRight
				className={classNames(
					styles.cloudRight,
					styles[progressClassForClouds],
				)}
			/>
			{!isMobileApp && <Header centered />}
			<div className={styles.outerContainer}>
				<div className={styles.processContainer}>
					<ActiveScreen {...activeScreenProps} />
					<ProgressIndicator
						className={styles.progressIndicator}
						steps={indicatorSteps}
						activeStep={indicatorSteps.indexOf(processState) + 1}
					/>
				</div>
			</div>
		</Page>
	);
}

const withHOCs = combineHOCs([localize(namespace), withStyles(styles)]);

const DashboardProcess = (withHOCs(
	DashboardProcessComponent,
): ReactComponent<Props>);

export { DashboardProcess };
