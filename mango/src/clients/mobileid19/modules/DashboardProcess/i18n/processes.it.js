const success = {
	Preparation: {
		title: 'Operazione andata a buon fine',
		subtitle: '',
	},
	null: {
		title: 'Operazione andata a buon fine',
		subtitle: '',
	},
	undefined: {
		title: 'Operazione andata a buon fine',
		subtitle: '',
	},
	SimActivation: {
		title: 'Operazione andata a buon fine',
		subtitle: 'Il Suo metodo SIM è stato attivato',
	},
	AppActivation: {
		title: 'Operazione andata a buon fine',
		subtitle: 'La Sua app è stata attivata',
	},
	ResetPin: {
		title: 'Operazione andata a buon fine',
		subtitle: 'Il Suo PIN è stato modificato',
	},
	GenerateRecoveryCode: {
		title: 'Operazione andata a buon fine',
		subtitle: 'È stato generato un nuovo codice di ripristino',
	},
	TestSignature: {
		title: 'Operazione andata a buon fine',
		subtitle: 'Il Suo test è stato eseguito con successo',
	},
	SimDeactivation: {
		title: 'Fatto!',
		subtitle: 'Il metodo SIM è stato disattivato con successo.',
		text1:
			'Naturalmente potrà riattivare la SIM di Mobile ID più avanti, in qualsiasi momento.',
		text2: '',
	},
	AppDeactivation: {
		title: 'Fatto!',
		subtitle: 'Il metodo app è stato disattivato con successo.',
		text1:
			'Importante: per portare a termine la procedura, disinstalli l’app Mobile ID dal suo apparecchio.',
		text2:
			'Naturalmente potrà riattivare l’app Mobile ID più avanti, in qualsiasi momento. Per farlo sarà necessario installare nuovamente l’app Mobile ID sul suo apparecchio.',
	},
};

const error = {
	'phone-activation/mid-auth-test-card-blocked': {
		title:
			"L'esecuzione del test Mobile\u00A0ID purtroppo non ha avuto successo.",
		subtitle:
			'Riprovare o contattare il proprio operatore di telefonia mobile.',
	},
	'phone-activation/mid-auth-test-error': {
		title:
			"L'esecuzione del test Mobile\u00A0ID purtroppo non ha avuto successo.",
		subtitle:
			'Riprovare o contattare il proprio operatore di telefonia mobile.',
	},
	'phone-activation/error/timeout': {
		title: 'Il processo è durato troppo a lungo.',
		subtitle: 'Riprovaci, per favore!',
	},
	'phone-activation/mid-auth-test-expired-transaction': {
		title:
			"L'esecuzione del test Mobile\u00A0ID non ha ricevuto risposta in tempo.",
		subtitle:
			'Riprovare o contattare il proprio operatore di telefonia mobile.',
		testSignatureButton: 'Riprova',
	},
	'phone-activation/mid-auth-test-unknown-client': {
		title:
			"L'esecuzione del test Mobile\u00A0ID purtroppo non ha avuto successo.",
		subtitle: 'La carta SIM non supporta ancora il servizio.',
		orderButton: 'Ordina SIM',
	},
	TestSignature: {
		title: 'Errore',
		subtitle: 'Purtroppo il test non è andato a buon fine',
	},
	ResetPin: {
		title: 'Errore',
		subtitle: 'Purtroppo il ripristino del PIN non è andato a buon fine',
	},
	'phone-activation/mid-auth-test-user-cancel': {
		title: 'Avete cancellato il vostro test di Mobile\u00A0ID.',
		subtitle:
			'Riprovare o contattare il proprio operatore di telefonia mobile.',
		testSignatureButton: 'Riprova',
	},
	SimActivation: {
		title: 'Errore',
		subtitle: 'Purtroppo l’attivazione non è andata a buon fine',
	},
	'phone-activation/mid-auth-test-pin-nr-blocked': {
		title: 'Il vostro Mobile\u00A0ID PIN è bloccato.',
		subtitle: 'Si prega di reimpostare il PIN Mobile\u00A0ID.',
		resetPinButton: 'PIN Reset',
	},
	'phone-activation/mid-auth-test-invalid-signed-data': {
		title:
			"L'esecuzione del test Mobile\u00A0ID purtroppo non ha avuto successo.",
		subtitle:
			'Riprovare o contattare il proprio operatore di telefonia mobile.',
		testSignatureButton: 'Riprova',
	},
	'phone-activation/mid-auth-test-no-cert-found': {
		title: 'Il tuo Mobile\u00A0ID non è più attivo.',
		subtitle: 'Attivare Mobile\u00A0ID.',
		activationButton: 'Provare Subito',
	},
	'phone-activation/mid-auth-test-pb-signature-process': {
		title: 'Un test Mobile\u00A0ID è già stato avviato.',
		subtitle: 'Si prega di riprovare in un secondo momento.',
		testSignatureButton: 'Riprova',
	},
	GenerateRecoveryCode: {
		title: 'Errore',
		subtitle:
			'Purtroppo la creazione di un codice di ripristino non è andata a buon fine',
	},
	AppActivation: {
		title: 'Errore',
		subtitle: 'Purtroppo l’attivazione non è andata a buon fine',
	},
	subtitle: 'Purtroppo al momento Mobile\u00A0ID non funziona!',
	title: 'Test fallito',
	'phone-activation/mid-auth-test-no-key-found': {
		title: 'Non hai ancora attivato Mobile\u00A0ID.',
		subtitle: 'Attivare Mobile\u00A0ID.',
		activationButton: 'Provare Subito',
	},
	SimDeactivation: {
		title: 'Errore',
		subtitle: 'Purtroppo non è stato possibile disattivare il metodo SIM.',
	},
	AppDeactivation: {
		title: 'Errore',
		subtitle: 'Purtroppo non è stato possibile disattivare il metodo app.',
	},
};

const mobileEnroll = {
	title: 'Il vostro Mobile ID è pronto',
	p1:
		"Se doveste avere bisogno di recuperare l'accesso al vostro account, questo codice vi aiuterà. Dovreste annotarlo e conservarlo in un luogo sicuro.",
	p2:
		'Se in precedenza si disponeva di un codice di recupero, questo non è più valido. Utilizzate invece questo nuovo codice.',
	recoveryCodeLabel: 'Il vostro codice di recupero è',
};

const result = {
	success,
	error,
	mobileEnroll,
	recoveryCodeInstruction:
		'La preghiamo di annotare il codice di ripristino e di conservarlo in un luogo sicuro!',
	question2: 'Come conservare il codice di ripristino?',
	recoveryCodeText: 'ecco il Suo codice ',
	recoveryCodeLabel: 'Il Suo codice di ripristino:',
	answer1:
		'Se viene sostituita la scheda SIM che si sta usando per Mobile ID o se si perde il PIN di Mobile ID, è necessario riattivare Mobile ID sul sito internet www.mobileid.ch utilizzando gli strumenti di Selfcare. In tali casi, il codice di ripristino consente di conservare i collegamenti già utilizzati con i diversi operatori.',
	answer2:
		'Scriverlo da qualche parte o stampare il codice di ripristino. Il codice di ripristino non deve essere salvato sul vostro computer o dispositivo mobile. Conservare il codice di ripristino in un luogo sicuro.',
	recoveryCodeFat: 'Codice di ripristino',
	serialNoLabel: 'Il Suo numero di serie Mobile ID unico è:',
	replacingInformation:
		'Questo codice sostituisce i codici di sblocco già esistenti.',
	appInformation:
		'Si prega di notare che attualmente non tutti i fornitori di servizi Mobile ID supportano l’APP Mobile ID.',
	issuer: 'ed è stato rilasciato da %{issuer}',
};

export const processesIT = {
	DeactivationConfirmation: {
		subtitle: {
			SimDeactivation:
				'Lei desidera disattivare la SIM di Mobile ID. Le chiediamo di confermare la procedura.',
			AppDeactivation:
				'Lei desidera disattivare l’app Mobile ID. Le chiediamo di confermare la procedura.',
		},
		warning: {
			SimDeactivation:
				'Potrà riattivare la SIM di Mobile ID più avanti, in qualsiasi momento.',
			AppDeactivation:
				'Potrà riattivare l’app Mobile ID più avanti, in qualsiasi momento.',
		},
		confirmationText: {
			SimDeactivation: 'Confermo di voler disattivare il metodo SIM.',
			AppDeactivation: 'Confermo di voler disattivare il metodo app.',
		},
		confirmationText2: 'Confermo di conoscere il mio ',
		recoveryCodeHighlight: 'codice di ripristino',
		confirmationText3: ' per una eventuale riattivazione.',
		warningTitle: {
			SimDeactivation: 'Attenzione',
			AppDeactivation: 'Attenzione',
		},
		warningText: {
			SimDeactivation:
				'Alcuni fornitori di servizi accettano esclusivamente il metodo SIM come strumento di autenticazione. Se procede con la disattivazione, potrebbe perdere l’accesso ai servizi offerti da tali fornitori.',
			AppDeactivation:
				'Alcuni fornitori di servizi accettano esclusivamente il metodo app come strumento di autenticazione. Se procede con la disattivazione, potrebbe perdere l’accesso ai servizi offerti da tali fornitori.',
		},
		warningQuestion: {
			SimDeactivation:
				'Desidera procedere con la disattivazione del metodo SIM?',
			AppDeactivation:
				'Desidera procedere con la disattivazione del metodo app?',
		},
		errors: {
			ERROR_NO_CONFIRMATION:
				'Le chiediamo di confermare che desidera disattivare il metodo app e che conosce il suo codice di ripristino.',
		},
		tooltipText: {
			hasRecoveryCode:
				'Per procedere alla riattivazione in un secondo momento, è necessario che lei conosca il suo codice di ripristino. Può generare un nuovo codice di ripristino in qualsiasi momento.',
			hasNoRecoveryCode:
				'Per procedere alla riattivazione in un secondo momento, è necessario che lei generi un codice di ripristino.',
		},
	},
	TermsAndConditions: {
		subtitle:
			'Utilizzando i nostri Servizi, l’utente accetta i presenti termini. Si prega di leggerli con attenzione.				',
		termsAndConditions1: {
			METHOD_SIM: 'Accetto i ',
			METHOD_APP: 'Accetto le ',
		},
		termsAndConditions2: {
			METHOD_SIM: '.',
			METHOD_APP: ' e prendere atto delle ',
		},
		termsAndConditionsLinkLabel: {
			METHOD_SIM: 'termini e le condizioni',
			METHOD_APP: "disposizioni d'uso",
		},
		termsAndConditionsLink: {
			METHOD_SIM:
				'https://documents.swisscom.com/product/filestore/lib/642ae7bd-2caa-4b2f-9141-2a2181f9ece6/mid_sim_nb-it.pdf',
			METHOD_APP:
				'https://documents.swisscom.com/product/filestore/lib/0740ed14-2710-46d6-a74c-f91a6e0bbde7/mid_app_nb-it.pdf',
		},
		termsAndConditionsLinkLabel2: {
			METHOD_SIM: '',
			METHOD_APP: 'dichiarazione di protezione dei dati',
		},
		termsAndConditionsLink2: {
			METHOD_SIM: '',
			METHOD_APP:
				'https://documents.swisscom.com/product/filestore/lib/cdfddac0-c5cc-4521-b1fa-1a7929fe7269/mid_app_ps_eu_ch-it.pdf',
		},
		termsAndConditions3: {
			METHOD_SIM: '',
			METHOD_APP: '.',
		},
		errors: {
			ERROR_TERMS_NOT_ACCEPTED:
				'Si prega di accettare le condizioni di utilizzo.',
		},
	},
	ChooseMethod: {
		subtitle: {
			Activation: 'Quale metodo desidera attivare?',
			TestSignature: 'Quale metodo desidera testare?',
		},
		errors: {
			ERROR_NO_METHOD: 'Selezionare un metodo.',
		},
		orderSim: 'Ordina SIM',
		information: {
			METHOD_SIM:
				'La SIM è attualmente supportata da tutti i fornitori di servizi che supportano Mobile ID.',
			METHOD_APP:
				"L'app non è attualmente supportata da tutti i fornitori di servizi Mobile ID.",
		},
		confirmation: {
			title: 'Attenzione: Account Attivo Rilevato',
			desc1: `Attualmente hai un account attivo dell'App Mobile ID.
				Se scegli di procedere con una nuova attivazione, il tuo account esistente verrà temporaneamente disattivato.
				Dovrai quindi completare il nuovo processo di attivazione per riattivare il tuo account.`,
			desc2:	`Avrai bisogno del tuo codice di recupero, che è fondamentale per mantenere le tue connessioni con i fornitori di servizi.
				Se non lo ricordi,`,
			link: 'crea un nuovo codice di recupero',
			desc3: ' prima di procedere.',
			confirmation: 'Ho capito e desidero procedere',
		},
	},
	Processing: {
		subtitle: {
			METHOD_SIM: 'La preghiamo di completare il processo sul Suo cellulare',
			METHOD_APP: 'La preghiamo di completare il processo con la Sua app',
			RECOVERY_METHOD_SECOND_METHOD: {
				sim: 'La preghiamo di rispondere alla domanda sul Suo cellulare',
				app: 'La preghiamo di rispondere alla domanda nella Sua app',
			},
			SimDeactivation:
				'Il metodo SIM verrà disattivato. Le chiediamo di attendere un momento.',
			AppDeactivation:
				'Il metodo app verrà disattivato. Le chiediamo di attendere un momento.',
		},
		qrCodeInstructions: {
			part1: 'Scarica la Mobile ID App gratuita Mobile ID ',
			part2:
				' e scansionare questo codice QR con la funzione “+ Aggiungi account” disponibile nell’app. Questo codice QR è valido per 10 minuti.',
			comingSoon: 'presto disponibile',
			generalInstructions: {
				part1:
					"Per favore segui i passi qui sotto per completare l'attivazione del Mobile-ID per ",
				part2: ' .',
				part3:
					'Installare l’applicazione Mobile ID dall’App Store e seguire le istruzioni dell’applicazione per creare un account. Tornare successivamente a questa pagina per completare la procedura di accesso.',
				watchVideo: 'Guarda il video tutorial',
			},
			step1title: 'Passo 1',
			step1: "Installare l'App Mobile-ID",
			step2title: 'Passo 2',
			step2a: 'Scannerizza codici QR con la tua App Mobile-ID',
			step2b: 'O ANCHE',
			step2c: "Clicca sull'icona Mobile-ID",
		},
		download: {
			dowloadLinkGooglePlay:
				'https://play.google.com/store/apps/details?id=com.swisscom.mobileid&hl=it',
			downloadLinkAppStore:
				'https://itunes.apple.com/ch/app/mobile-id/id1500393675?mt=8&l=it',
		},
		abortPrompt: 'Siete sicuri di voler interrompere il processo?',
	},
	Result: result,
	ChooseRecoveryMethod: {
		subtitle:
			'Avete la possibilità di ripristinare il vostro Mobile ID. Per fare questo, selezionare una selezione che fa al caso vostro.',
		noRecoveryReason: {
			noneOfTheAbove: "Vorrei continuare senza un'opzione di sblocco",
			cannotUseApp: "Vorrei continuare senza un'opzione di sblocco",
			cannotUseRecovery: "Vorrei continuare senza un'opzione di sblocco",
		},
		useRecoveryMethod: 'Conosco il mio  codice di ripristino',
		useSecondMethod: {
			METHOD_APP: 'Mi autentico con la mia SIM Mobile ID',
			METHOD_SIM: 'Mi autentico con la mia Mobile ID App',
		},
		warning: {
			title: 'Attenzione',
			text:
				'Se desidera continuare senza identificazione, per motivi di sicurezza dovrebbe ricollegare il Suo Mobile ID con alcuni dei Suoi fornitori di servizi',
			question: 'Desidera continuare senza identificazione?',
		},
		errors: {
			ERROR_NO_RECOVERY_METHOD: 'Selezionare un metodo di recupero.',
		},
	},
	RecoveryCodeInput: {
		subtitle: 'La preghiamo di inserire il Suo codice di ripristino',
		errors: {
			'phone-activation/recovery-code-invalid':
				'Il codice di ripristino da Lei inserito non è valido. La preghiamo di riprovare.',
			'phone-activation/recovery-code-too-many-attempts':
				'Il codice di recupero è stato inserito troppo spesso in modo errato. Per favore, riprova più tardi.',
			ERROR_RECOVERY_CODE_INVALID: 'Inserire il codice di recupero completo.',
		},
		tooltip:
			"Si prega di notare che un codice di sblocco può essere utilizzato una sola volta. Dopo un'attivazione riuscita, riceverete un nuovo codice di sblocco.",
	},
};
