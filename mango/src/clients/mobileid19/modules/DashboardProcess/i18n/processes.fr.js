const success = {
	Preparation: {
		title: '<PERSON><PERSON><PERSON>',
		subtitle: '',
	},
	null: {
		title: '<PERSON><PERSON><PERSON>',
		subtitle: '',
	},
	undefined: {
		title: '<PERSON><PERSON><PERSON>',
		subtitle: '',
	},
	SimActivation: {
		title: '<PERSON>cc<PERSON>',
		subtitle: 'Votre méthode SIM a été activée',
	},
	AppActivation: {
		title: 'Succ<PERSON>',
		subtitle: 'Votre application a été activée',
	},
	ResetPin: {
		title: 'Succ<PERSON>',
		subtitle: 'Votre PIN a été modifié',
	},
	GenerateRecoveryCode: {
		title: 'Succès',
		subtitle: 'Un nouveau code de restauration a été généré',
	},
	TestSignature: {
		title: 'Succ<PERSON>',
		subtitle: 'Votre test a réussi',
	},
	SimDeactivation: {
		title: 'Ça a marché!',
		subtitle: 'Vous avez désactivé avec succès la méthode SIM.',
		text1: 'Vous pouvez bien sûr réactiver la SIM Mobile ID à tout moment.',
		text2: '',
	},
	AppDeactivation: {
		title: 'Ça a marché!',
		subtitle: 'Vous avez désactivé avec succès la méthode application.',
		text1:
			'Important: veuillez désinstaller l’application Mobile ID de votre appareil afin de finaliser le processus.',
		text2:
			'Vous pouvez bien sûr réactiver l’application Mobile ID à tout moment. Pour ce faire, vous devez réinstaller l’application Mobile ID sur votre appareil.',
	},
};

const error = {
	'phone-activation/mid-auth-test-card-blocked': {
		title: "Votre test de Mobile\u00A0ID n'a malheureusement pas réussi.",
		subtitle: 'Veuillez réessayer ou contacter votre opérateur mobile.',
	},
	'phone-activation/mid-auth-test-error': {
		title: "Votre test de Mobile\u00A0ID n'a malheureusement pas réussi.",
		subtitle: 'Veuillez réessayer ou contacter votre opérateur mobile.',
	},
	'phone-activation/error/timeout': {
		title: 'Le procès a été trop long.',
		subtitle: 'Veuillez réessayer !',
	},
	'phone-activation/mid-auth-test-expired-transaction': {
		title: "Votre test n'a pas été répondu à temps.",
		subtitle: 'Veuillez réessayer ou contacter votre opérateur mobile.',
		testSignatureButton: 'Refaire le test',
	},
	'phone-activation/mid-auth-test-unknown-client': {
		title: "Votre test de Mobile\u00A0ID n'a malheureusement pas réussi.",
		subtitle: 'Votre carte SIM ne prend pas encore en charge ce service.',
		orderButton: 'Commander SIM',
	},
	TestSignature: {
		title: 'Erreur',
		subtitle: 'Le test a malheureusement échoué',
	},
	ResetPin: {
		title: 'Erreur',
		subtitle: 'La réinitialisation du PIN a malheureusement échoué',
	},
	'phone-activation/mid-auth-test-user-cancel': {
		title: 'Vous avez annulé votre test de Mobile\u00A0ID.',
		subtitle: 'Veuillez réessayer ou contacter votre opérateur mobile.',
		testSignatureButton: 'Refaire le test',
	},
	SimActivation: {
		title: 'Erreur',
		subtitle: 'L’activation a malheureusement échoué',
	},
	'phone-activation/mid-auth-test-pin-nr-blocked': {
		title: 'Votre code PIN de Mobile\u00A0ID est bloqué.',
		subtitle: 'Veuillez réinitialiser votre NIP de Mobile\u00A0ID.',
		resetPinButton: 'PIN Reset',
	},
	'phone-activation/mid-auth-test-invalid-signed-data': {
		title: "Votre test de Mobile\u00A0ID n'a malheureusement pas réussi.",
		subtitle: 'Veuillez réessayer ou contacter votre opérateur mobile.',
		testSignatureButton: 'Refaire le test',
	},
	'phone-activation/mid-auth-test-no-cert-found': {
		title: "Votre Mobile\u00A0ID n'est plus actif.",
		subtitle: 'Veuillez activer Mobile\u00A0ID.',
		activationButton: 'Essayer Maintenant',
	},
	'phone-activation/mid-auth-test-pb-signature-process': {
		title: 'Un test de Mobile\u00A0ID a déjà été lancé.',
		subtitle: 'Veuillez réessayer plus tard.',
		testSignatureButton: 'Refaire le test',
	},
	GenerateRecoveryCode: {
		title: 'Erreur',
		subtitle:
			'La génération d’un code de restauration a malheureusement échoué',
	},
	AppActivation: {
		title: 'Erreur',
		subtitle: 'L’activation a malheureusement échoué',
	},
	subtitle:
		'Malheureusement, votre Mobile\u00A0ID ne fonctionne pas en ce moment.',
	title: 'Echec du test',
	'phone-activation/mid-auth-test-no-key-found': {
		title: "Vous n'avez pas encore activé Mobile\u00A0ID.",
		subtitle: 'Veuillez activer Mobile\u00A0ID.',
		activationButton: 'Essayer Maintenant',
	},
	SimDeactivation: {
		title: 'Erreur',
		subtitle: 'La méthode SIM n’a pas pu être désactivée.',
	},
	AppDeactivation: {
		title: 'Erreur',
		subtitle: 'La méthode application n’a pas pu être désactivée.',
	},
};

const mobileEnroll = {
	title: 'Votre Mobile ID est prêt',
	p1:
		"Si vous devez récupérer l'accès à votre compte, ce code vous sera utile. Vous devriez le noter et le conserver dans un endroit sûr.",
	p2:
		"Si vous aviez précédemment un code de récupération, il n'est plus valide. Utilisez ce nouveau code à la place.",
	recoveryCodeLabel: 'Votre code de récupération est le suivant',
};

const result = {
	success,
	error,
	mobileEnroll,
	question1: 'Qu’est-ce que le code de réactivation Mobile ID?',
	recoveryCodeInstruction:
		'Veuillez noter le code de restauration et conservez-le dans un endroit sûr!',
	question2: 'Que faire de mon code de réactivation?',
	recoveryCodeText: 'Voyez ci-dessous votre ',
	recoveryCodeLabel: 'Votre code de restauration:',
	answer1:
		'En cas de changement de carte SIM Mobile ID ou de perte de votre code PIN Mobile ID, Mobile ID doit être réactivé via la rubrique «Aide» du portail disponible sur www.mobileid.ch/fr. Le code de réactivation vous permet de maintenir Mobile ID auprès des différents fournisseurs de services.',
	answer2:
		'Notez ou imprimez votre code de réactivation. Le code de réactivation ne doit toutefois pas être sauvegardé sur votre ordinateur ou votre mobile.  Gardez votre code de réactivation en lieu sûr.',
	recoveryCodeFat: 'Code de restauration',
	serialNoLabel: 'Votre numéro de série Mobile ID unique est:',
	replacingInformation:
		'Ce code remplace les codes de récupération déjà existants.',
	appInformation:
		"Veuillez noter qu'actuellement tous les fournisseurs de services d'identification mobile ne supportent pas Mobile ID app.",
	issuer: 'et a été délivré par %{issuer}',
};

export const processesFR = {
	DeactivationConfirmation: {
		subtitle: {
			SimDeactivation:
				'Vous souhaitez désactiver la SIM Mobile ID. Veuillez confirmer ce processus.',
			AppDeactivation:
				'Vous souhaitez désactiver l’application Mobile ID. Veuillez confirmer ce processus.',
		},
		warning: {
			SimDeactivation:
				'Vous pouvez réactiver la SIM Mobile ID à tout moment par la suite.',
			AppDeactivation:
				'Vous pouvez réactiver l’application Mobile ID à tout moment par la suite.',
		},
		confirmationText: {
			SimDeactivation: 'Je confirme vouloir désactiver la méthode SIM.',
			AppDeactivation: 'Je confirme vouloir désactiver la méthode application.',
		},
		confirmationText2: 'Je confirme connaître mon ',
		recoveryCodeHighlight: 'code de réactivation',
		confirmationText3: ' pour une éventuelle réactivation.',
		warningTitle: {
			SimDeactivation: 'Attention',
			AppDeactivation: 'Attention',
		},
		warningText: {
			SimDeactivation:
				'Certains fournisseurs de services autorisent uniquement la méthode SIM comme moyen d’authentification. Si vous procédez à la désactivation, vous risquez de perdre l’accès à ces fournisseurs de services.',
			AppDeactivation:
				'Certains fournisseurs de services autorisent uniquement la méthode application comme moyen d’authentification. Si vous procédez à la désactivation, vous risquez de perdre l’accès à ces fournisseurs de services.',
		},
		warningQuestion: {
			SimDeactivation:
				'Vous souhaitez procéder à la désactivation de la méthode SIM?',
			AppDeactivation:
				'Vous souhaitez procéder à la désactivation de la méthode application?',
		},
		errors: {
			ERROR_NO_CONFIRMATION:
				'Veuillez confirmer que vous souhaitez désactiver la méthode et que vous connaissez votre code de réactivation.',
		},
		tooltipText: {
			hasRecoveryCode:
				'Vous devriez connaître votre code de réactivation pour une réactivation ultérieure. Vous pouvez générer à tout moment un nouveau code de réactivation.',
			hasNoRecoveryCode:
				'Vous devriez générer un code de réactivation pour une réactivation ultérieure.',
		},
	},
	TermsAndConditions: {
		subtitle:
			'L’utilisation de nos Services implique votre acceptation des présentes Conditions d’Utilisation. Nous vous invitons à les lire attentivement.',
		termsAndConditions1: {
			METHOD_SIM: "J'accepte les ",
			METHOD_APP: "J'accepte les ",
		},
		termsAndConditions2: {
			METHOD_SIM: '.',
			METHOD_APP: ' et prends note de la ',
		},
		termsAndConditionsLinkLabel: {
			METHOD_SIM: 'termes et conditions',
			METHOD_APP: "conditions d'utilisation",
		},
		termsAndConditionsLink: {
			METHOD_SIM:
				'https://documents.swisscom.com/product/filestore/lib/53e48f81-19e0-4941-9b9e-e84be018a45f/mid_sim_nb-fr.pdf',
			METHOD_APP:
				'https://documents.swisscom.com/product/filestore/lib/2c394d6d-7d60-4930-a204-9d7ed90ae8f2/mid_app_nb-fr.pdf',
		},
		termsAndConditionsLinkLabel2: {
			METHOD_SIM: '',
			METHOD_APP: 'déclaration de protection des données',
		},
		termsAndConditionsLink2: {
			METHOD_SIM: '',
			METHOD_APP:
				'https://documents.swisscom.com/product/filestore/lib/d10fe962-7ae6-443f-abfa-f04f46cd08d8/mid_app_ps_eu_ch-fr.pdf',
		},
		termsAndConditions3: {
			METHOD_SIM: '',
			METHOD_APP: '.',
		},
		errors: {
			ERROR_TERMS_NOT_ACCEPTED:
				"Veuillez accepter les conditions d'utilisation.",
		},
	},
	ChooseMethod: {
		subtitle: {
			Activation: 'Quelle méthode souhaitez-vous activer?',
			TestSignature: 'Quelle méthode souhaitez-vous tester?',
		},
		errors: {
			ERROR_NO_METHOD: 'Veuillez choisir une méthode.',
		},
		orderSim: 'Commander la carte SIM',
		information: {
			METHOD_SIM:
				'La carte SIM est actuellement prise en charge par tous les fournisseurs de services Mobile ID.',
			METHOD_APP:
				"L'application n'est pas prise en charge actuellement par tous les fournisseurs de services Mobile ID.",
		},
		confirmation: {
			title: 'Attention: Compte Actif Détecté',
			desc1: `Vous avez actuellement un compte actif sur l'application Mobile ID.
				Si vous choisissez de procéder à une nouvelle activation, votre compte existant sera temporairement désactivé.
				Vous devrez ensuite compléter le processus de nouvelle activation pour réactiver votre compte.`,
			desc2:	`Vous aurez besoin de votre code de récupération, qui est crucial pour conserver vos connexions avec les prestataires de services.
				Si vous ne vous en souvenez pas, veuillez`,
			link: 'créer un nouveau code de récupération',
			desc3: ' avant de continuer.',
			confirmation: 'Je comprends et souhaite procéder',
		},
	},
	Processing: {
		subtitle: {
			METHOD_SIM:
				'Veuillez compléter le processus sur votre téléphone portable',
			METHOD_APP: 'Veuillez compléter le processus avec votre application',
			RECOVERY_METHOD_SECOND_METHOD: {
				sim: 'Veuillez répondre à la demande sur votre téléphone portable',
				app: 'Veuillez répondre à la demande dans votre application',
			},
			SimDeactivation:
				'La méthode SIM est désactivée. Veuillez patienter un instant.',
			AppDeactivation:
				'La méthode application est désactivée. Veuillez patienter un instant.',
		},
		qrCodeInstructions: {
			part1: "Télécharger l'application gratuite Mobile ID ",
			part2:
				' et scannez ce code QR avec la fonction «+ Ajouter un compte» dans l’application. Ce code QR est valable pendant 10 minutes.',
			comingSoon: 'prochainement',
			generalInstructions: {
				part1:
					"Veuillez suivre les étapes ci-dessous pour terminer l'activation de Mobile-ID pour ",
				part2: ' .',
				part3:
					'Veuillez installer l’application Mobile ID à partir de l’App Store et suivre les instructions de l’application pour créer un compte. Revenez ensuite sur cette page pour terminer la procédure de connexion.',
				watchVideo: 'Regarder les instructions en vidéo',
			},
			step1title: 'Étape 1',
			step1: "Installez l'application Mobile-ID",
			step2title: 'Étape 2',
			step2a: "Scannez le code QR avec l'application Mobile-ID",
			step2b: 'OU BIEN',
			step2c: "Clique sur l'icône Mobile-ID",
		},
		download: {
			dowloadLinkGooglePlay:
				'https://play.google.com/store/apps/details?id=com.swisscom.mobileid&hl=fr',
			downloadLinkAppStore:
				'https://itunes.apple.com/ch/app/mobile-id/id1500393675?mt=8&l=fr',
		},
		abortPrompt: 'Êtes-vous sûr de vouloir interrompre la procédure?',
	},
	Result: result,
	ChooseRecoveryMethod: {
		subtitle:
			"Vous avez la possibilité de restaurer votre Mobile ID. Pour ce faire, sélectionnez l'option qui vous convient.",
		noRecoveryReason: {
			noneOfTheAbove: "Je voudrais continuer sans l'option de restauration",
			cannotUseApp: "Je voudrais continuer sans l'option de restauration",
			cannotUseRecovery: "Je voudrais continuer sans l'option de restauration",
		},
		useRecoveryMethod: 'Je connais mon code de restauration Mobile ID',
		useSecondMethod: {
			METHOD_APP: "Je m'authentifie avec Mobile ID SIM",
			METHOD_SIM: "Je m'authentifie avec Mobile ID App",
		},
		warning: {
			title: 'Attention',
			text:
				'Si vous continuez sans identification, vous devrez peut-être reconnecter votre Mobile ID avec certains de vos fournisseurs de services pour des raisons de sécurité',
			question: 'Souhaitez-vous continuer sans identification?',
		},
		errors: {
			ERROR_NO_RECOVERY_METHOD: 'Veuillez choisir une méthode de récupération.',
		},
	},
	RecoveryCodeInput: {
		subtitle: 'Veuillez saisir votre code de restauration',
		errors: {
			'phone-activation/recovery-code-invalid':
				'Le code de restauration que vous avez saisi est invalide. Merci de réessayer.',
			'phone-activation/recovery-code-too-many-attempts':
				'Le code de récupération a été saisi trop souvent de manière incorrecte. Veuillez réessayer plus tard.',
			ERROR_RECOVERY_CODE_INVALID:
				'Veuillez saisir le code de recouvrement complet.',
		},
		tooltip:
			"Veuillez noter qu'un code de récupération ne peut être utilisé qu'une seule fois. Vous recevrez un nouveau code de récupération après une activation réussie.",
	},
};
