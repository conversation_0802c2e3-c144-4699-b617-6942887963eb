const success = {
	Preparation: {
		title: '<PERSON><PERSON>ol<PERSON>',
		subtitle: '',
	},
	null: {
		title: '<PERSON><PERSON>ol<PERSON>',
		subtitle: '',
	},
	undefined: {
		title: 'Erfolg',
		subtitle: '',
	},
	SimActivation: {
		title: '<PERSON>rfolg',
		subtitle: 'Ihre SIM Methode wurde aktiviert',
	},
	AppActivation: {
		title: '<PERSON>rfolg',
		subtitle: 'Ihre App wurde aktiviert',
	},
	ResetPin: {
		title: 'Erfolg',
		subtitle: 'Ihr PIN wurde geändert',
	},
	GenerateRecoveryCode: {
		title: 'Erfolg',
		subtitle: 'Ein neuer Wieder­herstellungs­code wurde generiert',
	},
	TestSignature: {
		title: 'Erfolg',
		subtitle: 'Ihr Test war erfolgreich',
	},
	SimDeactivation: {
		title: 'Das hat geklappt!',
		subtitle: 'Sie haben die SIM Methode erfolgreich deaktiviert.',
		text1:
			'Selbstverständlich können Sie die Mobile ID SIM zu jedem Zeitpunkt erneut aktivieren.',
		text2: '',
	},
	AppDeactivation: {
		title: 'Das hat geklappt!',
		subtitle: 'Sie haben die APP Methode erfolgreich gelöscht.',
		text1:
			'Wichtig: Bitte deinstallieren Sie die Mobile ID App von Ihrem Gerät, um den Vorgang abzuschliessen.',
		text2:
			'Selbstverständlich können Sie die Mobile ID App zu jedem Zeitpunkt erneut aktivieren. Dazu müssen Sie die Mobile ID App auf Ihrem Gerät neu installieren.',
	},
};

const error = {
	'phone-activation/mid-auth-test-card-blocked': {
		title: 'Ihr Mobile ID Testlauf war leider nicht erfolgreich.',
		subtitle:
			'Bitte versuchen Sie es erneut oder kontaktieren Sie Ihren Mobilfunkanbieter.',
	},
	'phone-activation/mid-auth-test-error': {
		title: 'Ihr Mobile ID Testlauf war leider nicht erfolgreich.',
		subtitle:
			'Bitte versuchen Sie es erneut oder kontaktieren Sie Ihren Mobilfunkanbieter.',
	},
	'phone-activation/error/timeout': {
		title: 'Der Prozess hat zu lange gedauert.',
		subtitle: 'Bitte versuchen Sie es erneut.',
	},
	'phone-activation/mid-auth-test-expired-transaction': {
		title: 'Ihr Mobile ID Testlauf wurde nicht rechtzeitig beantwortet.',
		subtitle:
			'Bitte versuchen Sie es erneut oder kontaktieren Sie Ihren Mobilfunkanbieter.',
		testSignatureButton: 'Erneut testen',
	},
	'phone-activation/mid-auth-test-unknown-client': {
		title: 'Ihr Mobile ID Testlauf war leider nicht erfolgreich.',
		subtitle: 'Ihre SIM Karte unterstützt den Service noch nicht.',
		orderButton: 'SIM Bestellen',
	},
	TestSignature: {
		title: 'Fehler',
		subtitle: 'Leider war der Test nicht erfolgreich',
	},
	ResetPin: {
		title: 'Fehler',
		subtitle: 'Leider ist der PIN Reset fehlgeschlagen',
	},
	'phone-activation/mid-auth-test-user-cancel': {
		title: 'Sie haben Ihren Mobile ID Testlauf abgebrochen.',
		subtitle:
			'Bitte versuchen Sie es erneut oder kontaktieren Sie Ihren Mobilfunkanbieter.',
		testSignatureButton: 'Erneut testen',
	},
	SimActivation: {
		title: 'Fehler',
		subtitle: 'Leider ist die Aktivierung fehlgeschlagen',
	},
	'phone-activation/mid-auth-test-pin-nr-blocked': {
		title: 'Ihr Mobile ID PIN ist blockiert.',
		subtitle: 'Bitte setzen Sie Ihren Mobile ID PIN neu.',
		resetPinButton: 'PIN Reset',
	},
	'phone-activation/mid-auth-test-invalid-signed-data': {
		title: 'Ihr Mobile ID Testlauf war leider nicht erfolgreich.',
		subtitle:
			'Bitte versuchen Sie es erneut oder kontaktieren Sie Ihren Mobilfunkanbieter.',
		testSignatureButton: 'Erneut testen',
	},
	'phone-activation/mid-auth-test-no-cert-found': {
		title: 'Ihre Mobile ID ist nicht mehr aktiv.',
		subtitle: 'Bitte aktivieren Sie Mobile ID.',
		activationButton: 'Jetzt Probieren',
	},
	'phone-activation/mid-auth-test-pb-signature-process': {
		title: 'Es wurde bereits ein Mobile ID Testlauf gestartet.',
		subtitle: 'Bitte versuchen Sie es zu einem späteren Zeitpunkt erneut.',
		testSignatureButton: 'Erneut testen',
	},
	GenerateRecoveryCode: {
		title: 'Fehler',
		subtitle:
			'Leider ist die Generierung eines Wiederherstellungcodes fehlgeschlagen',
	},
	AppActivation: {
		title: 'Fehler',
		subtitle: 'Leider ist die Aktivierung fehlgeschlagen',
	},
	subtitle: 'Leider funktioniert Ihre Mobile ID momentan nicht!',
	title: 'Test fehlgeschlagen',
	'phone-activation/mid-auth-test-no-key-found': {
		title: 'Sie haben Mobile ID noch nicht aktiviert.',
		subtitle: 'Bitte aktivieren Sie Mobile ID.',
		activationButton: 'Jetzt Probieren',
	},
	SimDeactivation: {
		title: 'Fehler',
		subtitle: 'Leider konnte die SIM Methode nicht deaktiviert werden.',
	},
	AppDeactivation: {
		title: 'Fehler',
		subtitle: 'Leider konnte die App Methode nicht deaktiviert werden.',
	},
};

const mobileEnroll = {
	title: 'Ihre Mobile ID ist bereit',
	p1:
		'Wenn Sie jemals den Zugang zu Ihrem Konto wiederherstellen müssen, wird Ihnen dieser Code helfen. Sie sollten ihn aufschreiben und an einem sicheren Ort aufbewahren.',
	p2:
		'Wenn Sie zuvor einen Wiederherstellungscode hatten, ist dieser nicht mehr gültig. Verwenden Sie stattdessen diesen neuen Code.',
	recoveryCodeLabel: 'Ihr Wiederherstellungscode lautet',
};

const result = {
	success,
	error,
	mobileEnroll,
	question1: 'Was ist der Mobile ID Recovery Code?',
	recoveryCodeInstruction:
		'Bitte notieren Sie sich den Wieder­herstellungs­code und bewahren Sie ihn an einem sicheren Ort auf!',
	question2: 'Wie sollte ein Wieder­herstellungs­code aufbewahrt werden?',
	recoveryCodeText: 'Unten sehen Sie Ihren ',
	recoveryCodeLabel: 'Ihr Wieder­herstellungs­code:',
	answer1:
		'Bei einem Wechsel Ihrer Mobile ID SIM-Karte oder bei einem Verlust Ihrer Mobile-ID-PIN, muss Mobile ID über das Internetportal www.mobileid.ch mit den Selbsthilfe-Werkzeugen erneut aktiviert werden. In solchen Fällen ermöglicht ein Wiederherstellungscode, dass bereits genutzte Verknüpfungen mit Dienstanbietern beibehalten werden.',
	answer2:
		'Notieren Sie Ihren Wieder­herstellungs­code oder drucken Sie ihn aus. Der Wieder­herstellungs­code sollte nicht auf Ihrem Computer oder Mobilgerät gespeichert werden. Bewahren Sie Ihren Wieder­herstellungs­code an einem sicheren Ort auf.',
	recoveryCodeFat: 'Wieder­herstellungs­code',
	serialNoLabel: 'Ihre eindeutige Mobile ID Seriennummer lautet:',
	replacingInformation:
		'Dieser Code ersetzt bereits vorhandene Wiederherstellungscodes.',
	appInformation:
		'Bitte beachten Sie, dass aktuell nicht alle Mobile ID Dienstanbieter die Mobile ID App unterstützen.',
	issuer: 'und wurde von %{issuer} ausgestellt.',
};

export const processesDE = {
	DeactivationConfirmation: {
		subtitle: {
			SimDeactivation:
				'Sie möchten die Mobile ID SIM deaktivieren. Bitte bestätigen Sie diesen Vorgang.',
			AppDeactivation:
				'Sie möchten die Mobile ID App deaktivieren. Bitte bestätigen Sie diesen Vorgang.',
		},
		warning: {
			SimDeactivation:
				'Sie können die Mobile ID SIM später zu jedem Zeitpunkt erneut aktivieren.',
			AppDeactivation:
				'Sie können die Mobile ID App später zu jedem Zeitpunkt erneut aktivieren.',
		},
		confirmationText: {
			SimDeactivation:
				'Ich bestätige, dass ich die SIM Methode deaktivieren möchte.',
			AppDeactivation:
				'Ich bestätige, dass ich die App Methode deaktivieren möchte.',
		},
		confirmationText2: 'Ich bestätige, dass ich meinen ',
		recoveryCodeHighlight: 'Wiederherstellungscode',
		confirmationText3: ' für eine allfällige erneute Aktivierung kenne.',
		warningTitle: {
			SimDeactivation: 'Achtung',
			AppDeactivation: 'Achtung',
		},
		warningText: {
			SimDeactivation:
				'Es gibt Dienstanbieter, welche ausschliesslich die SIM Methode als Authentisierung zulassen. Wenn Sie mit dem Deaktivieren fortfahren, verlieren Sie möglicherweise den Zugang zu diesen Dienstanbietern.',
			AppDeactivation:
				'Es gibt Dienstanbieter, welche ausschliesslich die App Methode als Authentisierung zulassen. Wenn Sie mit dem Deaktivieren fortfahren, verlieren Sie möglicherweise den Zugang zu diesen Dienstanbietern.',
		},
		warningQuestion: {
			SimDeactivation:
				'Möchten Sie mit dem Deaktivieren der SIM Methode fortfahren?',
			AppDeactivation:
				'Möchten Sie mit dem Deaktivieren der App Methode fortfahren?',
		},
		errors: {
			ERROR_NO_CONFIRMATION:
				'Bitte bestätigen Sie, dass Sie die Methode deaktivieren möchten und dass Sie Ihren Wiederherstellungscode kennen.',
		},
		tooltipText: {
			hasRecoveryCode:
				'Für eine spätere erneute Aktivierung sollten Sie Ihren Wiederherstellungscode kennen. Sie können jederzeit einen neuen Wiederherstellungscode generieren.',
			hasNoRecoveryCode:
				'Für eine spätere erneute Aktivierung sollten Sie einen Wiederherstellungscode generieren.',
		},
	},
	TermsAndConditions: {
		subtitle:
			'Durch die Verwendung unserer Dienste stimmen Sie diesen Nutzungsbedingungen zu. Bitte lesen Sie diese sorgfältig durch.',
		termsAndConditions1: {
			METHOD_SIM: 'Ich stimme den ',
			METHOD_APP: 'Ich akzeptiere die ',
		},
		termsAndConditions2: {
			METHOD_SIM: ' zu.',
			METHOD_APP: ' und nehme die ',
		},
		termsAndConditionsLinkLabel: {
			METHOD_SIM: 'Nutzungsbestimmungen',
			METHOD_APP: 'Nutzungsbestimmungen',
		},
		termsAndConditionsLink: {
			METHOD_SIM:
				'https://documents.swisscom.com/product/filestore/lib/164b8177-d292-409e-ad99-a92930493fed/mid_sim_nb-de.pdf',
			METHOD_APP:
				'https://documents.swisscom.com/product/filestore/lib/fef40f85-1ced-452d-9903-5d91a9c6e13f/mid_app_nb-de.pdf',
		},
		termsAndConditionsLinkLabel2: {
			METHOD_SIM: '',
			METHOD_APP: 'Datenschutzerklärung',
		},
		termsAndConditionsLink2: {
			METHOD_SIM: '',
			METHOD_APP:
				'https://documents.swisscom.com/product/filestore/lib/7091ff3f-d623-448a-8362-e26d587e3ae1/mid_app_ps_eu_ch-de.pdf',
		},
		termsAndConditions3: {
			METHOD_SIM: '',
			METHOD_APP: ' zur Kenntnis.',
		},
		errors: {
			ERROR_TERMS_NOT_ACCEPTED:
				'Bitte akzeptieren Sie die Nutzungsbestimmungen.',
		},
	},
	ChooseMethod: {
		subtitle: {
			Activation: 'Welche Methode möchten Sie aktivieren?',
			TestSignature: 'Welche Methode möchten Sie testen?',
		},
		errors: {
			ERROR_NO_METHOD: 'Bitte wählen Sie eine Methode aus.',
		},
		orderSim: 'SIM bestellen',
		information: {
			METHOD_SIM:
				'Die SIM wird aktuell durch alle Mobile ID Dienstanbieter unterstützt.',
			METHOD_APP:
				'Die App wird aktuell noch nicht durch alle Mobile ID Dienstanbieter unterstützt.',
		},
		confirmation: {
			title: 'Vorsicht: Aktives Konto erkannt',
			desc1: `Sie haben derzeit ein aktives Mobile ID App-Konto.
				Wenn Sie sich entscheiden, mit einer neuen Aktivierung fortzufahren, wird Ihr bestehendes Konto vorübergehend deaktiviert.
				Sie müssen dann den neuen Aktivierungsprozess abschließen, um Ihr Konto wieder zu aktivieren.`,
			desc2: `Sie benötigen Ihren Wiederherstellungscode, um Ihre Verbindungen zu Dienstanbietern aufrechtzuerhalten.
				Wenn Sie ihn nicht mehr wissen, erstellen Sie bitte einen`,
			link: 'neuen Wiederherstellungscode',
			desc3: ', bevor Sie fortfahren.',
			confirmation: 'Ich verstehe und möchte fortfahren',
		},
	},
	Processing: {
		subtitle: {
			METHOD_SIM: 'Bitte schliessen Sie den Prozess auf Ihrem Mobiltelefon ab',
			METHOD_APP: 'Bitte schliessen Sie den Prozess mit Ihrer App ab',
			RECOVERY_METHOD_SECOND_METHOD: {
				sim: 'Bitte beantworten Sie die Anfrage auf Ihrem Mobiltelefon',
				app: 'Bitte beantworten Sie die Anfrage in Ihrer App',
			},
			SimDeactivation:
				'Die SIM Methode wird deaktiviert. Bitte haben warten Sie einen Moment.',
			AppDeactivation:
				'Die App Methode wird deaktiviert. Bitte haben warten Sie einen Moment.',
		},
		qrCodeInstructions: {
			part1: 'Laden Sie die kostenlose Mobile ID App herunter ',
			part2:
				' und scannen Sie diesen QR-Code mit der Funktion „+ Account hinzufügen“ in der App. Dieser QR-Code ist für 10 Minuten gültig.',
			comingSoon: 'demnächst verfügbar',
			generalInstructions: {
				part1:
					'Bitte befolgen Sie die unten aufgeführten Schritte, um die Mobile-ID Aktivierung für ',
				part2: ' abzuschließen.',
				part3:
					'Bitte installieren Sie die Mobile ID App aus dem App Store und folgen Sie den Anweisungen in der App, um ein Konto zu erstellen. Bitte kehren Sie anschließend auf diese Seite zurück, um den Anmeldevorgang abzuschließen.',
				watchVideo: 'Videotutorial anschauen',
			},
			step1title: 'Schritt 1',
			step1: 'Installieren Sie die Mobile-ID App aus dem App Store',
			step2title: 'Schritt 2',
			step2a: 'Scannen Sie den QR Code mit der Mobile-ID App',
			step2b: 'ODER',
			step2c: 'Klicken Sie auf dieses Icon',
		},
		download: {
			dowloadLinkGooglePlay:
				'https://play.google.com/store/apps/details?id=com.swisscom.mobileid&hl=de',
			downloadLinkAppStore:
				'https://itunes.apple.com/ch/app/mobile-id/id1500393675?mt=8&l=de',
		},
		abortPrompt: 'Sind Sie sicher, dass Sie den Vorgang abbrechen wollen?',
	},
	Result: result,
	ChooseRecoveryMethod: {
		subtitle:
			'Sie haben die Möglichkeit, Ihre Mobile ID wiederherzustellen. Wählen Sie dazu eine für Sie passende Auswahl.',
		noRecoveryReason: {
			noneOfTheAbove: 'Ich möchte ohne Wiederherstellungsoption fortfahren',
			cannotUseApp: 'Ich möchte ohne Wiederherstellungsoption fortfahren',
			cannotUseRecovery: 'Ich möchte ohne Wiederherstellungsoption fortfahren',
		},
		useRecoveryMethod: 'Ich kenne meinen Mobile ID Wiederherstellungscode',
		useSecondMethod: {
			METHOD_APP: 'Ich authentisiere mich mit meiner Mobile ID SIM Karte',
			METHOD_SIM: 'Ich authentisiere mich mit meiner Mobile ID App',
		},
		warning: {
			title: 'Achtung',
			text:
				'Wenn Sie ohne Identifikation fortfahren, müssen Sie möglicherweise aus Sicherheitsgründen Ihre Mobile ID mit einigen Ihrer Dienstanbieter neu koppeln',
			question: 'Möchten Sie ohne Identifikation fortfahren?',
		},
		errors: {
			ERROR_NO_RECOVERY_METHOD:
				'Bitte wählen sie eine Wiederherstellungsmethode.',
		},
	},
	RecoveryCodeInput: {
		subtitle: 'Bitte geben Sie Ihren Wieder­herstellungs­code ein',
		errors: {
			'phone-activation/recovery-code-invalid':
				'Der von Ihnen eingegebene Wieder­herstellungs­code ist ungültig. Bitte versuchen Sie es noch einmal.',
			'phone-activation/recovery-code-too-many-attempts':
				'Der Wieder­herstellungs­code wurde zu oft falsch eingegeben. Bitte versuchen Sie es später noch einmal.',
			ERROR_RECOVERY_CODE_INVALID:
				'Bitte geben Sie den kompletten Wieder­herstellungs­code ein.',
		},
		tooltip:
			'Bitte beachten Sie, dass ein Wiederherstellungscode jeweils nur einmal verwendet werden kann. Sie erhalten nach einer erfolgreichen Aktivierung einen neuen Wiederherstellungscode.',
	},
	mobileEnroll: {},
};
