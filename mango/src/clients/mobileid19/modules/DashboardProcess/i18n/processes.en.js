const success = {
	Preparation: {
		title: 'Success',
		subtitle: '',
	},
	null: {
		title: 'Success',
		subtitle: '',
	},
	undefined: {
		title: 'Success',
		subtitle: '',
	},
	SimActivation: {
		title: 'Success',
		subtitle: 'Your SIM method has been activated',
	},
	AppActivation: {
		title: 'Success',
		subtitle: 'Your app has been activated',
	},
	ResetPin: {
		title: 'Success',
		subtitle: 'Your PIN has been changed',
	},
	GenerateRecoveryCode: {
		title: 'Success',
		subtitle: 'A new recovery code has been generated',
	},
	TestSignature: {
		title: 'Success',
		subtitle: 'Your test was successful',
	},
	SimDeactivation: {
		title: 'Success!',
		subtitle: 'You have successfully deactivated the SIM method.',
		text1: 'You can reactivate the Mobile ID SIM again at any time.',
		text2: '',
	},
	AppDeactivation: {
		title: 'Success!',
		subtitle: 'You have successfully deactivated the app method.',
		text1:
			'Important: Please uninstall the Mobile ID app from your device to complete the process.',
		text2:
			'You can reactivate the Mobile ID app again at any time. To do this, you will have to reinstall the Mobile ID app on your device.',
	},
};

const error = {
	'phone-activation/mid-auth-test-card-blocked': {
		title: 'Your Mobile\u00A0ID test run was unfortunately not successful.',
		subtitle: 'Please try again or contact your mobile operator.',
	},
	'phone-activation/mid-auth-test-error': {
		title: 'Your Mobile\u00A0ID test run was unfortunately not successful.',
		subtitle: 'Please try again or contact your mobile operator.',
	},
	'phone-activation/error/timeout': {
		title: 'The process took too long.',
		subtitle: 'Please try again!',
	},
	'phone-activation/mid-auth-test-expired-transaction': {
		title: 'Your Mobile\u00A0ID test run was not answered in time.',
		subtitle: 'Please try again or contact your mobile operator.',
		testSignatureButton: 'Retest',
	},
	TestSignature: {
		title: 'Error',
		subtitle: 'The test was unsuccessful',
	},
	'phone-activation/mid-auth-test-unknown-client': {
		title: 'Your Mobile\u00A0ID test run was unfortunately not successful.',
		subtitle: 'Your SIM card does not yet support the service.',
		orderButton: 'Order SIM',
	},
	ResetPin: {
		title: 'Error',
		subtitle: 'The PIN reset has failed',
	},
	'phone-activation/mid-auth-test-user-cancel': {
		title: 'You have cancelled your Mobile\u00A0ID test run.',
		subtitle: 'Please try again or contact your mobile operator.',
		testSignatureButton: 'Retest',
	},
	SimActivation: {
		title: 'Error',
		subtitle: 'The activation has failed',
	},
	'phone-activation/mid-auth-test-pin-nr-blocked': {
		title: 'Your Mobile\u00A0ID PIN is blocked.',
		subtitle: 'Please reset your Mobile\u00A0ID PIN.',
		resetPinButton: 'PIN Reset',
	},
	'phone-activation/mid-auth-test-invalid-signed-data': {
		title: 'Your Mobile\u00A0ID test run was unfortunately not successful.',
		subtitle: 'Please try again or contact your mobile operator.',
		testSignatureButton: 'Retest',
	},
	'phone-activation/mid-auth-test-no-cert-found': {
		title: 'Your Mobile\u00A0ID is no longer active.',
		subtitle: 'Please activate Mobile\u00A0ID.',
		activationButton: 'Try now',
	},
	'phone-activation/mid-auth-test-pb-signature-process': {
		title: 'A Mobile\u00A0ID test run has already been started.',
		subtitle: 'Please try again at a later time.',
		testSignatureButton: 'Retest',
	},
	GenerateRecoveryCode: {
		title: 'Error',
		subtitle: 'The generation of a recovery code has failed',
	},
	subtitle: 'Sorry, your Mobile\u00A0ID isn’t working.',
	title: 'Test failed',
	AppActivation: {
		title: 'Error',
		subtitle: 'The activation has failed',
	},
	'phone-activation/mid-auth-test-no-key-found': {
		title: 'You have not yet activated Mobile\u00A0ID.',
		subtitle: 'Please activate Mobile\u00A0ID.',
		activationButton: 'Try now',
	},
	SimDeactivation: {
		title: 'Error',
		subtitle: 'The SIM method could not be deactivated.',
	},
	AppDeactivation: {
		title: 'Error',
		subtitle: 'The app method could not be deactivated.',
	},
};

const mobileEnroll = {
	title: 'Your Mobile ID is ready',
	p1:
		'If you ever need to recover access to your account, this code will help. You should write it down, and store it in a safe place.',
	p2:
		'If you previously had a recovery code, it is no longer valid. Use this new code instead.',
	recoveryCodeLabel: 'Your recovery code is',
};

const result = {
	success,
	error,
	mobileEnroll,
	question1: 'What is a Mobile ID recovery code?',
	recoveryCodeInstruction:
		'Please record your recovery code and store it in a safe place.',
	question2: 'Where should I keep the recovery code?',
	recoveryCodeText: 'Below you can see your ',
	recoveryCodeLabel: 'Your recovery code:',
	answer1:
		'If you change your Mobile ID SIM card or lose your Mobile ID PIN, you will need to reactivate Mobile ID using the self-help tools on the online portal www.mobileid.ch. The recovery code allows you to retain your existing connections to service providers in such cases.',
	answer2:
		'Write down your recovery code or print it out. Do not store the recovery code on your computer or mobile device. Keep your recovery code in a safe place.',
	recoveryCodeFat: 'Recovery code',
	serialNoLabel: 'Your unique Mobile ID serial number is:',
	replacingInformation: 'This code replaces existing recovery codes.',
	appInformation:
		'Please note that not all Mobile ID service providers currently support the Mobile ID app.',
	issuer: 'and was issued by %{issuer}',
};

export const processesEN = {
	DeactivationConfirmation: {
		subtitle: {
			SimDeactivation:
				'You want to deactivate the Mobile ID SIM. Please confirm this process.',
			AppDeactivation:
				'You want to deactivate the Mobile ID app. Please confirm this process.',
		},
		warning: {
			SimDeactivation:
				'You can reactivate the Mobile ID SIM again at any time.',
			AppDeactivation:
				'You can reactivate the Mobile ID app again at any time.',
		},
		confirmationText: {
			SimDeactivation: 'I confirm that I want to deactivate the SIM method.',
			AppDeactivation: 'I confirm that I want to deactivate the app method.',
		},
		confirmationText2: 'I confirm that I know my ',
		recoveryCodeHighlight: 'recovery code',
		confirmationText3: ' in the event that I need to reactivate this option.',
		warningTitle: {
			SimDeactivation: 'Caution',
			AppDeactivation: 'Caution',
		},
		warningText: {
			SimDeactivation:
				'Some service providers only allow you to use the SIM method for authentication. If you continue with deactivation, you may lose access to these service providers.',
			AppDeactivation:
				'Some service providers only allow you to use the app method for authentication. If you continue with deactivation, you may lose access to these service providers.',
		},
		warningQuestion: {
			SimDeactivation:
				'Would you like to continue deactivating the SIM method?',
			AppDeactivation:
				'Would you like to continue deactivating the app method?',
		},
		errors: {
			ERROR_NO_CONFIRMATION:
				'Please confirm that you want to deactivate the method and that you know your recovery code.',
		},
		tooltipText: {
			hasRecoveryCode:
				'If you want to reactivate this option later, you will need to know your recovery code. You can generate a new recovery code at any time.',
			hasNoRecoveryCode:
				'Be sure to generate a recovery code so that you can reactivate this option later.',
		},
	},
	TermsAndConditions: {
		welcome: 'Welcome to Mobile ID',
		agreeAndContinue: 'Agree & Continue',
		mobileEnroll: {
			part1: 'Read our ',
			part2: '. Tap "Agree & Continue" to accept the',
		},
		subtitle:
			'By using our Services, you are agreeing to these terms. Please read them carefully.',
		termsAndConditions1: {
			METHOD_SIM: 'I agree to ',
			METHOD_APP: 'I agree to the ',
		},
		termsAndConditions2: {
			METHOD_SIM: '.',
			METHOD_APP: ' and acknowledge the ',
		},
		termsAndConditionsLinkLabel: {
			METHOD_SIM: 'terms and conditions',
			METHOD_APP: 'terms and conditions',
		},
		termsAndConditionsLink: {
			METHOD_SIM:
				'https://documents.swisscom.com/product/filestore/lib/80d6ac54-5e2c-4ac5-addf-d40219a8af63/mid_sim_nb-en.pdf',
			METHOD_APP:
				'https://documents.swisscom.com/product/filestore/lib/51efb547-ece1-4866-a71d-db6cf4db3d7b/mid_app_nb-en.pdf',
		},
		termsAndConditionsLinkLabel2: {
			METHOD_SIM: '',
			METHOD_APP: 'privacy statement',
		},
		termsAndConditionsLink2: {
			METHOD_SIM: '',
			METHOD_APP:
				'https://documents.swisscom.com/product/filestore/lib/3e48e7e8-fd61-4c82-a496-2fae95055931/mid_app_ps_eu_ch-en.pdf',
		},
		termsAndConditions3: {
			METHOD_SIM: '',
			METHOD_APP: '.',
		},
		errors: {
			ERROR_TERMS_NOT_ACCEPTED: 'Please accept the terms of use.',
		},
	},
	ChooseMethod: {
		subtitle: {
			Activation: 'Which method would you like to activate?',
			TestSignature: 'Which method would you like to test?',
		},
		errors: {
			ERROR_NO_METHOD: 'Please select a method.',
		},
		orderSim: 'Order SIM',
		information: {
			METHOD_SIM:
				'The SIM is currently supported by all Mobile ID service providers.',
			METHOD_APP:
				'The app is not currently supported by all Mobile ID service providers.',
		},
		confirmation: {
			title: 'Caution: Active Account Detected',
			desc1: `You currently have an active Mobile ID App account.
				If you choose to proceed with a new activation, your existing account will be temporarily deactivated.
				You must then complete the new activation process to reactivate your account.`,
			desc2: `You will need your recovery code, which is crucial for retaining your connections with service providers.
				If you don't remember it, please`,
			link: 'create a new recovery code',
			desc3: ' before proceeding.',
			confirmation: 'I understand and want to proceed',
		},
	},
	Processing: {
		subtitle: {
			METHOD_SIM: 'Please complete the process on your mobile phone',
			METHOD_APP: 'Please complete the process in your app',
			RECOVERY_METHOD_SECOND_METHOD: {
				sim: 'Please respond to the request on your mobile phone',
				app: 'Please respond to the request in your app',
			},
			SimDeactivation:
				'The SIM method is being deactivated. Please wait a moment.',
			AppDeactivation:
				'The app method is being deactivated. Please wait a moment.',
		},
		qrCodeInstructions: {
			part1: 'Download the free Mobile ID App ',
			part2:
				' and scan this QR code in the app using the function ‘+ Add account’. This QR code is valid for 10 minutes.',
			comingSoon: 'coming soon',
			generalInstructions: {
				part1:
					'Please follow the steps below to complete the Mobile-ID activation for ',
				part2: ' .',
				part3:
					'Please install the Mobile ID app from the App Store and follow the instructions in the app to create an account. Please return to this page afterwards to complete the sign-in process.',
				watchVideo: 'Watch video tutorial',
			},
			step1title: 'Step 1',
			step1: 'Install the Mobile-ID App',
			step2title: 'Step 2',
			step2a: 'Scan the QR code with your Mobile-ID App',
			step2b: 'OR',
			step2c: 'Click the Mobile-ID icon',
		},
		download: {
			dowloadLinkGooglePlay:
				'https://play.google.com/store/apps/details?id=com.swisscom.mobileid&hl=en',
			downloadLinkAppStore:
				'https://itunes.apple.com/ch/app/mobile-id/id1500393675?mt=8&l=en',
		},
		abortPrompt: 'Are you sure you want to cancel the process?',
	},
	Result: result,
	ChooseRecoveryMethod: {
		subtitle:
			'You have the possibility to restore your Mobile ID. To do this, select an option that suits you.',
		noRecoveryReason: {
			noneOfTheAbove: 'I would like to continue without the restore option',
			cannotUseApp: 'I would like to continue without the restore option',
			cannotUseRecovery: 'I would like to continue without the restore option',
		},
		useRecoveryMethod: 'I know my Mobile ID recovery code',
		useSecondMethod: {
			METHOD_APP: 'I authenticate myself with my Mobile ID SIM card',
			METHOD_SIM: 'I authenticate myself with my Mobile ID App',
		},
		warning: {
			title: 'Caution',
			text:
				'If you would like to continue without identification, you may need to re-link your Mobile ID with some of your service providers for security reasons',
			question: 'Would you like to continue without identification?',
		},
		errors: {
			ERROR_NO_RECOVERY_METHOD: 'Please select a recovery method.',
		},
	},
	RecoveryCodeInput: {
		subtitle: 'Please enter your recovery code',
		errors: {
			'phone-activation/recovery-code-invalid':
				'The recovery code that you entered is not valid. Please try again.',
			'phone-activation/recovery-code-too-many-attempts':
				'The recovery code was entered too often incorrectly. Please try again later.',
			ERROR_RECOVERY_CODE_INVALID: 'Please enter the complete recovery code.',
		},
		tooltip:
			'Please note that a recovery code can only be used once. You will receive a new recovery code after a successful activation.',
	},
};
