/* @flow */
import {
	ActionsObservable,
	combineEpics,
	ofType,
	StateObservable,
} from 'redux-observable';
import { from, of } from 'rxjs';
import { catchError, delay, mergeMap } from 'rxjs/operators';
import { getRequestContext } from '../../../../modules/config/selectors';
import { open } from '../../../../modules/modal/actions';
import { CONNECTION_ERROR } from '../../../../modules/request/errors/constants';
import { RESULT_MODAL_NAME } from '../DashboardProcess/ResultModal/ResultModal';
import { getPhoneNumber, getSessionJwt } from '../mobileid-session/selectors';
import {
	processAborted,
	processOptionsChanged,
	processStarted,
	processStateChanged,
	type RefreshRequested,
	refreshRequested,
	canceledRefresh,
} from './actions';
import {
	DesiredProcessName,
	METHOD_APP,
	METHOD_SIM,
	ProcessName,
	ProcessState,
	PROCESS_OPTIONS_CHANGED,
	REFRESH_REQUESTED,
	RETURNED_TO_PREVIOUS_STEP,
} from './constants';
import { epicsActivation } from './epicsActivation';
import { trackableError } from './epicsCommons';
import { epicsGenerateRc } from './epicsGenerateRc';
import { epicsPreparation } from './epicsPreparation';
import { epicsResetPin } from './epicsResetPin';
import { epicsTestSignature } from './epicsTestSignature';
import { MSG_ACTIVATION_ERROR, timeoutError } from './errors';
import { epicsDeactivation } from './epicsDeactivation';
import { getStatus } from './requests';
import {
	getInitialPossibleMethod,
	getProcessName,
	getProcessOptions,
	getProcessState,
} from './selectors';

const POLLING_INTERVAL = 2000; // in ms, 2s
const TIMEOUT_IN_MS = 600000; // in ms, 10min
const MAX_RETRIES = TIMEOUT_IN_MS / POLLING_INTERVAL;

async function updateActivationState(
	context,
	params: {
		phoneNumber: string,
		sessionJwt: string,
	},
): any {
	return await getStatus(context, params);
}

function createNextRefreshAction(action: RefreshRequested) {
	const { count } = action.payload;
	return refreshRequested(count + 1);
}

function refreshActivation(
	action$: ActionsObservable<*>,
	state$: StateObservable<any>,
): ActionsObservable<*> {
	return action$.pipe(
		ofType(REFRESH_REQUESTED),
		mergeMap((action: RefreshRequested) => {
			const state = state$.value;
			const requestContext = getRequestContext(state);
			const phoneNumber = getPhoneNumber(state);
			const sessionJwt = getSessionJwt(state);
			const processName = getProcessName(state);
			const params = { phoneNumber, sessionJwt };
			return from(updateActivationState(requestContext, params)).pipe(
				mergeMap(
					({
						simActivationStatus,
						appActivationStatus,
						appActivationMethod,
					}) => {
						switch (processName) {
							case ProcessName.AppActivation:
								if (appActivationStatus === 'ERROR') {
									return trackableError(MSG_ACTIVATION_ERROR);
								}

								if (
									appActivationStatus === 'SUCCESS' &&
									appActivationMethod === 'IN_APP'
								) {
									return [
										processStateChanged(ProcessState.Result, {
											hideRecoveryCode: true,
										}),
										open(RESULT_MODAL_NAME),
									];
								}

								if (appActivationStatus === 'SUCCESS') {
									return [
										processStateChanged(ProcessState.Result),
										open(RESULT_MODAL_NAME),
									];
								}
								break;
							case ProcessName.SimActivation:
							case ProcessName.ResetPin:
								if (simActivationStatus === 'ERROR') {
									return trackableError(MSG_ACTIVATION_ERROR);
								}

								if (simActivationStatus === 'SUCCESS') {
									return [
										processStateChanged(ProcessState.Result),
										open(RESULT_MODAL_NAME),
									];
								}
								break;
							default:
								break;
						}

						if (!processName) {
							return of(canceledRefresh());
						}

						const { count } = action.payload;
						if (count > MAX_RETRIES) {
							return trackableError(timeoutError());
						}

						return of(createNextRefreshAction(action)).pipe(
							delay(POLLING_INTERVAL),
						);
					},
				),
				catchError((error) => {
					const { count } = action.payload;
					if (count > MAX_RETRIES) {
						return of(
							processStateChanged(ProcessState.Result, {
								error: timeoutError(),
							}),
							open(RESULT_MODAL_NAME),
						);
					}

					if (error.type === CONNECTION_ERROR) {
						return of(createNextRefreshAction(action)).pipe(
							delay(POLLING_INTERVAL),
						);
					}
					return of(
						processStateChanged(ProcessState.Result, {
							error,
						}),
						open(RESULT_MODAL_NAME),
					);
				}),
			);
		}),
	);
}

function handleProcessOptionsUpdate(
	action$: ActionsObservable<*>,
	state$: StateObservable<any>,
): ActionsObservable<*> {
	return action$.pipe(
		ofType(PROCESS_OPTIONS_CHANGED),
		mergeMap(() => {
			const state = state$.value;
			const processOptions = getProcessOptions(state);

			const {
				desiredProcess,
				method,
				recoveryMethod,
				termsAndConditionsAccepted,
			} = processOptions;

			switch (desiredProcess) {
				case DesiredProcessName.Activation:
					if (!method) {
						return [processStateChanged(ProcessState.ChooseMethod)];
					}
					if (!termsAndConditionsAccepted) {
						return [processStateChanged(ProcessState.TermsAndConditions)];
					}
					if (!recoveryMethod) {
						return [processStateChanged(ProcessState.ChooseRecoveryMethod)];
					}
					if (method === METHOD_SIM) {
						return [processStarted(ProcessName.SimActivation, processOptions)];
					}
					if (method === METHOD_APP) {
						return [processStarted(ProcessName.AppActivation, processOptions)];
					}
					break;
				case DesiredProcessName.ResetPin:
					if (!termsAndConditionsAccepted) {
						return [processStateChanged(ProcessState.TermsAndConditions)];
					}
					if (!recoveryMethod) {
						return [processStateChanged(ProcessState.ChooseRecoveryMethod)];
					}
					return [processStarted(ProcessName.ResetPin, processOptions)];
				case DesiredProcessName.GenerateRecoveryCode:
					return [
						processStarted(ProcessName.GenerateRecoveryCode, processOptions),
					];
				case DesiredProcessName.TestSignature:
					if (!method) {
						return [processStateChanged(ProcessState.ChooseMethod)];
					}
					return [processStarted(ProcessName.TestSignature, processOptions)];

				case DesiredProcessName.AppDeactivation:
				case DesiredProcessName.SimDeactivation:
					return [];
				default:
					break;
			}
			return [processAborted()];
		}),
	);
}

function handleReturnToPreviousStep(
	action$: ActionsObservable<*>,
	state$: StateObservable<any>,
): ActionsObservable<*> {
	return action$.pipe(
		ofType(RETURNED_TO_PREVIOUS_STEP),
		mergeMap(() => {
			const state = state$.value;
			const processName = getProcessName(state);
			const processState = getProcessState(state);
			const processOptions = getProcessOptions(state);
			const {
				desiredProcess,
				method,
				termsAndConditionsAccepted,
			} = processOptions;
			const initialPossibleMethod = getInitialPossibleMethod(state);
			switch (processName) {
				case ProcessName.SimActivation:
				case ProcessName.AppActivation:
					switch (processState) {
						case ProcessState.RecoveryCodeInput:
							return [
								processStarted(ProcessName.Preparation, {
									desiredProcess,
									method,
									recoveryMethod: undefined,
									termsAndConditionsAccepted,
								}),
							];
						default:
							return [processAborted()];
					}
				case ProcessName.ResetPin:
					switch (processState) {
						case ProcessState.RecoveryCodeInput:
							return [
								processStarted(ProcessName.Preparation, {
									desiredProcess,
									method,
									recoveryMethod: undefined,
									termsAndConditionsAccepted,
								}),
							];
						default:
							return [processAborted()];
					}
				case ProcessName.Preparation:
					if (
						desiredProcess &&
						(desiredProcess === DesiredProcessName.Activation ||
							desiredProcess === DesiredProcessName.ResetPin) &&
						processState &&
						processState === ProcessState.ChooseRecoveryMethod
					) {
						return [
							processOptionsChanged({
								desiredProcess,
								method,
								recoveryMethod: undefined,
								termsAndConditionsAccepted: undefined,
							}),
						];
					}
					if (
						desiredProcess &&
						desiredProcess === DesiredProcessName.Activation &&
						processState &&
						processState === ProcessState.TermsAndConditions &&
						!initialPossibleMethod
					) {
						return [
							processOptionsChanged({
								desiredProcess,
								method: undefined,
								recoveryMethod: undefined,
								termsAndConditionsAccepted: undefined,
							}),
						];
					}
					return [processAborted()];
				default:
					return [processAborted()];
			}
		}),
	);
}

const epic = combineEpics(
	epicsActivation,
	epicsGenerateRc,
	epicsResetPin,
	epicsTestSignature,
	refreshActivation,
	epicsPreparation,
	handleReturnToPreviousStep,
	handleProcessOptionsUpdate,
	epicsDeactivation,
);

export default epic;
