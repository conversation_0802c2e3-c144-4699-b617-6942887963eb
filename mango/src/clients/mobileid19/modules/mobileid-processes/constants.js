/* @flow */
export const STATE_IDENTIFIER = 'mobileid-processes';

export const PROCESS_STARTED: 'mobileid-processes/PROCESS_REQUESTED' =
	'mobileid-processes/PROCESS_REQUESTED';
export const REFRESH_REQUESTED: 'mobileid-processes/REFRESH_REQUESTED' =
	'mobileid-processes/REFRESH_REQUESTED';
export const RECOVERY_CODE_RECEIVED: 'mobileid-processes/RECOVERY_CODE_RECEIVED' =
	'mobileid-processes/RECOVERY_CODE_RECEIVED';
export const PROCESS_STATE_CHANGED: 'mobileid-processes/PROCESS_STATE_CHANGED' =
	'mobileid-processes/PROCESS_STATE_CHANGED';
export const PROCESS_ABORTED: 'mobileid-processes/PROCESS_ABORTED' =
	'mobileid-processes/PROCESS_ABORTED';
export const PROCESS_OPTIONS_CHANGED: 'mobileid-processes/PROCESS_OPTIONS_CHANGED' =
	'mobileid-processes/PROCESS_OPTIONS_CHANGED';
export const SIM_ACTIVATION_WITH_RECOVERY_CODE_STARTED: 'mobileid-processes/SIM_ACTIVATION_WITH_RECOVERY_CODE_STARTED' =
	'mobileid-processes/SIM_ACTIVATION_WITH_RECOVERY_CODE_STARTED';
export const RETURNED_TO_PREVIOUS_STEP: 'mobileid-session/RETURNED_TO_PREVIOUS_STEP' =
	'mobileid-session/RETURNED_TO_PREVIOUS_STEP';
export const QR_CODE_URL_RECEIVED: 'mobileid-session/QR_CODE_URL_RECEIVED' =
	'mobileid-session/QR_CODE_URL_RECEIVED';

export const PROCESS_FINISHED: 'mobileid-session/PROCESS_FINISHED' =
	'mobileid-session/PROCESS_FINISHED';
export const CANCELED_REFRESH: 'mobileid-session/CANCELED_REFRESH' =
	'mobileid-session/CANCELED_REFRESH';

export const PREVENTED_SHOWING_RESULT_PAGE: 'mobileid-session/PREVENTED_SHOWING_RESULT_PAGE' =
	'mobileid-session/PREVENTED_SHOWING_RESULT_PAGE';

export const RECOVERY_METHOD_RECOVERY_CODE = 'RECOVERY_METHOD_RECOVERY_CODE';
export const RECOVERY_METHOD_SECOND_METHOD = 'RECOVERY_METHOD_SECOND_METHOD';
export const RECOVERY_METHOD_NO_RECOVERY = 'RECOVERY_METHOD_NO_RECOVERY';

export const METHOD_APP = 'METHOD_APP';
export const METHOD_SIM = 'METHOD_SIM';

export const ProcessName = {
	Preparation: 'Preparation',
	SimActivation: 'SimActivation',
	AppActivation: 'AppActivation',
	ResetPin: 'ResetPin',
	GenerateRecoveryCode: 'GenerateRecoveryCode',
	TestSignature: 'TestSignature',
	SimDeactivation: 'SimDeactivation',
	AppDeactivation: 'AppDeactivation',
};

export const DesiredProcessName = {
	Activation: 'Activation',
	ResetPin: 'ResetPin',
	GenerateRecoveryCode: 'GenerateRecoveryCode',
	TestSignature: 'TestSignature',
	SimDeactivation: 'SimDeactivation',
	AppDeactivation: 'AppDeactivation',
};

export const ProcessState = {
	ChooseMethod: 'ChooseMethod',
	ChooseRecoveryMethod: 'ChooseRecoveryMethod',
	RecoveryCodeInput: 'RecoveryCodeInput',
	Processing: 'Processing',
	Result: 'Result',
	TermsAndConditions: 'TermsAndConditions',
	DeactivationConfirmation: 'DeactivationConfirmation',
};
