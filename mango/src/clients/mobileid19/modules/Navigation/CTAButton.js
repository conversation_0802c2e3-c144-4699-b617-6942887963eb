import React from 'react';
import { withRouter } from 'react-router-dom';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { MailIcon } from './MailIcon';
import styles from './CTAButton.css';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import { namespace } from './i18n';
import { localize, type LocalizeProps } from '../../../../modules/i18n';
import { Link } from '../../../../tango/routing';
import { contactRoute } from '../../App/routingConfig';
import { getIsMobileAppIntegration } from '../../views/MyMobileId/utils';

function CTAButton({ t, location, locale }: Props & LocalizeProps) {
	const isContactRoute = location.pathname.includes(
		contactRoute.path[locale].replace(':locale', locale),
	);

	if (isContactRoute || getIsMobileAppIntegration(location.search)) {
		return null;
	}
	return (
		<Link to="contact">
			<div className={styles.ctaContainer}>
				<div className={styles.ctaText}>
					{t('callToAction')
						.split(' ')
						.map((v, i) => (
							<React.Fragment key={i}>{v}&nbsp;</React.Fragment>
						))}
				</div>
				<MailIcon />
			</div>
		</Link>
	);
}

const wrapped = combineHOCs([
	withRouter,
	withStyles(styles),
	localize(namespace),
])(CTAButton);

export { wrapped as CTAButton };
