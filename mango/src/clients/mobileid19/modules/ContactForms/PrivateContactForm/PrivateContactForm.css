@import '../../../assets/variables.css';

.form {
	position: relative;
	background: var(--white);

	&.fullGray {
		@apply --container;
		border-radius: 0;
		max-width: 100%;
		background: var(--light-gray);

		& input[type='text'], & input[type='email'], & textarea {
			background: var(--white);
		}

		& .content {
			max-width: 640px;
			margin: auto;
		}
	}

	&:before {
		content: '';
		display: block;
		position: absolute;
		width: calc(100% - 20px);
		height: 10px;
		bottom: -10px;
		left: 50%;
		transform: translateX(-50%);
		background: var(--medium-gray);
		z-index: -1;
		border-bottom-left-radius: 5px;
		border-bottom-right-radius: 5px;

		@media (--screen-mobile) {
			display: none;
			content: none;
		}
	}

	& .stamp {
		height: 120px;
		width: 120px;
		position: absolute;
		left: -60px;
		top: -60px;

		@media (--screen-mobile) {
			height: 60px;
			width: 60px;
			position: absolute;
			left: 10px;
			top: -30px;
		}
	}

	& .button {
		position: relative;
		left: 50%;
		transform: translateX(-50%);
		margin: 0 0 10px;
	}
}

.recaptcha {
	@apply --recaptcha;
}

.errorRecaptcha {
	@apply --successRecaptcha;
}

.successRecaptcha {
	@apply --successRecaptcha;
}

.phoneContainer {
	display: flex;
}

.countryCode {
	flex-basis: 30%;
}

.phone {
	margin-left: 40px;
	flex-basis: 70%;
}

.contentContainer {
	max-width: 1024px;
	margin: auto;
	padding: 39px 45px;

	& button.resendButton {
		position: relative;
		color: var(--primary-color);
		text-decoration: underline;
		background: none;
		padding: 0;
		font-weight: normal;
		text-transform: none;
	
		&:hover:not([disabled]) {
			background: none;
		}
	
		& .resendIcon {
			position: absolute;
			display: block;
			margin: auto;
			margin: 0;
			width: 29px;
			height: 29px;
			-webkit-animation: fadeinout 3s linear forwards;
			animation: fadeinout 3s linear forwards;
			top: 50%;
			right: -8px;
			transform: translate(100%, -50%);
		}
	}
	
	& .buttonContainer {
		display: flex;
		justify-content: flex-end;
		flex-basis: 100%;
	
		& .backButton {
			margin-right: 24px;
	
			@media (--screen-mobile) {
				margin-right: 0;
			}
		}
	}
	
	& .errorMsg {
		display: block;
		margin-top: 12px;
	}
}