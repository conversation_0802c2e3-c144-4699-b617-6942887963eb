const namespace = 'mobileid19.views.myMobileId';
const phrases = {
	de: {
		app: 'App',
		eSignature: {
			title: 'Rechtsgültig signieren mit Mobile\u00A0ID',
			notQualified: {
				point1:
					'Identifizieren Sie sich, um alle Anforderungen für die rechtsgültige elektronische Signatur zu erfüllen.',
				point2: '',
				point3: '',
			},
			chQualified: {
				point1:
					'Sie erfüllen alle Anforderungen für rechtsgültige elektronische Signaturen im Raum CH.',
				point2: '',
				point3: '',
			},
			euQualified: {
				point1:
					'Sie erfüllen alle Anforderungen für rechtsgültige elektronische Signaturen im Raum E.U.',
				point2: '',
				point3: '',
			},
			bothQualified: {
				point1:
					'Sie erfüllen alle Anforderungen für rechtsgültige elektronische Signaturen im Raum CH und E.U.',
				point2: '',
				point3: '',
			},
			buttonText: 'mehr infos',
			modal: {
				point2: {
					title:
						'Warum macht es Sinn, für die elektronische Signatur registriert zu sein?',
					paragraph1:
						'Prozesse werden immer "digitaler" und Onlinedienstanbieter bieten immer öfter die elektronische Signatur an, um Geschäfte abzuschliessen. Wenn Sie registriert sind, können Sie in Zukunft bei vielen Anbietern Versicherungen, Online-Käufe, Bankgeschäfte oder allgemeine Verträge, die normalerweise eine handschriftliche Unterschrift benötigen, online abschliessen. Die qualifizierte elektronische Signatur von Swisscom ist nämlich der handschriftlichen Unterschrift gleichgestellt.',
					paragraph2:
						'Sie sparen Zeit und Geld: kein Ausdrucken, kein Scannen, kein Gang zur Post',
					paragraph3: 'Weitere Informationen finden Sie',
					link: 'https://srsident.trustservices.swisscom.com/en/',
					linkText: 'hier',
				},
			},
		},
		testSignature: {
			title: 'Mobile\u00A0ID testen',
			simActive: {
				point1: 'Testen Sie Ihre SIM Methode.',
				point2: '',
				point3: '',
			},
			appActive: {
				point1: 'Testen Sie Ihre APP Methode.',
				point2: '',
				point3: '',
			},
			bothActive: {
				point1: 'Testen Sie Ihre SIM oder APP Methode.',
				point2: '',
				point3: '',
			},
			buttonText: 'Testen',
		},
		resetPin: {
			title: 'Mobile\u00A0ID PIN zurücksetzen',
			point1:
				'Definieren Sie einen neuen Mobile\u00A0ID PIN für Ihre SIM Methode.',
			point2: '',
			point3: '',
			buttonText: 'Zurücksetzen',
		},
		download: {
			title: 'Nutzen Sie die Mobile\u00A0ID App',
			dowloadLinkGooglePlay:
				'https://play.google.com/store/apps/details?id=com.swisscom.mobileid&hl=de',
			downloadLinkAppStore:
				'https://itunes.apple.com/ch/app/mobile-id/id1500393675?mt=8&l=de',
			point1: 'Laden Sie die Mobile\u00A0ID App jetzt kostenlos herunter.',
			point2: '',
			point3: '',
		},
		activeMethods: 'Aktive Methoden',
		activeMethod: 'Aktive Methode',
		orderNewSimCard: {
			title: 'Nutzen Sie die Mobile\u00A0ID SIM-Karte',
			point1:
				'Höchste Sicherheit und Kompatibilität erhalten Sie mit der Mobile\u00A0ID SIM.',
			point2:
				'Bestellen Sie eine Mobile\u00A0ID fähige SIM bei Ihrem Mobilfunkanbieter.',
			point3: '',
			links: {
				swisscom:
					'https://www.swisscom.ch/de/privatkunden/hilfe/mobile/sim-sperren-ersetzen.html',
				coopMobile: 'https://myaccount.coopmobile.ch/eCare/de/users/sign_in',
				sunrise:
					'https://www.sunrise.ch/de/residential/mysunrise/meine-produkte/deep-link-product-selector-page.html#/null/SimCard',
				salt: 'https://mobileid.salt.ch/de/',
				mbudget: 'https://selfcare.m-budget.migros.ch/eCare/de/users/sign_in',
				wingo: 'https://mywingo.wingo.ch/eCare/de/users/sign_in',
				upc: 'https://www.upc.ch/de/support/mobile/mobile-id/',
			},
		},
		activation: {
			title: 'Mobile\u00A0ID aktivieren',
			both: {
				point1:
					'Aktivieren Sie die sichere Zwei-Faktor-Authentifizierung mit Ihrer Mobile\u00A0ID SIM-Karte oder App.',
				point2: '',
				point3: '',
			},
			app: {
				point1:
					'Aktivieren Sie die sichere Zwei-Faktor-Authentifizierung mit Ihrer Mobile\u00A0ID App.',
				point2: '',
				point3: '',
			},
			buttonText: 'Aktivieren',
		},
		sim: 'SIM',
		generateRecoveryCode: {
			title: 'Wieder­herstellungs­code',
			hasRecoveryCode: {
				point1: 'Sie verfügen bereits über einen Wiederherstellungscode.',
				point2:
					'Sie können jederzeit einen neuen Wiederherstellungscode generieren.',
				point3:
					'Ein Wiederherstellungscode kann nur einmalig verwendet werden.',
				tooltip:
					'Für diesen Account existiert bereits ein Wiederherstellungscode. Sie können bei Bedarf einen neuen erstellen.',
			},
			hasNotRecoveryCode: {
				point1:
					'Für eine allfällige Wiederherstellung Ihrer Mobile\u00A0ID Methode sollten Sie jetzt einen Wiederherstellungscode generieren.',
				point2: '',
				point3: '',
				tooltip:
					'Für diesen Account wurde noch kein Wiederherstellungscode generiert.',
			},

			buttonText: 'Generieren',
		},
		phoneNumber: 'Telefonnummer',
		title: 'Willkommen auf Ihrem Mobile\u00A0ID Dashboard',
		simStatus: {
			title: 'SIM Status',
			active: {
				text: 'Aktiviert',
				tooltip:
					'Mobile\u00A0ID wurde auf Ihrer SIM Karte erfolgreich aktiviert.',
			},
			compatible: {
				text: 'Kompatibel. Nicht aktiviert.',
				tooltip:
					'Aktivieren Sie Mobile\u00A0ID jetzt kostenlos auf Ihrer SIM-Karte.',
			},
			notCompatible: {
				text: 'Nicht kompatibel',
				tooltip:
					'Bestellen Sie eine Mobile\u00A0ID fähige SIM bei Ihrem Mobilfunkanbieter.',
			},
		},
		appStatus: {
			title: 'APP Status',
			active: {
				text: 'Aktiviert',
				tooltip:
					'Ihre Mobile\u00A0ID App wurde erfolgreich aktiviert. Falls Sie die App neu installieren mussten, können sie eine erneute Aktivierung jederzeit durchführen.',
			},
			notActive: {
				text: 'Nicht aktiviert',
				tooltip:
					'Laden Sie die Mobile\u00A0ID App jetzt kostenlos herunter und aktivieren Sie die App auf Ihrem Mobile\u00A0ID Dashboard.',
			},
		},
		seo: {
			title: 'Ihr persönliches Mobile\u00A0ID Dashboard',
			description:
				'Das Mobile\u00A0ID Dashboard ist das Kontrollzentrum um Ihre Mobile\u00A0ID zu verwalten. Hier können Sie Ihre Mobile\u00A0ID aktivieren, sichern oder testen.',
		},
		loading: 'Ihr Dashboard wird geladen...',
		mobile_integration_loading: 'Bitte warten',
		deleteText: { app: 'Deaktivieren', sim: 'Deaktivieren' },
		test: {
			notAvailable: 'Zertifikatstest für Ihr Konto nicht verfügbar',
		},
	},
	en: {
		app: 'App',
		eSignature: {
			title: 'Legally valid signing with Mobile\u00A0ID',
			notQualified: {
				point1:
					'Identify yourself in order to meet all requirements for a legally valid electronic signature.',
				point2: '',
				point3: '',
			},
			chQualified: {
				point1:
					'You meet all requirements for legally valid electronic signatures in the CH area.',
				point2: '',
				point3: '',
			},
			euQualified: {
				point1:
					'You meet all requirements for legally valid electronic signatures in the E.U. area.',
				point2: '',
				point3: '',
			},
			bothQualified: {
				point1:
					'You meet all requirements for legally valid electronic signatures in the CH and E.U. area.',
				point2: '',
				point3: '',
			},
			buttonText: 'more info',
			modal: {
				point2: {
					title:
						'Why does it make sense to be registered for the electronic signature?',
					paragraph1:
						'Processes are becoming more and more "digital" and your online service providers are increasingly offering electronic signatures to complete processes. If you are registered, you can in future take out insurance, purchases, banking or general contracts with most online service providers that require a handwritten signature or online. Swisscom\'s electronic qualified signature is equivalent to the handwritten signature.',
					paragraph2: 'You save time and money: no printing, no scanning',
					paragraph3: 'You can find further information',
					link: 'https://srsident.trustservices.swisscom.com/',
					linkText: 'here',
				},
			},
		},
		testSignature: {
			title: 'Test Mobile\u00A0ID',
			simActive: {
				point1: 'Test your SIM method.',
				point2: '',
				point3: '',
			},
			appActive: {
				point1: 'Test your APP method.',
				point2: '',
				point3: '',
			},
			bothActive: {
				point1: 'Test your SIM or APP method.',
				point2: '',
				point3: '',
			},
			buttonText: 'Test',
		},
		resetPin: {
			title: 'Reset Mobile\u00A0ID PIN',
			point1: 'Define a new Mobile\u00A0ID PIN for your SIM method',
			point2: '',
			point3: '',
			buttonText: 'Reset',
		},

		download: {
			title: 'Use the Mobile\u00A0ID app',
			dowloadLinkGooglePlay:
				'https://play.google.com/store/apps/details?id=com.swisscom.mobileid&hl=en',
			downloadLinkAppStore:
				'https://itunes.apple.com/ch/app/mobile-id/id1500393675?mt=8&l=en',
			point1: 'Download the Mobile\u00A0ID App now for free.',
			point2: '',
			point3: '',
		},
		activeMethods: 'Active methods',
		activeMethod: 'Active method',
		orderNewSimCard: {
			title: 'Use the Mobile\u00A0ID SIM card',
			point1:
				'The Mobile\u00A0ID SIM provides you with maximum security and compatibility.',
			point2:
				'Order a Mobile\u00A0ID capable SIM from your mobile phone provider.',
			point3: '',
			links: {
				swisscom:
					'https://www.swisscom.ch/en/residential/help/mobile/block-replace-sim.html',
				coopMobile: 'https://myaccount.coopmobile.ch/eCare/en/users/sign_in',
				sunrise:
					'https://www.sunrise.ch/de/residential/mysunrise/meine-produkte/deep-link-product-selector-page.html#/null/SimCard',
				salt: 'https://mobileid.salt.ch/en/',
				mbudget: 'https://selfcare.m-budget.migros.ch/eCare/en/users/sign_in',
				wingo: 'https://mywingo.wingo.ch/eCare/en/users/sign_in',
				upc: 'https://www.upc.ch/de/support/mobile/mobile-id/',
			},
		},
		activation: {
			title: 'Activate Mobile\u00A0ID',
			both: {
				point1:
					'Activate secure two-factor authentication with your Mobile\u00A0ID SIM card or app.',
				point2: '',
				point3: '',
			},
			app: {
				point1:
					'Activate secure two-factor authentication with your Mobile\u00A0ID App.',
				point2: '',
				point3: '',
			},
			buttonText: 'Activate',
		},
		sim: 'SIM',
		generateRecoveryCode: {
			title: 'Recovery Code',
			hasRecoveryCode: {
				point1: 'You already have a recovery code.',
				point2: 'You can always generate a new recovery code.',
				point3: 'A recovery code can only be used once.',
				tooltip:
					'A recovery code already exists for this account. You can create a new one if necessary.',
			},
			hasNotRecoveryCode: {
				point1:
					'For a possible recovery of your Mobile\u00A0ID method you should now generate a recovery code.',
				point2: '',
				point3: '',
				tooltip: 'A recovery code has not yet been generated for this account.',
			},
			buttonText: 'Generate',
		},
		phoneNumber: 'Phone number',
		title: 'Welcome to your Mobile\u00A0ID dashboard',
		simStatus: {
			title: 'SIM Status',
			active: {
				text: 'Activated',
				tooltip: 'Mobile\u00A0ID was successfully activated on your SIM card.',
			},
			compatible: {
				text: 'Compatible. Not activated.',
				tooltip: 'Activate Mobile\u00A0ID now for free on your SIM card.',
			},
			notCompatible: {
				text: 'Not compatible',
				tooltip:
					'Order a Mobile\u00A0ID-enabled SIM from your mobile operator.',
			},
		},
		appStatus: {
			title: 'APP Status',
			active: {
				text: 'Activated',
				tooltip:
					'Your Mobile\u00A0ID app has been successfully activated. If you had to reinstall the app, you can activate it again at any time.',
			},
			notActive: {
				text: 'Not activated',
				tooltip:
					'Download the Mobile\u00A0ID app now for free and activate the app on your Mobile\u00A0ID Dashboard.',
			},
		},
		seo: {
			title: 'Your personal Mobile\u00A0ID Dashboard',
			description:
				'The Mobile\u00A0ID Dashboard is the control center for managing your Mobile\u00A0ID. Here you can activate, save or test your Mobile\u00A0ID.',
		},
		loading: 'Your dashboard is loading...',
		mobile_integration_loading: 'Please wait',
		deleteText: { app: 'Deactivate', sim: 'Deactivate' },
		test: {
			notAvailable: 'Certificate Test not available for your account',
		},
	},
	fr: {
		app: 'Application',
		eSignature: {
			title: 'Signature légalement valable avec Mobile\u00A0ID',
			notQualified: {
				point1:
					'Identifiez-vous afin de remplir toutes les conditions requises pour une signature électronique juridiquement valable.',
				point2: '',
				point3: '',
			},
			chQualified: {
				point1:
					'Vous remplissez toutes les conditions requises pour une signature électronique juridiquement valable dans le domaine de la CH.',
				point2: '',
				point3: '',
			},
			euQualified: {
				point1:
					"Vous remplissez toutes les conditions requises pour une signature électronique juridiquement valable dans la salle de l'U.E.",
				point2: '',
				point3: '',
			},
			bothQualified: {
				point1:
					'Vous remplissez toutes les conditions requises pour une signature électronique juridiquement valable dans la zone CH et U.E.',
				point2: '',
				point3: '',
			},
			buttonText: 'en savoir plus',
			modal: {
				point2: {
					title: 'Pourquoi être enregistré pour la signature électronique ?',
					paragraph1:
						"Les processus sont de plus en plus « numériques » et vos fournisseurs de services en ligne offrent de plus en plus la signatures électroniques pour compléter les processus. Si vous êtes enregistré, vous pouvez à l'avenir souscrire une assurance, réaliser des achats, des opérations bancaires ou de manière générale signer des contrats avec la plupart des fournisseurs de services en ligne qui nécessitent une signature manuscrite. La signature électronique qualifiée de Swisscom est en effet équivalente à la signature manuscrite.",
					paragraph2:
						"Vous économisez ainsi un temps précieux et de l'argent : pas d'impression, pas de numérisation.",
					paragraph3: 'Vous trouverez de plus amples informations',
					link: 'https://srsident.trustservices.swisscom.com/en/',
					linkText: 'ici',
				},
			},
		},
		testSignature: {
			title: 'Tester Mobile\u00A0ID',
			simActive: {
				point1: 'Testez votre méthode SIM.',
				point2: '',
				point3: '',
			},
			appActive: {
				point1: 'Testez votre méthode APP.',
				point2: '',
				point3: '',
			},
			bothActive: {
				point1: 'Testez votre méthode SIM ou APP.',
				point2: '',
				point3: '',
			},
			buttonText: 'Test',
		},
		resetPin: {
			title: 'Réinitialiser PIN Mobile\u00A0ID',
			point1: 'Définir un nouveau code PIN pour votre méthode SIM',
			point2: '',
			point3: '',
			buttonText: 'Réinitialiser',
		},

		download: {
			title: 'Utilisez l’application Mobile\u00A0ID',
			dowloadLinkGooglePlay:
				'https://play.google.com/store/apps/details?id=com.swisscom.mobileid&hl=fr',
			downloadLinkAppStore:
				'https://itunes.apple.com/ch/app/mobile-id/id1500393675?mt=8&l=fr',
			point1:
				"Téléchargez l'application Mobile\u00A0ID maintenant gratuitement.",
			point2: '',
			point3: '',
		},
		activeMethods: 'Méthodes actives',
		activeMethod: 'Méthode active',
		orderNewSimCard: {
			title: 'Utilisez la carte SIM Mobile\u00A0ID',
			point1:
				'La carte SIM Mobile\u00A0ID vous offre une sécurité et une compatibilité maximales.',
			point2:
				'Commandez une carte SIM compatible Mobile\u00A0ID auprès de votre opérateur de téléphonie mobile.',
			point3: '',
			links: {
				mbudget: 'https://selfcare.m-budget.migros.ch/eCare/fr/users/sign_in',
				salt: 'https://mobileid.salt.ch/fr/',
				coopMobile: 'https://myaccount.coopmobile.ch/eCare/fr/users/sign_in',
				sunrise:
					'https://www.sunrise.ch/de/residential/mysunrise/meine-produkte/deep-link-product-selector-page.html#/null/SimCard',
				swisscom:
					'https://www.swisscom.ch/fr/clients-prives/aide/mobile/bloquer-remplacer-sim.html',
				wingo: 'https://mywingo.wingo.ch/eCare/fr/users/sign_in',
				upc: 'https://www.upc.ch/de/support/mobile/mobile-id/',
			},
		},
		activation: {
			title: 'Activer Mobile\u00A0ID',
			both: {
				point1:
					'Activez une authentification à deux facteurs sécurisés avec votre Mobile\u00A0ID SIM ou votre application Mobile\u00A0ID.',
				point2: '',
				point3: '',
			},
			app: {
				point1:
					'Activez une authentification à deux facteurs sécurisés avec votre application Mobile\u00A0ID.',
				point2: '',
				point3: '',
			},
			buttonText: 'Activer',
		},
		sim: 'SIM',
		generateRecoveryCode: {
			title: 'Code de restauration',
			hasRecoveryCode: {
				point1: 'Vous avez déjà un code de récupération.',
				point2: 'pouvez générer un nouveau code de récupération à tout moment.',
				point3: "Un code de récupération peut être utilisé qu'une seule fois.",
				tooltip:
					'Un code de restauration existe déjà pour ce compte. Vous pouvez en créer une nouvelle si nécessaire.',
			},
			hasNotRecoveryCode: {
				point1:
					"Pour une éventuelle récupération de votre méthode d'identification mobile, vous devez maintenant générer un code de récupération.",
				point2: '',
				point3: '',
				tooltip:
					"Aucun code de restauration n'a encore été généré pour ce compte.",
			},
			buttonText: 'Générer',
		},
		phoneNumber: 'Numéro de téléphone',
		title: 'Bienvenue sur votre tableau de bord Mobile\u00A0ID',
		simStatus: {
			title: 'SIM Status',
			active: {
				text: 'Activé',
				tooltip:
					'Le Mobile\u00A0ID a été activé avec succès sur votre carte SIM.',
			},
			compatible: {
				text: 'Compatible. Non active.',
				tooltip:
					'Activez maintenant gratuitement le Mobile\u00A0ID sur votre carte SIM.',
			},
			notCompatible: {
				text: 'Non compatible',
				tooltip:
					'Commandez une carte SIM compatible Mobile\u00A0ID auprès de votre opérateur de téléphonie mobile.',
			},
		},
		appStatus: {
			title: 'APP Status',
			active: {
				text: 'Activé',
				tooltip:
					"Votre application Mobile\u00A0ID a été activée avec succès. Si vous avez dû réinstaller l'application, vous pouvez l'activer à nouveau à tout moment.",
			},
			notActive: {
				text: 'Non activé',
				tooltip:
					"Téléchargez l'application Mobile\u00A0ID gratuitement dès maintenant et activez l'application sur votre tableau de bord Mobile\u00A0ID.",
			},
		},
		seo: {
			title: 'Votre Dashboard Mobile\u00A0ID personnel',
			description:
				'Le Dashboard Mobile\u00A0ID est le centre de contrôle pour la gestion de votre Mobile\u00A0ID. Ici, vous pouvez activer, enregistrer ou tester votre Mobile\u00A0ID.',
		},
		loading: 'Votre tableau de bord est en train de charger...',
		mobile_integration_loading: 'Veuillez patienter',
		deleteText: { app: 'Désactiver', sim: 'Désactiver' },
		test: {
			notAvailable: 'Test de certificat non disponible pour votre compte',
		},
	},
	it: {
		app: 'App',
		eSignature: {
			title: 'Firma legalmente valida con Mobile\u00A0ID',
			notQualified: {
				point1:
					'Identificarsi per soddisfare tutti i requisiti per una firma elettronica legalmente valida.',
				point2: '',
				point3: '',
			},
			chQualified: {
				point1:
					"Lei soddisfa tutti i requisiti per le firme elettroniche legalmente valide nell'area CH.",
				point2: '',
				point3: '',
			},
			euQualified: {
				point1:
					'Lei soddisfa tutti i requisiti per le firme elettroniche legalmente valide nell’area UE.',
				point2: '',
				point3: '',
			},
			bothQualified: {
				point1:
					"Lei soddisfa tutti i requisiti per le firme elettroniche legalmente valide nell'area CH e UE.",
				point2: '',
				point3: '',
			},
			buttonText: 'maggiori informazioni',
			modal: {
				point2: {
					title: 'Perché ha senso essere registrati per la firma elettronica?',
					paragraph1:
						'I processi stanno diventando sempre più "digitali" e i vostri fornitori di servizi online offrono sempre più firme elettroniche per completare i processi. Se sei registrato, in futuro è possibile stipulare assicurazioni, acquisti, contratti bancari o generali con la maggior parte dei fornitori di servizi online che richiedono una firma scritta a mano o online. La firma elettronica qualificata di Swisscom equivale alla firma scritta.',
					paragraph2:
						'Risparmia tempo e denaro: nessuna stampa, nessuna scansione',
					paragraph3: 'Potete trovare ulteriori informazioni',
					link: 'https://srsident.trustservices.swisscom.com/en/',
					linkText: 'qui',
				},
			},
		},
		testSignature: {
			title: 'Testare Mobile\u00A0ID',
			simActive: {
				point1: 'Testate Mobile\u00A0ID con la SIM.',
				point2: '',
				point3: '',
			},
			appActive: {
				point1: 'Testate Mobile\u00A0ID con l’APP.',
				point2: '',
				point3: '',
			},
			bothActive: {
				point1: 'Testate Mobile\u00A0ID con la SIM o l’APP.',
				point2: '',
				point3: '',
			},
			buttonText: 'Testare',
		},
		resetPin: {
			title: 'Ripristinare il PIN Mobile\u00A0ID',
			point1:
				'Definite un nuovo PIN di identificazione sul cellulare per Mobile\u00A0ID con SIM',
			point2: '',
			point3: '',
			buttonText: 'Ripristinare',
		},

		download: {
			title: 'Utilizzare l’app Mobile\u00A0ID',
			dowloadLinkGooglePlay:
				'https://play.google.com/store/apps/details?id=com.swisscom.mobileid&hl=it',
			downloadLinkAppStore:
				'https://itunes.apple.com/ch/app/mobile-id/id1500393675?mt=8&l=it',
			point1: 'Scaricate ora gratuitamente l’APP Mobile\u00A0ID.',
			point2: '',
			point3: '',
		},
		activeMethods: 'Metodi attivi',
		activeMethod: 'Metodo attivo',
		orderNewSimCard: {
			title: 'Utilizzare la carta SIM Mobile\u00A0ID',
			point1:
				'La SIM Mobile\u00A0ID vi offre la massima sicurezza e compatibilità.',
			point2:
				'Ordinate una SIM Mobile\u00A0ID compatibile dal vostro operatore di telefonia mobile.',
			point3: '',
			links: {
				mbudget: 'https://selfcare.m-budget.migros.ch/eCare/it/users/sign_in',
				salt: 'https://mobileid.salt.ch/it/',
				coopMobile: 'https://myaccount.coopmobile.ch/eCare/it/users/sign_in',
				sunrise:
					'https://www.sunrise.ch/de/residential/mysunrise/meine-produkte/deep-link-product-selector-page.html#/null/SimCard',
				swisscom:
					'https://www.swisscom.ch/it/clienti-privati/aiuto/mobile/bloccare-sostituire-sim.html',
				wingo: 'https://mywingo.wingo.ch/eCare/it/users/sign_in',
				upc: 'https://www.upc.ch/de/support/mobile/mobile-id/',
			},
		},
		activation: {
			title: 'Attivare Mobile\u00A0ID',
			both: {
				point1:
					"Attivate l'autenticazione sicura a due fattori con la carta SIM o l'APP Mobile\u00A0ID.",
				point2: '',
				point3: '',
			},
			app: {
				point1:
					"Attivate l'autenticazione sicura a due fattori con l’APP Mobile\u00A0ID.",
				point2: '',
				point3: '',
			},
			buttonText: 'Attivare',
		},
		sim: 'SIM',
		generateRecoveryCode: {
			title: 'Codice di ripristino',
			hasRecoveryCode: {
				point1: 'Avete già un codice di sblocco.',
				point2: 'È possibile generare un nuovo codice di in qualsiasi momento.',
				point3: 'Un codice di sblocco può essere utilizzato una sola volta.',
				tooltip:
					'Esiste già un codice di ripristino per questo account. Potete crearne uno nuovo, se necessario.',
			},
			hasNotRecoveryCode: {
				point1:
					'Per una possibile riattivazione del vostro Mobile\u00A0ID dovreste ora generare un codice di sblocco.',
				point2: '',
				point3: '',
				tooltip:
					'Un codice di ripristino non è ancora stato generato per questo conto.',
			},
			buttonText: 'Generare',
		},
		phoneNumber: 'Numero di telefono',
		title: 'Benvenuto/a sulla Sua dashboard Mobile\u00A0ID',
		simStatus: {
			title: 'SIM Status',
			active: {
				text: 'Attivato',
				tooltip:
					'Mobile\u00A0ID è stato attivato con successo sulla vostra carta SIM.',
			},
			compatible: {
				text: 'Compatibile. Non attivato.',
				tooltip:
					'Attivate ora gratuitamente Mobile\u00A0ID sulla vostra carta SIM.',
			},
			notCompatible: {
				text: 'Non è compatibile',
				tooltip:
					'Ordinate una SIM compatibile Mobile\u00A0ID dal vostro operatore di telefonia mobile.',
			},
		},
		appStatus: {
			title: 'APP Status',
			active: {
				text: 'Attivato',
				tooltip:
					"L'app Mobile\u00A0ID è stata attivata con successo. Se avete dovuto reinstallare l'app, potete attivarla di nuovo in qualsiasi momento.",
			},
			notActive: {
				text: 'Non attivato',
				tooltip:
					"Scaricate subito gratuitamente l'app Mobile\u00A0ID e attivatela sulla Dashboard Mobile\u00A0ID.",
			},
		},
		seo: {
			title: 'Il tuo personale Dashboard Mobile\u00A0ID',
			description:
				'Il Mobile\u00A0ID Dashboard è il centro di controllo per la gestione del vostro Mobile\u00A0ID. Qui potete attivare, salvare o testare il vostro Mobile\u00A0ID.',
		},
		loading: 'Il tuo cruscotto sta caricando...',
		mobile_integration_loading: 'Attendere prego',
		deleteText: { app: 'Disattivare', sim: 'Disattivare' },
		test: {
			notAvailable: 'Test del certificato non disponibile per il tuo account',
		},
	},
};

export { phrases, namespace };
