/* @flow */
import React from 'react';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { withRouter } from 'react-router-dom';
import { localize, LocalizeProps } from '../../../../modules/i18n';

import combineHOCs from '../../../../tango/hoc/combineHOCs';
import { DashboardProcess } from '../../modules/DashboardProcess';
import Header from '../../modules/Header';
import {
	DesiredProcessName,
	METHOD_APP,
	METHOD_SIM,
	ProcessName,
	ProcessState,
} from '../../modules/mobileid-processes/constants';
import {
	ORIGIN_LOGGED_OUT,
	PROFILE_REQUEST_ID,
} from '../../modules/mobileid-session/constants';
import Page from '../../modules/Page';
import { WithCssTransition } from '../../modules/WithCssTransition';
import { namespace } from './i18n';
import styles from './MyMobileId.css';
import { getQueryParam, getSearch } from '../Login/utils';
import type { RouteProps } from '../../../../tango/routing/types';
import { PhoneNumberTile } from './tiles/PhoneNumberTile';
import { SimStatusTile } from './tiles/SimStatusTile';
import { AppStatusTile } from './tiles/AppStatusTile';
import { SimProvidertile } from './tiles/SimProviderTile';
import { SimActivationTile } from './tiles/SimActivationTile';
import { ResetMobilePinTile } from './tiles/ResetMobilePinTile';
import { GenerateRecoveryCodeTile } from './tiles/GenerateRecoveryCodeTile';
import { TestSignatureTile } from './tiles/TestSignatureTile';
import { AppOnboardingTile } from './tiles/AppOnboardingTile';
import { ESignatureTile } from './tiles/ESignatureTile';
import { DESIRED_WORKFLOW } from './constants';
import { getActivationStatus, getIsMobileAppIntegration } from './utils';
import { LoadingDashboardTile } from './tiles/LoadingDashboardTile';
import { HeaderTile } from './tiles/HeaderTile';
import { EsignatureModal } from './modal/EsignatureModal';

type Props = {
	requestState: Object,
	onRequestOTP: Function,
};

class MyMobileIdComponent extends React.Component {
	props: Props & LocalizeProps & RouteProps;
	state = {};
	componentDidMount = () => {
		const {
			onStartProcess,
			loggedOut,
			isLoggedIn,
			processState,
			locale,
			history,
			profile,
		} = this.props;

		const { isSimActive, isAppActive } = getActivationStatus(profile);
		const simOrAppActive = isSimActive || isAppActive;

		window.scrollTo(0, 0);

		// the redirects with the query params are set in the tenant config
		const desiredWorkFlow = getQueryParam('desired-workflow');

		if (
			[
				DESIRED_WORKFLOW.APP_ACTIVATION,
				DESIRED_WORKFLOW.APP_ENROLL_MOBILE_ONLY,
			].includes(desiredWorkFlow)
		) {
			onStartProcess(ProcessName.Preparation, {
				desiredProcess: DesiredProcessName.Activation,
				method: METHOD_APP,
			});
		} else if (
			desiredWorkFlow === DESIRED_WORKFLOW.RECOVERY_CODE_GENERATION &&
			simOrAppActive
		) {
			onStartProcess(ProcessName.Preparation, {
				desiredProcess: DesiredProcessName.GenerateRecoveryCode,
				method: isAppActive && !isSimActive ? METHOD_APP : METHOD_SIM,
			});
		} else if (desiredWorkFlow === DESIRED_WORKFLOW.ACTIVATION) {
			onStartProcess(ProcessName.Preparation, {
				desiredProcess: DesiredProcessName.Activation,
			});
		} else if (loggedOut) {
			// if logged out, redirect to login page
			history.push(`/${locale}/login?origin=${ORIGIN_LOGGED_OUT}`);
		} else if (!isLoggedIn && processState !== ProcessState.Result) {
			history.push(`/${locale}/login${getSearch()}`);
		}
	};

	componentDidUpdate = () => {
		const { loggedOut, isLoggedIn, processState, history, locale } = this.props;

		if (loggedOut) {
			// if logged out, redirect to login page
			history.push(`/${locale}/login?origin=${ORIGIN_LOGGED_OUT}`);
		} else if (!isLoggedIn && processState !== ProcessState.Result) {
			history.push(`/${locale}/login`);
		}
	};

	render() {
		const {
			t,
			phoneNumber,
			profile,
			onStartProcess,
			processName,
			processState,
			possibleTestMethod,
			country,
			openModal,
			closeModal,
			maintenanceWindow,
			locale,
			requestState,
			isLoggedIn,
			loggedOut,
			refresh,
		} = this.props;

		const {
			isSimCompatible,
			isSimActive,
			isAppActive,
			hasRecoveryCode,
			hasESignature,
		} = getActivationStatus(profile);

		// country information
		const isSwissNumber: boolean = country && country === 'CH';
		const simOrAppActive = isSimActive || isAppActive;

		// request state
		const isFetching =
			requestState && requestState[PROFILE_REQUEST_ID].isFetching;

		if (processName && processState !== ProcessState.Result) {
			return <DashboardProcess />;
		}

		const isMobileAppIntegration = getIsMobileAppIntegration();
		if (isMobileAppIntegration) {
			return null;
		}

		return (
			<WithCssTransition visible animateOnMount>
				<Page
					className={styles.noPrint}
					title={t('seo.title')}
					description={t('seo.description')}
				>
					<Header centered />
					<div className={styles.container}>
						<HeaderTile refresh={refresh} isFetching={isFetching} />
						{maintenanceWindow && (
							<div className={styles.row}>
								<div className={styles.maintenanceContainer}>
									<p>
										{maintenanceWindow.getIn(['translations', locale, 'text'])}
									</p>
								</div>
							</div>
						)}
						{isFetching || !isLoggedIn || loggedOut ? (
							<LoadingDashboardTile />
						) : (
							<React.Fragment>
								<div className={styles.row} >
									<div className={styles.infoCol}>
										<PhoneNumberTile phoneNumber={phoneNumber} />
									</div>
									{isSwissNumber && (
										<div
											className={classNames(
												styles.infoCol,
												styles.withMarginLeft,
												{
													[styles.bigMargin]: isSimActive,
												},
											)}
										>
											<SimStatusTile
												isSimActive={isSimActive}
												isSimCompatible={isSimCompatible}
												onStartProcess={onStartProcess}
											/>
										</div>
									)}
									<div
										className={classNames(
											styles.col,
											styles.infoCol,
											styles.withMarginLeft,
											{
												[styles.bigMargin]: isAppActive,
											},
										)}
									>
										<AppStatusTile
											isAppActive={isAppActive}
											onStartProcess={onStartProcess}
										/>
									</div>
								</div>
								<div className={styles.row}>
									{isSwissNumber && !isSimCompatible && (
										<div className={styles.tileCol}>
											<SimProvidertile />
										</div>
									)}
									<div className={styles.tileCol}>
										<SimActivationTile
											isSwissNumber={isSwissNumber}
											isSimCompatible={isSimCompatible}
											onStartProcess={onStartProcess}
										/>
									</div>
									{isSimActive && (
										<div className={styles.tileCol}>
											<ResetMobilePinTile onStartProcess={onStartProcess} />
										</div>
									)}
									{simOrAppActive && (
										<React.Fragment>
											<div className={styles.tileCol}>
												<GenerateRecoveryCodeTile
													hasRecoveryCode={hasRecoveryCode}
													isAppActive={isAppActive}
													isSimActive={isSimActive}
													onStartProcess={onStartProcess}
												/>
											</div>
											<div className={styles.tileCol}>
												<TestSignatureTile
													possibleTestMethod={possibleTestMethod}
													isSimActive={isSimActive}
													isAppActive={isAppActive}
													onStartProcess={onStartProcess}
												/>
											</div>
										</React.Fragment>
									)}
									{!isAppActive && (
										<div className={styles.tileCol}>
											<AppOnboardingTile />
										</div>
									)}
									{hasESignature && simOrAppActive && (
										<div className={styles.tileCol}>
											<ESignatureTile profile={profile} openModal={openModal} />
										</div>
									)}
								</div>
							</React.Fragment>
						)}
					</div>
					<EsignatureModal closeModal={closeModal} />
				</Page>
			</WithCssTransition>
		);
	}
}

const withHOCs = combineHOCs([
	localize(namespace),
	withStyles(styles),
	withRouter,
]);

const MyMobileId = (withHOCs(MyMobileIdComponent): ReactComponent<EmptyProps>);

export { MyMobileId };
