/* @flow */
import React from 'react';
import { connect } from 'react-redux';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import { MyMobileId } from './MyMobileId';
import type { TypeProfile } from '../../modules/mobileid-session/constants';
import { mapDispatchToProps, mapStateToProps } from './mapToProps';

export type ChildProps = {
	phoneNumber?: string,
	onStartProcess: Function,
	processName?: string,
};

type ConnectedProps = {
	requestOTP: Function,
	requestState: Object,
	processState: string,
	isLoggedIn: boolean,
	loggedOut: boolean,
	profile: TypeProfile,
	possibleTestMethod?: string,
	country?: string,
	openModal: Function,
	closeModal: Function,
	maintenanceWindow: Object,
	locale: string,
	refresh: Function,
};

type Props = ConnectedProps & ChildProps;

function ContainerComponent({
	profile,
	phoneNumber,
	onStartProcess,
	processName,
	processState,
	isLoggedIn,
	loggedOut,
	possibleTestMethod,
	country,
	openModal,
	closeModal,
	maintenanceWindow,
	locale,
	requestState,
	refresh,
}: Props) {
	return (
		<MyMobileId
			phoneNumber={phoneNumber}
			profile={profile}
			onStartProcess={onStartProcess}
			processName={processName}
			processState={processState}
			isLoggedIn={isLoggedIn}
			loggedOut={loggedOut}
			possibleTestMethod={possibleTestMethod}
			country={country}
			openModal={openModal}
			closeModal={closeModal}
			maintenanceWindow={maintenanceWindow}
			locale={locale}
			requestState={requestState}
			refresh={refresh}
		/>
	);
}


const withHOCs = combineHOCs([
	connect(
		mapStateToProps,
		mapDispatchToProps,
	),
]);

const MyMobileIdContainer = (withHOCs(ContainerComponent): ReactComponent<EmptyProps>);

export { MyMobileIdContainer };
