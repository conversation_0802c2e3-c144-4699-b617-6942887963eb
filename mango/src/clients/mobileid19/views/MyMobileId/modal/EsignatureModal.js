import withStyles from 'isomorphic-style-loader/lib/withStyles';
import React from 'react';
import ExternalLink from '../../../../../modules/external-link';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import Modal from '../../../../../modules/modal';
import combineHOCs from '../../../../../tango/hoc/combineHOCs';
import Close from '../../../assets/svg/Close';
import { H3, H5 } from '../../../modules/titles';
import { E_SIGNATURE_MODAL } from '../constants';
import styles from '../MyMobileId.css';
import { namespace } from '../i18n';

type Props = {
	clodeModal: Function,
} & LocalizeProps;

function EsignatureModalComponent({ closeModal, t }: Props) {
	return (
		<Modal
			name={E_SIGNATURE_MODAL}
			className={styles.modal}
			overlayClassName={styles.modalOverlay}
			closeOnOverlayClick={false}
			closeTimeoutInMilliseconds={0}
		>
			<button
				id="STATIC_ID_E_SIGNATURE_MODAL_CLOSE_BUTTON"
				className={styles.closeButton}
				onClick={() => closeModal(E_SIGNATURE_MODAL)}
				type="button"
			>
				<Close className={styles.closeIcon} />
			</button>
			<div className={styles.modalContent}>
				<H3 margin="medium" className={styles.modalTitle}>
					{t('eSignature.title')}
				</H3>
				<H5>{t('eSignature.modal.point2.title')}</H5>
				<p>{t('eSignature.modal.point2.paragraph1')}</p>
				<p>{t('eSignature.modal.point2.paragraph2')}</p>
				<p>
					{t('eSignature.modal.point2.paragraph3')}{' '}
					<ExternalLink href={t('eSignature.modal.point2.link')}>
						{t('eSignature.modal.point2.linkText')}
					</ExternalLink>
				</p>
			</div>
		</Modal>
	);
}

const withHOCs = combineHOCs([localize(namespace), withStyles(styles)]);

const EsignatureModal = (withHOCs(
	EsignatureModalComponent,
): ReactComponent<EmptyProps>);

export { EsignatureModal };
