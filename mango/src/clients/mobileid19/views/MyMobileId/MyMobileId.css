@import '../../assets/variables.css';

.h20 {
	height: 20px;
}

* {
	box-sizing: border-box;
}

.noPrint {
	position: relative;
}

.container {
	margin-bottom: 80px;
	margin: auto;
	max-width: var(--max-width, 1440px);
	padding-right: var(--margin-container-mobile, 20px);
	padding-left: var(--margin-container-mobile, 20px);

	@media (--screen-lg) {
		padding-right: var(--margin-container-desktop, 20px);
		padding-left: var(--margin-container-desktop, 20px);
	}

	@media (--screen-xxxl) {
		max-width: 1704px;
	}
}

.row {
	display: grid;
	grid-template-columns: repeat(3, minmax(0, 1fr));
	gap: 20px;

	@media (--screen-mobile) {
		grid-template-columns: repeat(1, minmax(0, 1fr));
	}

	&.row4 {
		grid-template-columns: repeat(4, minmax(0, 1fr));
	}

	&.full {
		grid-template-columns: repeat(1, minmax(0, 1fr));
	}
}

.title {
	margin-top: 64px;
	text-align: center;
	padding: 0 40px !important;
}

.tileCol, .infoCol {
	margin: 10px 0;
	@media (min-width: 1440px) {
		max-width: 440px;
	}
}

.infoCol {
	text-align: center;

	&.withMarginLeft {
		& h4 {
			margin-left: 16px;
		}

		&.bigMargin {
			& h4 {
				margin-left: 54px;
			}
		}
	}
}

.refreshButton {
	background: none;
	border: none;
	box-shadow: none;
	margin-left: 10px;
	display: inline-flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	position: absolute;
	top: 12px;

	&.fetching {
		animation: rotate 2s infinite alternate ease-out forwards;
	}
}

@keyframes rotate {
	from {
		transform: rotate(0deg);
	}

	to {
		transform: rotate(360deg);
	}
}

.tileContainer {
	display: flex;
	flex-direction: column;
	padding: 32px 50px;
	border: 1px solid #EDEDED;
	border-radius: 30px;
	text-align: center;
	height: 100%;
	box-sizing: border-box;
	box-shadow: 0px 8px 12px 0 rgba(0,0,0,0.1);

	& button {
		margin-top: auto;
	}

	@media (--screen-mobile) {
		padding: 24px 12px;
	}
}

.info {
	color: var(--black);
	margin-bottom: 20px;
	display: inline-block;

}

.infoDisabled {
	& .infoSvgText, & .infoSvg {
		opacity: .3;
	}
}

.sim, .app {
	padding: 0 24px;
}

.infoSvgStatusIndicator {
	width: 12px;
	margin-left: 2px;
	opacity: 1;
}

.infoSvg {
	margin-right: 8px;
}

.textCenter {
	text-align: center;
}

.tileUl {
	margin: 0 0 20px 0;
	padding: 0;

	color: var(--black);

	& li {
		list-style: none;
		margin: 0;
		padding: 0;
	}
}

.store {
	display: block;
	width: 50%;

	& img {
		width: 100%;
		height: auto;
		display: flex;
	}
}

.providerLogo {
	padding: 10px;
	height: 72px;
	display: flex;
	justify-content: center;
	align-items: center;

	margin-bottom: 20px;
	border: 1px solid #EDEDED;
	border-radius: 5px;
	text-align: center;

	& img {
		width: 64px;
		max-width: 100%;
	}
}

.cardImg {
	display: block;
	height: 128px;
	width: auto;
	margin: auto;
	margin-bottom: 30px;

	@media (--screen-mobile) {
		height: auto;
		width: 100%;
	}
}

.row {
	margin-bottom: 20px;
}

h2 {
	min-height: 50px;
}

.cloudLeft {
	top: -12px;
	position: absolute;
	right: calc(100% - 16vw);

	@media (--screen-tablet) {
		display: none;
	}
}

.cloudRight {
	top: 12px;
	position: absolute;
	left: calc(100% - 16vw);

	@media (--screen-tablet) {
		display: none;
	}
}

.tooltipButton {
	position: relative !important;
}

.tooltip {
	left: calc(50% + 2px) !important;
	transform: translateX(-50%) translateY(-20%) !important;

	/* top: -12px; */
	@media (--screen-mobile) {
		transform: translateX(-50%) !important;
		top: unset !important;
		left: -55px !important;
		bottom: 32px !important;
		display: block !important;
		width: 70vw !important;
	}

	&:after {
		right: unset !important;
		left: 50% !important;
	}
}


.iconContainer {
	position: relative;
	margin-right: 8px;

	&.inlineIconContainer {
		display: inline-block;
		margin: 0px 8px 0 10px;

		@media (--screen-mobile) {
			margin: 0 6px 0 8px;
		}
	}

	& img {
		display: block;
	}

	& .greenTick {
		position: absolute;
		left: 0;
		bottom: 0;
		transform: translate(-50%, 30%);
	}
}


.modal {
	background: white;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	padding: 40px;
	text-align: left;
	box-shadow: rgba(0, 0, 0, 0.3) 0 3px 46px;
	border-radius: 20px;
	max-height: 90%;
	max-width: 90%;
	display: block;
	height: fit-content;
	height: 50%;
	width: 50%;

	@media (--screen-tablet) {
		width: 70%;
	}

	@media (--screen-mobile) {
		height: 90%;
		width: 90%;
		padding: 20px 12px;
	}

	& .closeButton {
		-webkit-appearance: none !important;
		background: white;
		margin: 0;
		padding: 0;
		position: absolute;
		top: 0;
		right: 0;
		width: 41px;
		height: 41px;
		cursor: pointer;
		transform: translate(50%, -50%);
		border: none;
		border-radius: 41px;
		box-sizing: border-box;
		padding: 8px;

		@media (--screen-mobile) {
			transform: translate(10%, -10%);
		}

		&:hover {
			& .closeIcon {
				fill: #7E7E7E;
			}
		}

		& .closeIcon {
			transition: all 0.3s ease;
			fill: #DEDEDE;
			width: 100%;
			height: 100%;
		}
	}

	&:after {
		content: "";
		display: block;
		clear: both;
	}

	& .modalContent {
		overflow-y: scroll;
		display: block;
		height: 100%;
		padding-right: 48px;

		@media (--screen-tablet) {
			padding-right: 24px;
		}

		@media (--screen-mobile) {
			padding-right: 0;
		}

		& .special {
			font-weight: bold;
			color: var(--primary-color);
			display: inline;
			margin: 0 1px;
		}

		& a {
			color: var(--primary-color);
		}

		& .screenshot {
			height: 20px;
			display: inline;
		}
	}
}

.modalOverlay {
	background: #0000005e;
	position: fixed;
	top: 0;
	width: 100%;
	height: 100%;
	z-index: 10000;
}

.maintenanceContainer {
	max-width: 50%;
	height: fit-content;
	border-right: 4px solid orange;
	margin-bottom: 12px;
	margin-top: -40px;
	padding-right: 8px;

	@media (--screen-tablet) {
		max-width: 60%;
		margin-top: -32px;
	}

	@media (--screen-mobile) {
		max-width: 100%;
		margin-top: -24px;
	}

	& p {
		margin-bottom: 12px;
		margin-top: 12px;
	}
}

.moreMenuFlexContainer {
	display: flex;
	justify-content: center;
	align-items: center;

	& h4 {
		display: flex;
	}
}

.flex {
	display: flex;
	gap: 20px;
}
