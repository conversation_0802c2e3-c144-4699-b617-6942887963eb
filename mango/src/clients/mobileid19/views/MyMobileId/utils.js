import { PROFILE_TYPE_ACTIVE } from '../../modules/mobileid-session/constants';
import { getQueryParam } from '../Login/utils';
import { DESIRED_WORKFLOW } from './constants';

export const simStatusTranslationPath = (
	isSimCompatible: booelan,
	isSimActive: boolean,
): string => {
	if (isSimActive) {
		return 'simStatus.active';
	}

	if (isSimCompatible) {
		return 'simStatus.compatible';
	}

	return 'simStatus.notCompatible';
};

export const appStatusTranslationPath = (isAppActive: boolean): string => {
	if (isAppActive) {
		return 'appStatus.active';
	}

	return 'appStatus.notActive';
};

export const testTileTranslationPath = (
	isSimActive: boolean,
	isAppActive: boolean,
): string => {
	if (isSimActive && isAppActive) {
		return 'bothActive';
	}

	if (isAppActive) {
		return 'appActive';
	}

	return 'simActive';
};

export const activationTileTranslationPath = (
	isSwissNumber: boolean,
	isSimCompatible: boolean,
): string => {
	if (isSwissNumber && isSimCompatible) {
		return 'both';
	}

	return 'app';
};

export const eSignatureTileTranslationPath = (
	isEuQualified: boolean,
	isChQualified: boolean,
): string => {
	if (isEuQualified && isChQualified) {
		return 'bothQualified';
	}

	if (isEuQualified) {
		return 'euQualified';
	}

	if (isChQualified) {
		return 'chQualified';
	}

	return 'notQualified';
};

export const recoveryCodeTranslationPath = (
	hasRecoveryCode: boolean,
): string => {
	if (hasRecoveryCode) {
		return 'generateRecoveryCode.hasRecoveryCode';
	}

	if (!hasRecoveryCode) {
		return 'generateRecoveryCode.hasNotRecoveryCode';
	}

	return 'generateRecoveryCode.hasNotRecoveryCode';
};

export function getActivationStatus(profile: Object) {
	// sim status
	const isSimCompatible: boolean = profile && profile.isSimCompatible;
	const isSimActive: boolean =
		profile && profile.simStatus === PROFILE_TYPE_ACTIVE;

	// app status
	const isAppActive: boolean =
		profile && profile.appStatus === PROFILE_TYPE_ACTIVE;

	// recoverycode information
	const hasRecoveryCode: boolean = profile && profile.hasRecoveryCode;

	const hasESignature: boolean =
			profile && profile.eSignature && !profile.eSignature.error;
	return {
		isSimCompatible,
		isSimActive,
		isAppActive,
		hasRecoveryCode,
		hasESignature,
	};
}

const MOBILE_ENROLL_ENABLED = true;

export const getIsMobileAppIntegration = (search: string) => {
	if (!MOBILE_ENROLL_ENABLED) {
		return false;
	}
	if (search) {
		return search && search.includes(`desired-workflow=${DESIRED_WORKFLOW.APP_ENROLL_MOBILE_ONLY}`);
	}
	return getQueryParam('desired-workflow') === DESIRED_WORKFLOW.APP_ENROLL_MOBILE_ONLY;
};
