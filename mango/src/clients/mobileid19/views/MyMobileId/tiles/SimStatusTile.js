/* @flow */
import React from 'react';
import styles from '../MyMobileId.css';
import { H4 } from '../../../modules/titles';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import { namespace } from '../i18n';
import { simStatusTranslationPath } from '../utils';
import { Tooltip } from '../../../modules/Form/Tooltip';
import {
	DesiredProcessName,
	ProcessName,
} from '../../../modules/mobileid-processes/constants';
import { DotMenu } from '../../../modules/DotMenu';
import GreenCheck from '../../../assets/img/icons/green_tick.svg';
import RedCross from '../../../assets/img/icons/red_cross.svg';
import {
	PROFILE_TYPE_ACTIVE,
	PROFILE_TYPE_INACTIVE,
} from '../../../modules/mobileid-session/constants';
import { type StartProcessProps } from '../types';

type Props = {
	isSimCompatible: boolean,
	isSimActive: Boolean,
};

function SimStatusTile({
	isSimCompatible,
	isSimActive,
	onStartProcess,
	t,
}: Props & StartProcessProps & LocalizeProps) {
	const customIcon = isSimActive ? (
		<img
			src={GreenCheck}
			className={styles.infoSvgStatusIndicator}
			alt="greenCheck"
		/>
	) : (
		<img
			src={RedCross}
			className={styles.infoSvgStatusIndicator}
			alt="redCross"
		/>
	);
	const prefix = simStatusTranslationPath(isSimCompatible, isSimActive);
	return (
		<React.Fragment>
			<div className={styles.moreMenuFlexContainer}>
				<H4 margin="small" align="center" size="like-h6">
					{t('simStatus.title')}
					<Tooltip
						className={styles.tooltipButton}
						classNameTooltip={styles.tooltip}
						tooltip={t(`${prefix}.tooltip`)}
						customIcon={customIcon}
					/>
				</H4>
				{isSimActive && (
					<DotMenu
						onDelete={() =>
							onStartProcess(ProcessName.SimDeactivation, {
								desiredProcess: DesiredProcessName.SimDeactivation,
							})
						}
						deleteText={t('deleteText.sim')}
					/>
				)}
			</div>
			<span
				id={`STATIC_ID_DASHBOARD_SIM_STATE_${
					isSimActive ? PROFILE_TYPE_ACTIVE : PROFILE_TYPE_INACTIVE
				}`}
				className={styles.info}
			>
				{t(`${prefix}.text`)}
			</span>
		</React.Fragment>
	);
}

const Container = localize(namespace)(SimStatusTile);

export { Container as SimStatusTile };
