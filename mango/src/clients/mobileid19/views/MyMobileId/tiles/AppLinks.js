/* @flow */
import React from 'react';
import styles from '../MyMobileId.css';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import { namespace } from '../i18n';
import ExternalLink from '../../../../../modules/external-link';
import appStore from '../../../assets/logos/appStore.svg';
import googlePlay from '../../../assets/logos/googlePlay.svg';

function AppLinks({ t }: LocalizeProps) {
	return (
		<div className={styles.flex}>
			<ExternalLink
				id="STATIC_ID_DASHBOARD_BUTTON_DOWNLOAD_APP_GOOGLE_PLAY"
				href={t('download.dowloadLinkGooglePlay')}
				className={styles.store}
			>
				<img src={googlePlay} alt="googlePlay" />
			</ExternalLink>
			<ExternalLink
				id="STATIC_ID_DASHBOARD_BUTTON_DOWNLOAD_APP"
				href={t('download.downloadLinkAppStore')}
				className={styles.store}
			>
				<img src={appStore} alt="appStore" />
			</ExternalLink>
		</div>
	);
}

const Container = localize(namespace)(AppLinks);

export { Container as AppLinks };
