/* @flow */
import React from 'react';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import { namespace } from '../i18n';
import { H1 } from '../../../modules/titles';
import { IllustrationCloudLeft } from '../../../assets/svg/IllustrationCloudLeft';
import { IllustrationCloudRight } from '../../../assets/svg/IllustrationCloudRight';
import styles from '../MyMobileId.css';
import Refresh from '../../../assets/img/icons/refresh.svg';
import combineHOCs from '../../../../../tango/hoc/combineHOCs';
import { getIsMobileAppIntegration } from '../utils';

type Props = {
	refresh: () => void,
	isFetching: Boolean,
};

function HeaderTile({ t, refresh, isFetching }: Props & LocalizeProps) {
	const isMobileAppIntegration = getIsMobileAppIntegration();
	if (isMobileAppIntegration) {
		return <div className={styles.h20} />;
	}
	return (
		<React.Fragment>
			<IllustrationCloudLeft className={styles.cloudLeft} />
			<IllustrationCloudRight className={styles.cloudRight} />
			<div>
				<div className={styles.col}>
					<H1
						size="like-h2"
						align="center"
						margin="huge"
						className={styles.title}
					>
						{isMobileAppIntegration ? (
							<div>
								<br />
								<br />
								<br />
							</div>
						) : (
							<React.Fragment>
								{t('title')}
								<button
									onClick={refresh}
									className={classNames(styles.refreshButton, {
										[styles.fetching]: isFetching,
									})}
								>
									<img src={Refresh} alt="refresh" />
								</button>
							</React.Fragment>
						)}
					</H1>
				</div>
			</div>
		</React.Fragment>
	);
}

const withHOCs = combineHOCs([localize(namespace), withStyles(styles)])(
	HeaderTile,
);

export { withHOCs as HeaderTile };
