/* @flow */
import React from 'react';
import { fromJS } from 'immutable';
import styles from '../MyMobileId.css';
import { H2 } from '../../../modules/titles';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import { namespace } from '../i18n';
import { ResponsiveImage } from '../../../../../modules/files';
import { cardImgSize } from '../constants';
import { testTileTranslationPath } from '../utils';
import Button from '../../../modules/Button';
import {
	DesiredProcessName,
	ProcessName,
} from '../../../modules/mobileid-processes/constants';
import { type StartProcessProps } from '../types';

const imgRobot = fromJS({
	rawFormat: 'cloudinary/V1',
	raw: {
		public_id: 'mobileid/illustrations/roboto_2x',
		version: 1565964112,
	},
	transformations: [],
});

type Props = {
	isSimActive: boolean,
	isAppActive: Boolean,
	possibleTestMethod: String,
};

function TestSignatureTile({
	isSimActive,
	isAppActive,
	possibleTestMethod,
	onStartProcess,
	t,
}: Props & StartProcessProps & LocalizeProps) {
	const prefix = `testSignature.${testTileTranslationPath(
		isSimActive,
		isAppActive,
	)}`;
	return (
		<div id="STATIC_ID_DASHBOARD_TEST_TILE" className={styles.tileContainer}>
			<ResponsiveImage
				className={styles.cardImg}
				image={imgRobot}
				sizes={cardImgSize}
			/>
			<H2 margin="small" align="center" size="like-h6">
				{t('testSignature.title')}
			</H2>
			<ul className={styles.tileUl}>
				<li>{t(`${prefix}.point1`)}</li>
				<li>{t(`${prefix}.point2`)}</li>
				<li>{t(`${prefix}.point3`)}</li>
			</ul>
			<Button
				id="STATIC_ID_DASHBOARD_BUTTON_START_TEST"
				onClick={() =>
					onStartProcess(ProcessName.Preparation, {
						desiredProcess: DesiredProcessName.TestSignature,
						method: possibleTestMethod,
					})
				}
			>
				{t('testSignature.buttonText')}
			</Button>
		</div>
	);
}

const Container = localize(namespace)(TestSignatureTile);

export { Container as TestSignatureTile };
