/* @flow */
import classNames from 'classnames';
import React from 'react';
import styles from '../MyMobileId.css';
import { H2 } from '../../../modules/titles';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import { namespace } from '../i18n';
import { allProviders } from '../../../modules/providers/data';

function SimProvidertile({ locale, t }: LocalizeProps) {
	return (
		<div
			id="STATIC_ID_DASHBOARD_SIM_INCOMPATIBLE_TILE"
			className={styles.tileContainer}
		>
			<H2 margin="small" align="center" size="like-h6">
				{t('orderNewSimCard.title')}
			</H2>
			<ul className={styles.tileUl}>
				<li>{t('orderNewSimCard.point1')}</li>
				<li>{t('orderNewSimCard.point2')}</li>
				<li>{t('orderNewSimCard.point3')}</li>
			</ul>
			<div className={classNames(styles.row, styles.row4)}>
				{allProviders.map(({ image, alt, title, orderSimUrls }, index) => (
					<div className={styles.col} key={`primary-${index}`}>
						<div
							href={orderSimUrls && orderSimUrls[locale]}
							className={styles.providerLogo}
						>
							<img src={image} alt={alt} />
						</div>
					</div>
				))}
			</div>
		</div>
	);
}

const Container = localize(namespace)(SimProvidertile);

export { Container as SimProvidertile };
