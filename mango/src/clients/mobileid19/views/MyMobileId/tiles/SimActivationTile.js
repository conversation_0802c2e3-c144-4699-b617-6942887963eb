/* @flow */
import React from 'react';
import { fromJS } from 'immutable';
import styles from '../MyMobileId.css';
import { H2 } from '../../../modules/titles';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import { namespace } from '../i18n';
import { ResponsiveImage } from '../../../../../modules/files';
import { activationTileTranslationPath } from '../utils';
import Button from '../../../modules/Button';
import {
	DesiredProcessName,
	ProcessName,
} from '../../../modules/mobileid-processes/constants';
import { cardImgSize } from '../constants';
import { type StartProcessProps } from '../types';

const imgActivate = fromJS({
	rawFormat: 'cloudinary/V1',
	raw: {
		public_id: 'mobileid/illustrations/activate_2x',
		version: 1565964112,
	},
	transformations: [],
});

type Props = {
	isSwissNumber: boolean,
	isSimCompatible: Boolean,
};

function SimActivationTile({
	isSwissNumber,
	isSimCompatible,
	onStartProcess,
	t,
}: Props & StartProcessProps & LocalizeProps) {
	const prefix = `activation.${activationTileTranslationPath(
		isSwissNumber,
		isSimCompatible,
	)}`;
	return (
		<div
			id="STATIC_ID_DASHBOARD_ACTIVATION_TILE"
			className={styles.tileContainer}
		>
			<ResponsiveImage
				className={styles.cardImg}
				image={imgActivate}
				sizes={cardImgSize}
			/>
			<H2 margin="small" align="center" size="like-h6">
				{t('activation.title')}
			</H2>
			<ul className={styles.tileUl}>
				<li>{t(`${prefix}.point1`)}</li>
				<li>{t(`${prefix}.point2`)}</li>
				<li>{t(`${prefix}.point3`)}</li>
			</ul>
			<Button
				id="STATIC_ID_DASHBOARD_BUTTON_START_ACTIVATION"
				onClick={() =>
					onStartProcess(ProcessName.Preparation, {
						desiredProcess: DesiredProcessName.Activation,
					})
				}
			>
				{t('activation.buttonText')}
			</Button>
		</div>
	);
}

const Container = localize(namespace)(SimActivationTile);

export { Container as SimActivationTile };
