/* @flow */
import React from 'react';
import { fromJS } from 'immutable';
import styles from '../MyMobileId.css';
import { H2 } from '../../../modules/titles';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import { namespace } from '../i18n';
import { ResponsiveImage } from '../../../../../modules/files';
import { cardImgSize } from '../constants';
import { AppLinks } from './AppLinks';

const imgDownload = fromJS({
	rawFormat: 'cloudinary/V1',
	raw: {
		public_id: 'mobileid/illustrations/download_2x',
		version: 1565964112,
	},
	transformations: [],
});

function AppOnboardingTile({ t }: LocalizeProps) {
	return (
		<div
			id="STATIC_ID_DASHBOARD_DOWNLOAD_APP_TILE"
			className={styles.tileContainer}
		>
			<ResponsiveImage
				className={styles.cardImg}
				image={imgDownload}
				sizes={cardImgSize}
			/>
			<H2 margin="small" align="center" size="like-h6">
				{t('download.title')}
			</H2>
			<ul className={styles.tileUl}>
				<li>{t('download.point1')}</li>
				<li>{t('download.point2')}</li>
				<li>{t('download.point3')}</li>
			</ul>
			<AppLinks />
		</div>
	);
}

const Container = localize(namespace)(AppOnboardingTile);

export { Container as AppOnboardingTile };
