/* @flow */
import React from 'react';
import styles from '../MyMobileId.css';
import { H4 } from '../../../modules/titles';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import { namespace } from '../i18n';
import { Tooltip } from '../../../modules/Form/Tooltip';
import { appStatusTranslationPath } from '../utils';
import { DotMenu } from '../../../modules/DotMenu';
import {
	PROFILE_TYPE_ACTIVE,
	PROFILE_TYPE_INACTIVE,
} from '../../../modules/mobileid-session/constants';
import GreenCheck from '../../../assets/img/icons/green_tick.svg';
import RedCross from '../../../assets/img/icons/red_cross.svg';
import {
	DesiredProcessName,
	ProcessName,
} from '../../../modules/mobileid-processes/constants';
import { type StartProcessProps } from '../types';

type Props = {
	isAppActive: boolean,
};

function AppStatusTile({
	isAppActive,
	onStartProcess,
	t,
}: Props & StartProcessProps & LocalizeProps) {
	const customIcon = isAppActive ? (
		<img
			src={GreenCheck}
			className={styles.infoSvgStatusIndicator}
			alt="greenCheck"
		/>
	) : (
		<img
			src={RedCross}
			className={styles.infoSvgStatusIndicator}
			alt="redCross"
		/>
	);
	return (
		<React.Fragment>
			<div className={styles.moreMenuFlexContainer}>
				<H4 margin="small" align="center" size="like-h6">
					{t('appStatus.title')}
					<Tooltip
						className={styles.tooltipButton}
						classNameTooltip={styles.tooltip}
						tooltip={t(`${appStatusTranslationPath(isAppActive)}.tooltip`)}
						customIcon={customIcon}
					/>
				</H4>
				{isAppActive && (
					<DotMenu
						onDelete={() =>
							onStartProcess(ProcessName.AppDeactivation, {
								desiredProcess: DesiredProcessName.AppDeactivation,
							})
						}
						deleteText={t('deleteText.app')}
					/>
				)}
			</div>
			<span
				id={`STATIC_ID_DASHBOARD_APP_STATE_${
					isAppActive ? PROFILE_TYPE_ACTIVE : PROFILE_TYPE_INACTIVE
				}`}
				className={styles.info}
			>
				{t(`${appStatusTranslationPath(isAppActive)}.text`)}
			</span>
		</React.Fragment>
	);
}

const Container = localize(namespace)(AppStatusTile);

export { Container as AppStatusTile };
