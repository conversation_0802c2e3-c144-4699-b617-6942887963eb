/* @flow */
import React from 'react';
import { fromJS } from 'immutable';
import styles from '../MyMobileId.css';
import { H2 } from '../../../modules/titles';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import { namespace } from '../i18n';
import { ResponsiveImage } from '../../../../../modules/files';
import { cardImgSize } from '../constants';
import Button from '../../../modules/Button';
import { DesiredProcessName, METHOD_SIM, ProcessName } from '../../../modules/mobileid-processes/constants';
import { type StartProcessProps } from '../types';

const imgReset = fromJS({
	rawFormat: 'cloudinary/V1',
	raw: {
		public_id: 'mobileid/illustrations/resett_2x',
		version: 1565964112,
	},
	transformations: [],
});

type Props = {
	phoneNumber: String,
};

function ResetMobilePinTile({ onStartProcess, t }: Props & StartProcessProps & LocalizeProps) {
	return (
		<div id="STATIC_ID_DASHBOARD_RESET_TILE" className={styles.tileContainer}>
			<ResponsiveImage
				className={styles.cardImg}
				image={imgReset}
				sizes={cardImgSize}
			/>
			<H2 margin="small" align="center" size="like-h6">
				{t('resetPin.title')}
			</H2>
			<ul className={styles.tileUl}>
				<li>{t('resetPin.point1')}</li>
				<li>{t('resetPin.point2')}</li>
				<li>{t('resetPin.point3')}</li>
			</ul>
			<Button
				id="STATIC_ID_DASHBOARD_BUTTON_START_RESET_PIN"
				THEME_SECONDARY
				onClick={() =>
					onStartProcess(ProcessName.Preparation, {
						desiredProcess: DesiredProcessName.ResetPin,
						method: METHOD_SIM,
					})
				}
			>
				{t('resetPin.buttonText')}
			</Button>
		</div>
	);
}

const Container = localize(namespace)(ResetMobilePinTile);

export { Container as ResetMobilePinTile };
