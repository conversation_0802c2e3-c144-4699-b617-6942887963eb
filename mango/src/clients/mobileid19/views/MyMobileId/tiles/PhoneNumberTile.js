/* @flow */
import React from 'react';
import styles from '../MyMobileId.css';
import { H4 } from '../../../modules/titles';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import { namespace } from '../i18n';

type Props = {
	phoneNumber: String,
};

function PhoneNumberTile({ phoneNumber, t }: Props & LocalizeProps) {
	return (
		<React.Fragment>
			<H4 margin="small" align="center" size="like-h6">
				{t('phoneNumber')}
			</H4>
			<span className={styles.info}>{`+${phoneNumber}`}</span>
		</React.Fragment>
	);
}

const Container = localize(namespace)(PhoneNumberTile);

export { Container as PhoneNumberTile };
