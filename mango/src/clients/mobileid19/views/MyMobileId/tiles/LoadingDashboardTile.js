/* @flow */
import React from 'react';
import classNames from 'classnames';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import Loader from '../../../modules/Loader';
import { namespace } from '../i18n';
import { getIsMobileAppIntegration } from '../utils';
import styles from './LoadingDashboardTile.css';
import { H4 } from '../../../modules/titles';

function LoadingDashboardTile({ t }: LocalizeProps) {
	const isMobileAppIntegration = getIsMobileAppIntegration();
	const key = isMobileAppIntegration ? 'mobile_integration_loading' : 'loading';

	const mobileAppIntegrationStyles = {
		[styles.mobileAppIntegrationContainer]: isMobileAppIntegration,
	};

	return (
		<div
			className={classNames(mobileAppIntegrationStyles)}
			data-id="just-something"
		>
			<H4 margin="huge" align="center">{t(key)}</H4>
			<Loader />
		</div>
	);
}

const Container = localize(namespace)(LoadingDashboardTile);

export { Container as LoadingDashboardTile };
