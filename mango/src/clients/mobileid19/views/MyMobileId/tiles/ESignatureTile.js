/* @flow */
import React from 'react';
import { fromJS } from 'immutable';
import classNames from 'classnames';
import styles from '../MyMobileId.css';
import { H2 } from '../../../modules/titles';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import { namespace } from '../i18n';
import { ResponsiveImage } from '../../../../../modules/files';
import { cardImgSize, E_SIGNATURE_MODAL } from '../constants';
import { eSignatureTileTranslationPath } from '../utils';
import Button, { THEME_PRIMARY } from '../../../modules/Button';
import { TYPE_BUTTON } from '../../../modules/Button/Button';

const imgSignature = fromJS({
	rawFormat: 'cloudinary/V1',
	raw: {
		public_id: 'mobileid/illustrations/eSignature.png',
		version: 1572620125,
	},
	transformations: [],
});

type Props = {
	profile: Object,
	openModal: (name: string) => void,
};

function ESignatureTile({ profile, openModal, t }: Props & LocalizeProps) {
	const isEuQualified: boolean =
		profile && profile.eSignature && profile.eSignature.euQualified;
	const isChQualified: boolean =
		profile && profile.eSignature && profile.eSignature.chQualified;
	const prefix = `eSignature.${eSignatureTileTranslationPath(
		isEuQualified,
		isChQualified,
	)}`;
	return (
		<div
			id="STATIC_ID_DASHBOARD_ESIGNATURE_TILE"
			className={styles.tileContainer}
		>
			<ResponsiveImage
				className={styles.cardImg}
				image={imgSignature}
				sizes={cardImgSize}
			/>
			<H2 margin="small" align="center" size="like-h6">
				{t('eSignature.title')}
			</H2>
			<ul className={classNames(styles.tileUl)}>
				<li>{t(`${prefix}.point1`)}</li>
				<li>{t(`${prefix}.point2`)}</li>
				<li>{t(`${prefix}.point3`)}</li>
			</ul>
			<Button
				id="STATIC_ID_DASHBOARD_BUTTON_E_SIGNATURE_MAIN"
				onClick={() => openModal(E_SIGNATURE_MODAL)}
				theme={THEME_PRIMARY}
				type={TYPE_BUTTON}
			>
				{t('eSignature.buttonText')}
			</Button>
		</div>
	);
}

const Container = localize(namespace)(ESignatureTile);

export { Container as ESignatureTile };
