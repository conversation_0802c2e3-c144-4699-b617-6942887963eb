/* @flow */
import React from 'react';
import { fromJS } from 'immutable';
import styles from '../MyMobileId.css';
import { H2 } from '../../../modules/titles';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import { namespace } from '../i18n';
import { Tooltip } from '../../../modules/Form/Tooltip';
import { recoveryCodeTranslationPath } from '../utils';
import GreenCheck from '../../../assets/img/icons/green_tick.svg';
import RedCross from '../../../assets/img/icons/red_cross.svg';
import {
	DesiredProcessName,
	METHOD_APP,
	METHOD_SIM,
	ProcessName,
} from '../../../modules/mobileid-processes/constants';
import { type StartProcessProps } from '../types';
import { ResponsiveImage } from '../../../../../modules/files';
import { cardImgSize } from '../constants';
import Button from '../../../modules/Button';

const imgGenerate = fromJS({
	rawFormat: 'cloudinary/V1',
	raw: {
		public_id: 'mobileid/illustrations/generate_2x',
		version: 1565964112,
	},
	transformations: [],
});

type Props = {
	hasRecoveryCode: boolean,
	isAppActive: boolean,
	isSimActive: boolean,
};

function GenerateRecoveryCodeTile({
	hasRecoveryCode,
	isAppActive,
	isSimActive,
	onStartProcess,
	t,
}: Props & StartProcessProps & LocalizeProps) {
	const prefix = `generateRecoveryCode.${
		hasRecoveryCode ? 'hasRecoveryCode' : 'hasNotRecoveryCode'
	}`;
	return (
		<div
			id="STATIC_ID_DASHBOARD_RECOVERY_TILE"
			className={styles.tileContainer}
		>
			<ResponsiveImage
				className={styles.cardImg}
				image={imgGenerate}
				sizes={cardImgSize}
			/>
			<H2 margin="small" align="center" size="like-h6">
				{t('generateRecoveryCode.title')}
				<Tooltip
					className={styles.tooltipButton}
					classNameTooltip={styles.tooltip}
					tooltip={t(`${recoveryCodeTranslationPath(hasRecoveryCode)}.tooltip`)}
					customIcon={
						hasRecoveryCode ? (
							<img
								src={GreenCheck}
								className={styles.infoSvgStatusIndicator}
								alt="greenCheck"
							/>
						) : (
							<img
								src={RedCross}
								className={styles.infoSvgStatusIndicator}
								alt="redCross"
							/>
						)
					}
				/>
			</H2>
			<ul className={styles.tileUl}>
				<li>{t(`${prefix}.point1`)}</li>
				<li>{t(`${prefix}.point2`)}</li>
				<li>{t(`${prefix}.point3`)}</li>
			</ul>
			<Button
				id="STATIC_ID_DASHBOARD_BUTTON_START_RECOVERY_CODE"
				onClick={() =>
					onStartProcess(ProcessName.Preparation, {
						desiredProcess: DesiredProcessName.GenerateRecoveryCode,
						method: isAppActive && !isSimActive ? METHOD_APP : METHOD_SIM,
					})
				}
			>
				{t('generateRecoveryCode.buttonText')}
			</Button>
		</div>
	);
}

const Container = localize(namespace)(GenerateRecoveryCodeTile);

export { Container as GenerateRecoveryCodeTile };
