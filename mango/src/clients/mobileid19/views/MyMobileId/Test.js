/* @flow */
import React from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import { DashboardProcess } from '../../modules/DashboardProcess';
import { processStarted } from '../../modules/mobileid-processes/actions';
import {
	DesiredProcessName,
	ProcessName,
	ProcessState,
} from '../../modules/mobileid-processes/constants';
import {
	getPossibleTestMethod,
	getProcessName,
	getProcessState,
} from '../../modules/mobileid-processes/selectors';
import {
	getProfile,
	getSessionJwt,
} from '../../modules/mobileid-session/selectors';
import { type StartProcessProps } from './types';
import type { RouteProps } from '../../../../tango/routing/types';
import { localize } from '../../../../modules/i18n';
import { getActivationStatus } from './utils';
import Page from '../../modules/Page';
import styles from './Test.css';
import Header from '../../modules/Header';
import { namespace } from './i18n';
import { getRequestIsFetching } from '../../../../modules/request/selectors';
import { PROFILE_REQUEST_ID } from '../../modules/mobileid-session/constants';
import { H3 } from '../../modules/titles';

type InternalProps = StartProcessProps &
	RouteProps & {
		processName: String,
		processState: String,
		isLoggedIn: Boolean,
		possibleTestMethod: String,
		profile: Object,
		isFetching: Boolean,
	};

function onUpdate(
	{
		isFetching,
		isLoggedIn,
		onStartProcess,
		locale,
		possibleTestMethod,
		history,
	}: InternalProps,
	started: boolean,
	startProcess: () => void,
) {
	if (isFetching === false && !started) {
		startProcess();
		if (isLoggedIn) {
			onStartProcess(ProcessName.Preparation, {
				desiredProcess: DesiredProcessName.TestSignature,
				method: possibleTestMethod,
			});
		} else {
			history.push(`/${locale}/my-mobile-id`);
		}
	}
}

class Test extends React.Component {
	props: InternalProps;
	state = {
		processStarted: false,
	};

	componentDidMount() {
		onUpdate(this.props, this.state.processStarted, () =>
			this.setState({ processStarted: true }),
		);
	}

	componentDidUpdate() {
		onUpdate(this.props, this.state.processStarted, () =>
			this.setState({ processStarted: true }),
		);
	}

	render() {
		const { processName, processState, profile, isLoggedIn, t } = this.props;
		const { isSimActive, isAppActive } = getActivationStatus(profile);
		const showProcess = isSimActive || isAppActive;
		const showProcessDashboard =
			processName && processState !== ProcessState.Result;
		if (isLoggedIn && showProcess && showProcessDashboard) {
			return <DashboardProcess />;
		}
		return (
			<Page
				className={styles.page}
				title={t('seo.title')}
				description={t('seo.description')}
			>
				<Header centered />
				<div className={styles.outerContainer}>
					<div className={styles.processContainer}>
						{isLoggedIn && !showProcess && (
							<H3 className={styles.notAvailable}>
								{t('test.notAvailable')}
							</H3>
						)}
					</div>
				</div>
			</Page>
		);
	}
}

function mapStateToProps(state: ReduxState) {
	return {
		profile: getProfile(state),
		processName: getProcessName(state),
		processState: getProcessState(state),
		isLoggedIn: Boolean(getSessionJwt(state)),
		possibleTestMethod: getPossibleTestMethod(state),
		isFetching: getRequestIsFetching(state, PROFILE_REQUEST_ID),
	};
}

function mapDispatchToProps(dispatch: Function) {
	return {
		onStartProcess: (processName, processStateOptions) =>
			dispatch(processStarted(processName, processStateOptions)),
	};
}

const withHOCs = combineHOCs([
	connect(
		mapStateToProps,
		mapDispatchToProps,
	),
	localize(namespace),
	withStyles(styles),
	withRouter,
])(Test);

export { withHOCs as Test };
