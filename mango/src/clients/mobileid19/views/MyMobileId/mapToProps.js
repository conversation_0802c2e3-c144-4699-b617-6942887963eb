import {
	getProfile,
	getPhoneNumber,
	getSessionJwt,
	getIsLoggedOut,
	getCountry,
} from '../../modules/mobileid-session/selectors';
import { processStarted } from '../../modules/mobileid-processes/actions';
import {
	getProcessName,
	getProcessState,
	getPossibleTestMethod,
	getActiveMaintenanceWindow,
} from '../../modules/mobileid-processes/selectors';
import { PROFILE_REQUEST_ID } from '../../modules/mobileid-session/constants';
import { open, close } from '../../../../modules/modal/actions';
import { getLocale } from '../../../../modules/i18n/selectors';
import {
	getRequestError,
	getRequestHasError,
	getRequestIsCompleted,
	getRequestIsFetching,
	getRequestIsSuccessful,
} from '../../../../modules/request/selectors';
import { profileRequested } from '../../modules/mobileid-session/login/actions';

function mapStateToProps(state: ReduxState) {
	return {
		phoneNumber: getPhoneNumber(state),
		profile: getProfile(state),
		processName: getProcessName(state),
		processState: getProcessState(state),
		isLoggedIn: Boolean(getSessionJwt(state)),
		loggedOut: getIsLoggedOut(state),
		possibleTestMethod: getPossibleTestMethod(state),
		country: getCountry(state),
		maintenanceWindow: getActiveMaintenanceWindow(state),
		locale: getLocale(state),
		requestState: {
			[PROFILE_REQUEST_ID]: {
				isFetching: getRequestIsFetching(state, PROFILE_REQUEST_ID),
				requestError: getRequestError(state, PROFILE_REQUEST_ID),
				hasError: getRequestHasError(state, PROFILE_REQUEST_ID),
				isSuccesful: getRequestIsSuccessful(state, PROFILE_REQUEST_ID),
				isCompleted: getRequestIsCompleted(state, PROFILE_REQUEST_ID),
			},
		},
	};
}

function mapDispatchToProps(dispatch: Function) {
	return {
		onStartProcess: (processName, processStateOptions) =>
			dispatch(processStarted(processName, processStateOptions)),
		openModal: name => dispatch(open(name)),
		closeModal: name => dispatch(close(name)),
		refresh: () => dispatch(profileRequested()),
	};
}

export {
	mapStateToProps,
	mapDispatchToProps,
};
