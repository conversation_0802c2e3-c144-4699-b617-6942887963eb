@import '../../assets/variables.css';

.center {
	text-align: center !important;
}

.outerWrapper {
	display: flex;
	width: 100%;
	min-height: 800px;
	height: 47vw;
	flex-wrap: nowrap;

	@media (max-width: 1200px) {
		flex-wrap: wrap;
		height: fit-content;
		min-height: 0;
	}

	& .contentContainer {
		display: flex;
		flex-basis: 50%;
		padding: 6vw 100px 100px;
		flex-wrap: wrap;

		@media (max-width: 1440px) {
			padding: 50px 80px;
		}

		@media (max-width: 1200px) {
			flex-basis: 100%;
		}

		@media (--screen-mobile) {
			display: block;
			padding: 40px 20px;
		}

		& .progressIndicator {
			flex-basis: 100%;
			align-self: flex-end;
		}
	}

	& .moodImageContainer {
		flex-basis: 50%;
		position: relative;

		@media (max-width: 1200px) {
			display: none;
		}

		& .image {
			height: 100%;
			background-position: top right;
			background-size: cover;
		}

		& .mobileidIcon {
			position: absolute;
			top: 50%;
			right: 0;
			transform: translate(50%, -50%);
			height: 70%;
			width: auto;
		}
	}
}

.inputContainer {
	display: flex;
	margin-bottom: 10px;
	flex-basis: 100%;

	@media (--screen-mobile) {
		flex-wrap: wrap;
	}

	& .simpleInput {
		margin-left: 20px;
		width: 100%;

		@media (--screen-mobile) {
			flex-basis: 100%;
			margin-left: 0;
			margin-top: 12px;
		}
	}

	& .countrySelect {
		@media (--screen-mobile) {
			flex-basis: 100%;

			& > div {
				width: 100%;

				@media (--screen-mobile) {
					width: 50%;
					min-width: 170px;
				}
			}
		}
	}
}

.title {
	flex-basis: 100%;

	@media (max-width: 1440px) {
		margin-bottom: 0;
	}

	@media (max-width: 1200px) {
		margin-bottom: 0;
	}
}

.subTitle {
	flex-basis: 100%;
	transition: all 0.5s ease;
}

.buttonContainer {
	display: flex;
	justify-content: flex-end;
	flex-basis: 100%;

	& .backButton {
		margin-right: 24px;

		@media (--screen-mobile) {
			margin-right: 0;
		}
	}
}

.transitionContainer {
	width: 100%;
	display: block;

	&.transitionEnter {
		opacity: 0;
	}

	&.transitionEnterActive {
		opacity: 1;
		transition: all 500ms ease;
	}

	&.transitionExit {
		opacity: 0;
	}
}

.emiratesCheckboxContainer {
	margin-top: -16px;
	margin-bottom: 32px;

	& .emiratesCheckbox {
		height: 20px;
		width: 20px;
		border-radius: 4px;
	}
}


.errorMsg {
	display: block;
	margin-top: 12px;
}

.error {
	color: red;
	margin-bottom: 12px;
}

@-webkit-keyframes fadeinout {
	0%,
	100% {
		opacity: 0;
	}

	50% {
		opacity: 1;
	}
}

@keyframes fadeinout {
	0%,
	100% {
		opacity: 0;
	}

	50% {
		opacity: 1;
	}
}

.maintenanceContainer {
	border-right: 4px solid orange;
	height: fit-content;
	padding-right: 8px;

	& h4 {
		color: orange;
	}
}

.resendInformation {
	@apply --small;
	margin-top: 12px;
	margin-bottom: 30px;
}

.recaptchaDisclaimer {
	margin-top: 30px;

	& a {
		color: #1967d2;
	}
}

.container {
	margin-bottom: 80px;
	margin: auto;
	max-width: var(--max-width, 1440px);
	padding-right: var(--margin-container-mobile, 20px);
	padding-left: var(--margin-container-mobile, 20px);

	@media (--screen-lg) {
		padding-right: var(--margin-container-desktop, 20px);
		padding-left: var(--margin-container-desktop, 20px);
	}

	@media (--screen-xxxl) {
		max-width: 1704px;
	}
}

.loaderContainer {
	display: block;
	margin: auto;

	&.mobileAppIntegrationContainer {
		padding-top: 20px;
	}
}

.mobileAppIntegrationContainer {
	margin-top: -78px;
}

.mobileEnrollP {
	color: black;

	@media (--screen-mobile) {
		font-size: 16px;
	}
}

.friendlyCaptcha {
	margin-top: 0px;
	margin-bottom: 30px;
	width: 100% !important;
	max-width: 100% !important;
	border-width: 0px !important;
}

.resendButton {
	text-decoration: underline;
	padding: 0 3px;
	cursor: pointer;
	font-weight: 700;
}
