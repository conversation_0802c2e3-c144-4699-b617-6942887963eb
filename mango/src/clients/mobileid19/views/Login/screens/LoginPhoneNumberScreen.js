import React from 'react';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { SimpleBoxInput } from '../../../modules/BoxInput';
import combineHOCs from '../../../../../tango/hoc/combineHOCs';
import { CountrySelect } from '../../../modules/Login';
import { ContinueButton } from '../../../modules/ProgressButton';
import { FriendlyCaptcha } from '../../../../../modules/friendlyCaptcha/FriendlyCaptcha';
import { MobileEnrollText } from '../MobileEnrollText';
import { nextDisabled } from '../utils';
import { OTP_REQUEST_ID } from '../../../modules/mobileid-session/constants';
import { LOGIN_STEP_PHONE_NUMBER } from '../constants';
import { localize, type LocalizeProps } from '../../../../../modules/i18n';
import { namespace } from '../i18n';
import styles from '../Login.css';
import { H4 } from '../../../modules/titles';
import { getIsMobileAppIntegration } from '../../MyMobileId/utils';

type OwnProps = {
	requestState: Object,
	search: string,
	callingCode: string,
	excludedPrefixes: string[],
	phoneNumber: string,
	recaptcha: string,
	handleCountrySelectUpdate: Function,
	handleInputChange: Function,
	handleSimpleInputKeyDown: Function,
	error: boolean,
	setRecaptcha: Function,
	handleRequestOTP: Function,
	handleOnEmirateChange: Function,
};

type Props = OwnProps & LocalizeProps & RouteProps;

function LoginPhoneNumberScreen({
	t,
	requestState,
	search,
	callingCode,
	excludedPrefixes,
	phoneNumber,
	recaptcha,
	handleCountrySelectUpdate,
	handleInputChange,
	handleSimpleInputKeyDown,
	error,
	setRecaptcha,
	handleRequestOTP,
	handleOnEmirateChange,
}: Props) {
	const otpRequestState = requestState[OTP_REQUEST_ID];
	const isMobileAppIntegration = getIsMobileAppIntegration(search);

	const text = {
		default: t(`subtitle.${LOGIN_STEP_PHONE_NUMBER}`),
		mobileEnroll: t(`mobileEnroll.subtitle.${LOGIN_STEP_PHONE_NUMBER}`),
	};
	return (
		<form>
			<MobileEnrollText text={text} search={search} />
			<div className={styles.inputContainer}>
				<CountrySelect
					className={styles.countrySelect}
					selectedItem={callingCode}
					onUpdate={handleCountrySelectUpdate}
					label={t('countrySelectLabel')}
					staticId="STATIC_ID_INPUT_COUNTRY_SELECT"
					excludedPrefixes={excludedPrefixes}
				/>
				<SimpleBoxInput
					className={styles.simpleInput}
					label={t('telLabel')}
					value={phoneNumber}
					onChange={event => handleInputChange(event.target.value)}
					type="tel"
					staticId="STATIC_ID_LOGIN_TEL_INPUT"
					autoFocus
					onKeyDown={handleSimpleInputKeyDown}
				/>
			</div>
			{error && (
				<div className={styles.error}>
					{t('error.validation/invalid-phone-number')}
				</div>
			)}

			{callingCode && callingCode.value === '971' && (
				<div className={styles.emiratesCheckboxContainer}>
					<input
						className={styles.emiratesCheckbox}
						id="isOutsideEmirates"
						type="checkbox"
						onChange={handleOnEmirateChange}
					/>
					<label htmlFor="isOutsideEmirates">
						{t('emiratesCheckboxLabel')}
					</label>
				</div>
			)}

			<div>
				<FriendlyCaptcha
					onChange={setRecaptcha}
					isMobileApp={isMobileAppIntegration}
					className={styles.friendlyCaptcha}
				/>
			</div>

			<div className={styles.errorMsg}>
				<H4 size="like-h6" margin="medium">
					{otpRequestState.hasError &&
						t(`error.${otpRequestState.requestError.toJS().msgKey}`)}
				</H4>
			</div>
			<div className={styles.buttonContainer}>
				<ContinueButton
					onContinue={() => handleRequestOTP('original')}
					disabled={nextDisabled(phoneNumber) || !recaptcha}
					fetching={otpRequestState.isFetching}
					buttonText={t('continue')}
					staticId="STATIC_ID_LOGIN_CONTINUE_BUTTON_1"
				/>
			</div>
		</form>
	);
}

const withHOCs = combineHOCs([localize(namespace), withStyles(styles)])(
	LoginPhoneNumberScreen,
);

export { withHOCs as LoginPhoneNumberScreen };
