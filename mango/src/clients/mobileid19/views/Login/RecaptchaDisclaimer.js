import withStyles from 'isomorphic-style-loader/lib/withStyles';
import React from 'react';
import ExternalLink from '../../../../modules/external-link';
import { localize, type LocalizeProps } from '../../../../modules/i18n';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import { namespace } from './i18n';
import styles from './Login.css';

function RecaptchaDisclaimer({ t }: LocalizeProps) {
	return (
		<p className={styles.recaptchaDisclaimer}>
			{t('recaptchaDisclaimer.part1')}{' '}
			<ExternalLink href="https://policies.google.com/privacy">
				{t('recaptchaDisclaimer.part2')}
			</ExternalLink>{' '}
			{t('recaptchaDisclaimer.part3')}{' '}
			<ExternalLink href="https://policies.google.com/terms">
				{t('recaptchaDisclaimer.part4')}{' '}
			</ExternalLink>
			{t('recaptchaDisclaimer.part5')}
		</p>
	);
}

const withHOCs = combineHOCs([localize(namespace), withStyles(styles)])(
	RecaptchaDisclaimer,
);

export { withHOCs as RecaptchaDisclaimer };
