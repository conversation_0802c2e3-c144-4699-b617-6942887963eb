/* @flow */
import React from 'react';
import { connect } from 'react-redux';
import { Map } from 'immutable';
// import { localize, type LocalizeProps } from '../../../../modules/i18n';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import { Login } from './Login';
import {
	getRequestIsFetching,
	getRequestHasError,
	getRequestIsSuccessful,
	getRequestIsCompleted,
	getRequestError,
} from '../../../../modules/request/selectors';
import {
	otpRequested,
	otpEntered,
} from '../../modules/mobileid-session/login/actions';
import { getProfile } from '../../modules/mobileid-session/selectors';
import {
	OTP_REQUEST_ID,
	OTP_VALIDATE_ID,
	PROFILE_REQUEST_ID,
} from '../../modules/mobileid-session/constants';
import { getActiveMaintenanceWindow } from '../../modules/mobileid-processes/selectors';
import { getLocale } from '../../../../modules/i18n/selectors';
import { getCustomConfig } from '../../../../modules/config/selectors';
import Page from '../../modules/Page';
import { getIsMobileAppIntegration } from '../MyMobileId/utils';
import Header from '../../modules/Header';
import { localize, LocalizeProps } from '../../../../modules/i18n';
import { namespace } from './i18n';

type ConnectedProps = {
	onOtpRequested: Function,
	onOtpEntered: Function,
	requestState: Object,
	profile: Object,
	maintenanceWindow: Object,
	locale: string,
	excludedPrefixes: Array<string>,
	search: string;
};

type Props = ConnectedProps & LocalizeProps;

function ContainerComponent({
	onOtpRequested,
	onOtpEntered,
	requestState,
	profile,
	maintenanceWindow,
	locale,
	excludedPrefixes,
	t,
	search,
}: Props) {
	const isMobileAppIntegration = getIsMobileAppIntegration(search);
	return (
		<Page
			title={t('seo.title')}
			description={t('seo.description')}
		>
			{!isMobileAppIntegration && <Header centered />}
			<Login
				onOtpRequested={onOtpRequested}
				onOtpEntered={onOtpEntered}
				requestState={requestState}
				profile={profile}
				maintenanceWindow={maintenanceWindow}
				locale={locale}
				excludedPrefixes={excludedPrefixes}
				search={search}
			/>
		</Page>
	);
}

function mapStateToProps(state: ReduxState) {
	const customConfig = getCustomConfig(state) || Map();
	return {
		requestState: {
			[OTP_REQUEST_ID]: {
				isFetching: getRequestIsFetching(state, OTP_REQUEST_ID),
				requestError: getRequestError(state, OTP_REQUEST_ID),
				hasError: getRequestHasError(state, OTP_REQUEST_ID),
				isSuccesful: getRequestIsSuccessful(state, OTP_REQUEST_ID),
				isCompleted: getRequestIsCompleted(state, OTP_REQUEST_ID),
			},
			[OTP_VALIDATE_ID]: {
				isFetching: getRequestIsFetching(state, OTP_VALIDATE_ID),
				requestError: getRequestError(state, OTP_VALIDATE_ID),
				hasError: getRequestHasError(state, OTP_VALIDATE_ID),
				isSuccesful: getRequestIsSuccessful(state, OTP_VALIDATE_ID),
				isCompleted: getRequestIsCompleted(state, OTP_VALIDATE_ID),
			},
			[PROFILE_REQUEST_ID]: {
				isFetching: getRequestIsFetching(state, PROFILE_REQUEST_ID),
				requestError: getRequestError(state, PROFILE_REQUEST_ID),
				hasError: getRequestHasError(state, PROFILE_REQUEST_ID),
				isSuccesful: getRequestIsSuccessful(state, PROFILE_REQUEST_ID),
				isCompleted: getRequestIsCompleted(state, PROFILE_REQUEST_ID),
			},
		},
		profile: getProfile(state),
		maintenanceWindow: getActiveMaintenanceWindow(state),
		locale: getLocale(state),
		excludedPrefixes: customConfig.get('excludedPrefixes'),
	};
}

function mapDispatchToProps(dispatch: Function) {
	return {
		onOtpRequested: (
			locale,
			phoneNumber,
			country,
			recaptcha,
			reCaptchaVersion,
			sendMode,
		) =>
			dispatch(
				otpRequested(
					locale,
					phoneNumber,
					country,
					recaptcha,
					reCaptchaVersion,
					sendMode,
				),
			),
		onOtpEntered: smsCode => dispatch(otpEntered(smsCode)),
	};
}

const withHOCs = combineHOCs([
	localize(namespace),
	connect(
		mapStateToProps,
		mapDispatchToProps,
	),
]);

const LoginContainer = (withHOCs(ContainerComponent): ReactComponent<EmptyProps>);

export { LoginContainer };
