/* @flow */
import React from 'react';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { CSSTransition } from 'react-transition-group';
import { withRouter } from 'react-router-dom';
import { ResponsiveBackground } from '../../../../modules/files';
import { localize, LocalizeProps } from '../../../../modules/i18n';
import combineHOCs from '../../../../tango/hoc/combineHOCs';

import { halfWidthImage } from '../../modules/ImageSizes';
import { callingCodes } from '../../modules/Login';
import type { RouteProps } from '../../../../tango/routing/types';
import {
	OTP_REQUEST_ID,
	PROFILE_REQUEST_ID,
} from '../../modules/mobileid-session/constants';
import { ProgressIndicator } from '../../modules/ProgressIndicator/ProgressIndicator';
import { H4 } from '../../modules/titles';
import { namespace } from './i18n';
import styles from './Login.css';
import { Mobileid19BigIcon } from '../../assets/svg/Mobileid19BigIcon';
import {
	formatPhoneNumber,
	getQueryParam,
	getSearch,
	validatePhoneNumber,
} from './utils';
import { LoadingDashboardTile } from '../MyMobileId/tiles/LoadingDashboardTile';
import { HeaderTile } from '../MyMobileId/tiles/HeaderTile';
import {
	LOGIN_STEP_PHONE_NUMBER,
	LOGIN_STEP_OTP,
	MOOD_IMAGE,
	STEPS,
} from './constants';
import { getIsMobileAppIntegration } from '../MyMobileId/utils';
import { MobileEnrollTitle } from './MobileEnrollTitle';
import { LoginOTPScreen } from './screens/LoginOTPScreen';
import type { CallingCode } from '../../modules/Login/CountrySelect/utils/types';
import { LoginPhoneNumberScreen } from './screens/LoginPhoneNumberScreen';

type OwnProps = {
	requestState: Object,
	onOtpRequested: Function,
	search: string,
};

type Props = OwnProps & LocalizeProps & RouteProps;
type State = {
	callingCode: CallingCode,
	phoneNumber: string,
	loginStep: string,
	OTP: string,
	requestedProfile: boolean,
	recaptcha?: string,
	isOutsideEmiratesChecked: boolean,
	error: boolean,
};

const onlyNumbersRegex = /^(\s*|\d+)$/;

function getSendMode(useDigitalMarketplaceAPI: boolean, sendMode: string) {
	if (useDigitalMarketplaceAPI && sendMode === 'original') {
		return 'digital-marketplace';
	}
	return sendMode;
}

const newCaptcha = true;
const recaptchaVersion = newCaptcha ? 'friendly' : 'v2';

class LoginComponent extends React.Component {
	props: Props;
	state: State = {
		callingCode: callingCodes[0],
		phoneNumber: '',
		loginStep: LOGIN_STEP_PHONE_NUMBER,
		OTP: '',
		requestedProfile: false,
		recaptcha: undefined,
		isOutsideEmiratesChecked: false,
		error: false,
	};

	componentDidMount = () => {
		// set values from query params
		const queryParamCallingCode = getQueryParam('prefix');
		const matchingCode = callingCodes.find(
			code => code.value === queryParamCallingCode,
		);

		if (matchingCode) {
			this.setState({ callingCode: matchingCode });
		}

		const queryPhoneNumber = getQueryParam('number');
		if (queryPhoneNumber && onlyNumbersRegex.test(queryPhoneNumber)) {
			this.setState({ phoneNumber: queryPhoneNumber });
		}
	};

	componentDidUpdate = (prevProps) => {
		const { requestState, profile, history, locale } = this.props;
		const { loginStep } = this.state;

		if (
			loginStep === LOGIN_STEP_PHONE_NUMBER &&
			!prevProps.requestState[OTP_REQUEST_ID].isSuccesful &&
			!prevProps.requestState[OTP_REQUEST_ID].isCompleted &&
			requestState[OTP_REQUEST_ID].isSuccesful &&
			requestState[OTP_REQUEST_ID].isCompleted
		) {
			this.setState({ loginStep: LOGIN_STEP_OTP });
			window.scrollTo(0, 0);
		}

		if (profile && profile.simStatus) {
			history.push(`/${locale}/my-mobile-id${getSearch()}`);
		}
	};

	handleInputChange = (value: string) => {
		this.setState({ phoneNumber: value });
		setTimeout(() => {
			const formatedNumber = formatPhoneNumber(
				value,
				this.state.callingCode.value,
			);
			this.setState({
				phoneNumber: formatedNumber,
			});
		}, 50);
	};

	handlePhoneNumberValidation = (() => async () => {
		this.setState({ error: false });
		const formatedNumber = formatPhoneNumber(
			this.state.phoneNumber,
			this.state.callingCode.value,
		);
		const res = await validatePhoneNumber(
			formatedNumber,
			this.state.callingCode.value,
		);
		if (res.valid) {
			return true;
		}
		this.setState({ error: true });
		return false;
	})();

	handleCountrySelectUpdate = (callingCode) => {
		if (callingCode) {
			this.setState({ callingCode });
		}
	};

	handleNewStep = (newStep) => {
		if (newStep) {
			this.setState({
				loginStep: newStep,
				OTP: '',
				requestedProfile: false,
				recaptcha: undefined,
			});
		}
	};

	handleRequestOTP = (() => async (sendMode: string = 'original') => {
		const { locale, onOtpRequested, history, location } = this.props;
		const {
			callingCode,
			phoneNumber,
			recaptcha,
			isOutsideEmiratesChecked,
		} = this.state;

		let actualPhoneNumber = phoneNumber;
		const isValid = await this.handlePhoneNumberValidation();
		if (isValid) {
			// remove all dashes from calling code
			const dashRegex = /-/g;
			const actualCallingCodeValue = callingCode.value.replace(dashRegex, '');

			// remove leading 0 if swiss number
			if (
				phoneNumber &&
				callingCode.code === 'CH' &&
				phoneNumber.charAt(0) === '0'
			) {
				actualPhoneNumber = phoneNumber.substring(1);
			}

			const useDigitalMarketplaceAPI =
				callingCode.value === '971' && !isOutsideEmiratesChecked;

			if (location.search.includes('origin=logged-out')) {
				history.push({
					search: location.search.replace('origin=logged-out', ''),
				});
			}

			onOtpRequested(
				locale,
				`${actualCallingCodeValue}${actualPhoneNumber}`,
				callingCode.code,
				recaptcha,
				recaptchaVersion,
				getSendMode(useDigitalMarketplaceAPI, sendMode),
			);
		}
	})();

	handleEnteredSmsCode = () => {
		const { onOtpEntered } = this.props;
		const { OTP } = this.state;
		onOtpEntered(OTP);
		this.setState({ requestedProfile: true });
	};

	handleSimpleInputKeyDown = (event) => {
		const { phoneNumber, recaptcha } = this.props;
		if (event.key === 'Enter' && phoneNumber !== '' && recaptcha) {
			this.handleRequestOTP('original');
		}
	};

	handleOnEmirateChange = (event) => {
		if (event && event.target) {
			this.setState({
				isOutsideEmiratesChecked: Boolean(event.target.checked),
			});
		}
	};

	render() {
		const {
			t,
			requestState,
			maintenanceWindow,
			locale,
			excludedPrefixes,
			search,
		} = this.props;
		const {
			phoneNumber,
			callingCode,
			OTP,
			loginStep,
			requestedProfile,
			recaptcha,
		} = this.state;

		const profileIsFetching = requestState[PROFILE_REQUEST_ID].isFetching;

		const isMobileAppIntegration = getIsMobileAppIntegration(search);
		const mobileAppIntegrationStyles = {
			[styles.mobileAppIntegrationContainer]: isMobileAppIntegration,
		};

		if (profileIsFetching) {
			return (
				<div
					className={classNames(styles.container, mobileAppIntegrationStyles)}
				>
					<div
						className={classNames(
							styles.loaderContainer,
							mobileAppIntegrationStyles,
						)}
					>
						<HeaderTile isFetching={profileIsFetching} refresh={() => {}} />
						<LoadingDashboardTile />
					</div>
				</div>
			);
		}

		const title = {
			default: t(`origin.${getQueryParam('origin') || 'default'}.title`),
			// TODO: probably need to interfere here based on LOGIN_STEP_PHONE_NUMBER
			mobileEnroll: t(`mobileEnroll.title.${LOGIN_STEP_PHONE_NUMBER}`),
		};

		return (
			<div
				className={classNames(styles.outerWrapper, mobileAppIntegrationStyles)}
			>
				<div className={styles.contentContainer}>
					{loginStep !== LOGIN_STEP_OTP && (
						<MobileEnrollTitle title={title} search={search} />
					)}
					{maintenanceWindow && (
						<div className={styles.maintenanceContainer}>
							<H4>{t('maintenanceTitle')}</H4>
							<p>{maintenanceWindow.getIn(['translations', locale, 'text'])}</p>
						</div>
					)}
					<CSSTransition
						key={loginStep}
						timeout={500}
						in
						classNames={{
							enter: styles.transitionEnter,
							enterActive: styles.transitionEnterActive,
							exit: styles.transitionExit,
						}}
					>
						<div className={styles.transitionContainer}>
							{loginStep === LOGIN_STEP_PHONE_NUMBER && (
								<LoginPhoneNumberScreen
									requestState={requestState}
									search={search}
									callingCode={callingCode}
									excludedPrefixes={excludedPrefixes}
									phoneNumber={phoneNumber}
									recaptcha={recaptcha}
									handleCountrySelectUpdate={this.handleCountrySelectUpdate}
									handleInputChange={this.handleInputChange}
									handleSimpleInputKeyDown={this.handleSimpleInputKeyDown}
									error={this.state.error}
									setRecaptcha={value => this.setState({ recaptcha: value })}
									handleRequestOTP={this.handleRequestOTP}
									handleOnEmirateChange={this.handleOnEmirateChange}
								/>
							)}
							{loginStep === LOGIN_STEP_OTP && (
								<LoginOTPScreen
									otp={OTP}
									requestedProfile={requestedProfile}
									requestState={requestState}
									setOtp={val => this.setState({ OTP: val })}
									submitOtp={this.handleEnteredSmsCode}
									onBack={() => this.handleNewStep(LOGIN_STEP_PHONE_NUMBER)}
									recaptcha={recaptcha}
									setRecaptcha={value => this.setState({ recaptcha: value })}
									requestOtp={this.handleRequestOTP}
									callingCode={this.state.callingCode}
									search={search}
									title={title}
								/>
							)}
						</div>
					</CSSTransition>
					{!isMobileAppIntegration && (
						<ProgressIndicator
							className={styles.progressIndicator}
							steps={STEPS}
							activeStep={loginStep === LOGIN_STEP_PHONE_NUMBER ? 1 : 2}
						/>
					)}
				</div>
				<div className={styles.moodImageContainer}>
					<ResponsiveBackground
						image={MOOD_IMAGE}
						sizes={halfWidthImage}
						className={styles.image}
					/>
					<Mobileid19BigIcon className={styles.mobileidIcon} />
				</div>
			</div>
		);
	}
}

const withHOCs = combineHOCs([
	localize(namespace),
	withStyles(styles),
	withRouter,
]);

const Login = (withHOCs(LoginComponent): ReactComponent<Props>);

export { Login };
