import React from 'react';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import styles from './Login.css';
import { getIsMobileAppIntegration } from '../MyMobileId/utils';
import { H3 } from '../../modules/titles';

type Props = {
	text: Object | string,
	search: string,
	center?: boolean,
};

function MobileEnrollText({ text, search, center }: Props) {
	const centerStyles = {
		[styles.center]: center,
	};

	if (getIsMobileAppIntegration(search)) {
		return (
			<p className={classNames(styles.mobileEnrollP, centerStyles)}>
				{typeof text === 'string' ? text : text.mobileEnroll}
			</p>
		);
	}
	return (
		<H3
			secondary
			margin="medium"
			size="like-h4"
			className={classNames(styles.subTitle, centerStyles)}
		>
			{typeof text === 'string' ? text : text.default}
		</H3>
	);
}

const withHOCs = combineHOCs([withStyles(styles)])(MobileEnrollText);

export { withHOCs as MobileEnrollText };
