import React from 'react';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import styles from './Login.css';
import { getIsMobileAppIntegration } from '../MyMobileId/utils';
import { H1, H4 } from '../../modules/titles';

type Props = {
	title: Object,
	search?: string,
	center?: boolean,
};

function MobileEnrollTitle({ title, search, center }: Props) {
	const centerStyles = {
		[styles.center]: center,
	};

	if (getIsMobileAppIntegration(search)) {
		return (
			<H4 margin="huge" className={classNames(centerStyles)}>
				{title.mobileEnroll}
			</H4>
		);
	}
	return (
		<H1
			margin="huge"
			size="like-h2"
			className={classNames(styles.title, centerStyles)}
		>
			{title.default}
		</H1>
	);
}

const withHOCs = combineHOCs([withStyles(styles)])(MobileEnrollTitle);

export { withHOCs as MobileEnrollTitle };
