/* @flow */
import { spread } from '../../../modules/i18n/util';
import * as EditorModule from '../../../modules/editor/i18n';
import * as Error404 from '../views/Error404/i18n';
import * as Home from '../views/Home/i18n';
import * as PostFinanceModules from '../modules/PostFinance/i18n';
import * as Slider from '../modules/PostFinance/Slider/i18n';
import * as PostFinanceView from '../views/PostFinance/i18n';
import * as PrivateClients from '../views/PrivateClients/i18n';
import * as BusinessClients from '../views/BusinessClients/i18n';
import * as Partners from '../views/Partners/i18n';
import * as Press from '../views/Press/i18n';
import * as Contact from '../views/Contact/i18n';
import * as Faq from '../views/Faq/i18n';
import * as SimOrder from '../views/SimOrder/i18n';
import * as Clients from '../views/Clients/i18n';
import * as ActivationContactProvider from '../views/Activation/ContactProvider/i18n';
import * as AboutUs from '../views/AboutUs/i18n';
import * as ProviderLogos from '../modules/providers/i18n';
import * as Navigation from '../modules/Navigation/i18n';
import * as Footer from '../modules/Footer/i18n';
import * as BriefingPackage from '../modules/BriefingPackage/i18n';
import * as Platforms from '../modules/Platforms/i18n';
import * as ClientsModule from '../modules/Clients/i18n';
import * as PrivateContactForm from '../modules/ContactForms/PrivateContactForm/i18n';
import * as PartnerContactForm from '../modules/ContactForms/PartnerContactForm/i18n';
import * as BusinessOfferForm from '../modules/ContactForms/BusinessOfferForm/i18n';
import * as OffersCalculator from '../modules/offers-calculator/i18n';
import * as NewsletterForm from '../modules/ContactForms/NewsletterForm/i18n';
import * as OpenGraphMetaTags from '../modules/OpenGraphMetaTags/i18n';
import * as HowDoesItWork from '../modules/HowDoesItWork/i18n';
import * as WhereCanIUse from '../modules/WhereCanIUse/i18n';
import * as PurpleBlock from '../modules/PurpleBlock/i18n';
import * as Benefits from '../modules/Benefits/i18n';
import * as IconBlock from '../modules/IconBlock/i18n';
import * as SalesContact from '../modules/SalesContact/i18n';
import * as PartnerBlock from '../modules/PartnerBlock/i18n';
import * as ClientStories from '../modules/ClientStories/i18n';
import * as SelectProvider from '../modules/SelectProvider/i18n';
import * as Roadmap from '../modules/Roadmap/i18n';
import * as NewsSlider from '../modules/NewsSlider/i18n';
import * as NewWorld from '../views/NewWorld/i18n';
import * as PricingPackage from '../modules/PricingPackage/i18n';
import * as CookieDisclaimer from '../modules/CookieDisclaimer/i18n';
import * as Login from '../views/Login/i18n';
import * as MyMobileId from '../views/MyMobileId/i18n';
import * as DashboardProcess from '../modules/DashboardProcess/i18n';
import * as ProgressIndicator from '../modules/ProgressIndicator/i18n';
import * as Documents from '../views/Documents/i18n';
import * as LoginButton from '../modules/LoginButton/i18n';
import * as HealthCheck from '../modules/Healthcheck/i18n';

const i18n = spread([
	EditorModule,
	Error404,
	Home,
	PostFinanceModules,
	Slider,
	PostFinanceView,
	PrivateClients,
	BusinessClients,
	Partners,
	Press,
	Contact,
	Faq,
	SimOrder,
	Clients,
	ActivationContactProvider,
	BriefingPackage,
	ProviderLogos,
	Navigation,
	Footer,
	Platforms,
	ClientsModule,
	PrivateContactForm,
	PartnerContactForm,
	NewsletterForm,
	OpenGraphMetaTags,
	OffersCalculator,
	HowDoesItWork,
	WhereCanIUse,
	PurpleBlock,
	Benefits,
	IconBlock,
	SalesContact,
	PartnerBlock,
	ClientStories,
	SelectProvider,
	Roadmap,
	AboutUs,
	NewsSlider,
	NewWorld,
	PricingPackage,
	CookieDisclaimer,
	Login,
	MyMobileId,
	DashboardProcess,
	ProgressIndicator,
	Documents,
	LoginButton,
	BusinessOfferForm,
	HealthCheck,
]);

const phrases = {
	de: {
		...i18n.de,
	},
	en: {
		...i18n.en,
	},
	fr: {
		...i18n.fr,
	},
	it: {
		...i18n.it,
	},
};

export {
	phrases,
};
