/* @flow */
import React from 'react';
import { isEmpty } from 'lodash';
import { Map, List } from 'immutable';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import ExternalLink from '../../../../../modules/external-link';
import Services from '../Services';
import TextConfigPerLanguage from '../TextConfigPerLanguage';
import WhiteBox from '../../../modules/WhiteBox';
import s from './brandWebsiteConfig.css';
import { prepareTestDriveLink } from './helpers';

export type Props = {
	dealerId: string, // eslint-disable-line react/no-unused-prop-types
	dealerLabel: string,
	brand: Object,
	active: boolean,
	services: List<*>,
	languages: List<string>,
	initialized: boolean,
	active: boolean,
	siteTitles: Map<string, string>,
	siteDescriptions: Map<string, string>,
	usedCarsUrls: Map<string, string>,
	newCarsUrls: Map<string, string>,
	damageReportUrls: Map<string, string>,
	demonstrationCarsUrls: Map<string, string>,
	testDriveUrls: Map<string, string>,
	quoteUrls: Map<string, string>,
	configuratorUrls: Map<string, string>,
	visitAppointmentsUrls: Map<string, string>,
	webShopUrls: Map<string, string>,
	visitDoubleClickFloodlightId: string,
	contactDoubleClickFloodlightId: string,
	excludedInGoogle: boolean,
	hasLivechat: boolean,
	onChange: Function,
	isFetching: boolean,
	isInitializeFetching: boolean,
	setActive: Function,
	setConfig: Function,
	onInitialize: Function,
	touched: boolean,
	customerNr: string,
	allLocations: Array<Object>,
};

function BrandWebsiteConfig(props: Props) {
	const {
		dealerLabel,
		brand,
		services,
		languages,
		active,
		initialized,
		siteTitles,
		siteDescriptions,
		usedCarsUrls,
		newCarsUrls,
		damageReportUrls,
		demonstrationCarsUrls,
		testDriveUrls,
		quoteUrls,
		configuratorUrls,
		visitAppointmentsUrls,
		webShopUrls,
		excludedInGoogle,
		hasLivechat,
		onChange,
		isFetching,
		isInitializeFetching,
		setActive,
		setConfig,
		onInitialize,
		touched,
		visitDoubleClickFloodlightId,
		contactDoubleClickFloodlightId,
		customerNr,
		allLocations,
	} = props;

	const getTestDriveLink = (
		brandId,
		customerNrParam,
		locale,
		allLocationsParam,
		testDriveUrlParam,
	) => {
		if (brandId === 'skoda' && isEmpty(allLocationsParam)) {
			return 'contact';
		}

		if (brandId === 'skoda' && !isEmpty(allLocationsParam)) {
			return prepareTestDriveLink(
				customerNrParam,
				locale,
				allLocationsParam,
				testDriveUrlParam,
			);
		}
		return testDriveUrls;
	};

	const save = (updatedConfig?: Object = {}) => {
		setConfig(
			[[dealerLabel, brand.id], 'dealer-brand-website'],
			{
				config: {
					'site-titles': siteTitles,
					'site-descriptions': siteDescriptions,
					urls: {
						'used-cars': usedCarsUrls,
						'new-cars': newCarsUrls,
						'damage-report': damageReportUrls,
						'demonstration-cars': demonstrationCarsUrls,
						'test-drive': testDriveUrls,
						quotes: quoteUrls,
						'visit-appointments': visitAppointmentsUrls,
						'web-shop': webShopUrls,
						configurator: configuratorUrls,
					},
					'has-livechat': hasLivechat,
					'excluded-in-google': excludedInGoogle,
					'visit-doubleclick-floodlight-id': visitDoubleClickFloodlightId,
					'contact-doubleclick-floodlight-id': contactDoubleClickFloodlightId,
					...updatedConfig,
				},
			},
			brand.id,
		);
	};

	return (
		<WhiteBox>
			<h3>
				{brand.name} {services.includes('VP') && <small>(VP)</small>}{' '}
				{services.includes('VAS') && <small>(VAS)</small>}
			</h3>
			<div className={s.well}>
				<dl>
					<dt>PROD</dt>
					<dd>
						<ExternalLink href={`https://${brand.baseUrl}/${dealerLabel}`}>
							<tt>
								https://{brand.baseUrl}/{dealerLabel}
							</tt>
						</ExternalLink>
					</dd>
					{brand.prodBaseUrl && (
						<React.Fragment>
							<dt>Spectra PROD</dt>
							<dd>
								<ExternalLink
									href={`https://${brand.prodBaseUrl}/${dealerLabel}`}
								>
									<tt>
										https://{brand.prodBaseUrl}/{dealerLabel}
									</tt>
								</ExternalLink>
							</dd>
						</React.Fragment>
					)}
					{brand.testBaseUrl && (
						<React.Fragment>
							<dt>Spectra TEST</dt>
							<dd>
								<ExternalLink
									href={`https://${brand.testBaseUrl}/${dealerLabel}`}
								>
									<tt>
										https://{brand.testBaseUrl}/{dealerLabel}
									</tt>
								</ExternalLink>
							</dd>
						</React.Fragment>
					)}
					{brand.preTestBaseUrl && (
						<React.Fragment>
							<dt>Spectra PRETEST</dt>
							<dd>
								<ExternalLink
									href={`https://${brand.preTestBaseUrl}/${dealerLabel}`}
								>
									<tt>
										https://{brand.preTestBaseUrl}/{dealerLabel}
									</tt>
								</ExternalLink>
							</dd>
						</React.Fragment>
					)}
				</dl>
			</div>

			{!initialized ? (
				<div>
					<p className={s.initialization__infoText}>
						You will lose all unsaved changes. Please go through the
						configuration and check if you have saved everything.
					</p>
					<button
						disabled={isInitializeFetching || initialized}
						onClick={() => onInitialize(dealerLabel)}
					>
						Initialize
					</button>
				</div>
			) : (
				<div>
					<div>
						<h4>Site Title</h4>
						<TextConfigPerLanguage
							languages={languages}
							config={siteTitles}
							onChange={(key, value) =>
								onChange(['site-titles', ...key], value)
							}
							name={`${brand.name}_siteTitles`}
						/>
					</div>
					<div>
						<h4>Site Description</h4>
						<TextConfigPerLanguage
							languages={languages}
							config={siteDescriptions}
							onChange={(key, value) =>
								onChange(['site-descriptions', ...key], value)
							}
							name={`${brand.name}_siteDescriptions`}
						/>
					</div>
					<div>
						<h4>Used cars URL</h4>
						<TextConfigPerLanguage
							languages={languages}
							config={usedCarsUrls}
							onChange={(key, value) =>
								onChange(['urls', 'used-cars', ...key], value)
							}
							name={`${brand.name}_usedCars`}
						/>
					</div>
					<div>
						<h4>New cars URL</h4>
						<TextConfigPerLanguage
							languages={languages}
							config={newCarsUrls}
							onChange={(key, value) =>
								onChange(['urls', 'new-cars', ...key], value)
							}
							name={`${brand.name}_newCars`}
						/>
					</div>
					<div>
						<h4>Demonstration cars URL</h4>
						<TextConfigPerLanguage
							languages={languages}
							config={demonstrationCarsUrls}
							onChange={(key, value) =>
								onChange(['urls', 'demonstration-cars', ...key], value)
							}
							name={`${brand.name}_demonstrationCars`}
						/>
					</div>
					<div>
						<h4>Test drive URL</h4>
						<small className={s.urlEg}>
							{brand.defaultUrls && brand.defaultUrls.testDriveUrl}
						</small>
						<TextConfigPerLanguage
							languages={languages}
							config={
								getTestDriveLink(
									brand.id,
									customerNr,
									languages.first(),
									allLocations,
									testDriveUrls.first(),
								)
							}
							onChange={(key, value) =>
								onChange(['urls', 'test-drive', ...key], value)
							}
							name={`${brand.name}_testDrive`}
						/>
					</div>

					<div>
						<h4>Damage report URL</h4>
						<TextConfigPerLanguage
							languages={languages}
							config={damageReportUrls}
							onChange={(key, value) =>
								onChange(['urls', 'damage-report', ...key], value)
							}
							name={`${brand.name}_damageReport`}
						/>
					</div>
					{brand.id === 'volkswagen-nutzfahrzeuge' && (
						<div>
							<h4>Quote URL</h4>
							<small>VW NF only</small>
							<br />
							<small className={s.urlEg}>
								{brand.defaultUrls && brand.defaultUrls.quotesUrl}
							</small>
							<TextConfigPerLanguage
								languages={languages}
								config={quoteUrls}
								onChange={(key, value) =>
									onChange(['urls', 'quotes', ...key], value)
								}
								name={`${brand.name}_quotes`}
							/>
						</div>
					)}
					{brand.id === 'skoda' && (
						<div>
							<h4>Configurator URL</h4>
							<small>SKODA only</small>
							<TextConfigPerLanguage
								languages={languages}
								config={configuratorUrls}
								onChange={(key, value) =>
									onChange(['urls', 'configurator', ...key], value)
								}
								name={`${brand.name}_configurator`}
							/>
						</div>
					)}
					<div>
						<h4>Visit appointments URL</h4>
						<TextConfigPerLanguage
							languages={languages}
							config={visitAppointmentsUrls}
							onChange={(key, value) =>
								onChange(['urls', 'visit-appointments', ...key], value)
							}
							name={`${brand.name}_visitAppointment`}
						/>
					</div>
					{brand.id !== 'skoda' && (
						<div>
							<h4>Webshop URL</h4>
							<TextConfigPerLanguage
								languages={languages}
								config={webShopUrls}
								onChange={(key, value) =>
									onChange(['urls', 'web-shop', ...key], value)
								}
								name={`${brand.name}_webShop`}
							/>
						</div>
					)}
					<div>
						<h4>Visit DoubleClick Floodlight ID</h4>
						<span>
							<input
								type="text"
								id={`${brand.name}_visit-doubleclick-floodlight-id`}
								defaultValue={visitDoubleClickFloodlightId}
								onChange={event =>
									onChange(
										['visit-doubleclick-floodlight-id'],
										event.target.value,
									)
								}
							/>
						</span>
					</div>
					<div>
						<h4>Contact DoubleClick Floodlight ID</h4>
						<span>
							<input
								id={`${brand.name}_contact-doubleclick-floodlight-id`}
								type="text"
								defaultValue={contactDoubleClickFloodlightId}
								onChange={event =>
									onChange(
										['contact-doubleclick-floodlight-id'],
										event.target.value,
									)
								}
							/>
						</span>
					</div>
					<div>
						<h4>Livechat Integration</h4>
						<span>
							<input
								id={`${brand.name}_livechat`}
								type="checkbox"
								defaultChecked={hasLivechat}
								onChange={event =>
									onChange(['has-livechat'], event.target.checked)
								}
							/>
							<label htmlFor={`${brand.name}_livechat`}>Livechat</label>
						</span>
					</div>
					<hr className={s.separator} />
					{services && <Services services={services} brand={brand.id} />}
					<div className={s.buttonContainer}>
						<button
							onClick={() => save()}
							disabled={isFetching || !initialized}
							className={touched ? s.touched : ''}
						>
							Save
						</button>
						<button
							onClick={() =>
								setActive(
									[[dealerLabel, brand.id], 'dealer-brand-website'],
									!active,
									brand.id,
								)
							}
							disabled={isFetching || !initialized || touched}
							className="secondary"
						>
							{active ? 'Deactivate' : 'Activate'}
						</button>
						<button
							onClick={() => {
								onChange(['excluded-in-google'], !excludedInGoogle);
								save({ 'excluded-in-google': !excludedInGoogle });
							}}
							disabled={isFetching || !initialized || touched}
							className="secondary"
						>
							{excludedInGoogle ? 'Include on Google' : 'Exclude on Google'}
						</button>
					</div>
				</div>
			)}
		</WhiteBox>
	);
}

export default withStyles(s)(BrandWebsiteConfig);
