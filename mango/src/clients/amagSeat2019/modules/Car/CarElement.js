/* @flow */
import React from 'react';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import classNames from 'classnames';
import { connect } from 'react-redux';

import styles from './CarElement.css';
import { Link } from '../Layout';
import { type LinkProps } from '../../../../../src/tango/routing';
import { Image } from '../../../../modules/files';
import { Info } from '../../assets/svg';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import { Tooltip } from '../tooltip';
import { localize } from '../../../../modules/i18n';
import type { LocalizeProps } from '../../../../modules/i18n';
import { namespace } from './i18n';
import withDealerBrandWebsiteConfig from '../../../amagCore/modules/withDealerBrandWebsiteConfig';
import type { DealerBrandWebsiteConfigProps } from '../../../amagCore/modules/withDealerBrandWebsiteConfig';
import {
	getDealerBrandWebsites,
	getDealerLabel,
} from '../../../amagCore/modules/DealerConfigProvider/selectors';
import { GTM_CLASS_MAPPING } from '../../../amagCore/modules/tracking/constants';

type Props = LocalizeProps &
	DealerBrandWebsiteConfigProps & {
		linkProps: LinkProps,
		alias?: string,
		name: string,
		tag?: any,
		image: *,
		staticModel?: boolean,
		className?: string,
	};

const newsletterLink = {
	de: 'https://www.seat.ch/de/angebote-konfigurieren/newsletter.html',
	fr: 'https://www.seat.ch/fr/offres-configurateur/newsletter.html',
	it: 'https://www.seat.ch/it/offerte-configurazione/newsletter.html',
};

function getSecondaryLink(staticModel, alias, locale, t, testDriveUrl) {
	if (!staticModel) {
		if (alias && alias.includes('mii-electric')) {
			return (
				<Link to={newsletterLink[locale]} orange arrow external>
					Newsletter
				</Link>
			);
		}

		if (testDriveUrl) {
			return (
				<Link
					to={testDriveUrl}
					className={GTM_CLASS_MAPPING.testDrive}
					orange
					arrow
					custom
				>
					{t(`${namespace}.testDrive`)}
				</Link>
			);
		}
	}
	return null;
}

const getLinks = (alias, locale) => ({
	to: 'car.model.alias',
	params: {
		lang: locale,
		aliasName: alias,
	},
	external: false,
});

function CarElement({
	alias,
	name,
	tag,
	className,
	image,
	staticModel,
	testDriveUrl,
	locale,
	t,
}: Props) {
	const linkProps = getLinks(alias, locale);

	return (
		<div className={classNames(className, styles.car)}>
			{tag && tag.get('name') ? (
				<span className={styles.badge}>
					<em>{tag.get('name')}</em>
					{tag.get('description') ? <Info /> : null}
					{tag.get('description') ? (
						<Tooltip tooltip={tag.get('description')} />
					) : null}
				</span>
			) : null}

			<Link {...linkProps}>
				<Image image={image} />
			</Link>

			<div className={styles.links}>
				<Link {...linkProps}>
					<h2 className={styles.carName}>{name}</h2>
				</Link>

				{!['New Leon', 'New Leon Sportstourer'].includes(name) &&
					getSecondaryLink(staticModel, alias, locale, t, testDriveUrl)}
			</div>
		</div>
	);
}

function mapStateToProps(state) {
	return {
		dealerBrandWebistes: getDealerBrandWebsites(state),
		dealerLabel: getDealerLabel(state),
	};
}

const withHocs = combineHOCs([
	connect(mapStateToProps),
	withDealerBrandWebsiteConfig,
	localize(),
	withStyles(styles),
]);

export default withHocs(CarElement);
