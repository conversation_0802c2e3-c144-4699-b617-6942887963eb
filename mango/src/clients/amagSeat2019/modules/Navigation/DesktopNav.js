import React, { useState, useEffect } from 'react';
import classNames from 'classnames';
import { withRouter } from 'react-router';
import { Link } from '../../../../tango/routing';
import { <PERSON><PERSON>, <PERSON> } from '../../assets/svg';
import { LogoutButtonSimple } from '../../../../modules/login';
import NavigationBlock from './NavigationBlock';
import { NavigationDropdownBlock } from './NavigationDropdownBlock';
import type { NavElement, DropdownNavElement } from './types';
import styles from './Navigation.css';

type Props = {
	primary: Array<DropdownNavElement>,
	secondary: Array<NavElement>,
	top: Array<NavElement>,
	admin: Array<NavElement>,
	isLoggedIn: boolean,
	isServicePartner: boolean,
	name?: string,
	history: Object,
};

function DesktopNav(props: Props) {
	const {
		primary,
		secondary,
		top,
		admin,
		isLoggedIn,
		isServicePartner,
		name,
		history,
	} = props;

	const [mobileMenu, setMobileMenu] = useState(false);
	const [unlisten, setUnlisten] = useState(null);

	useEffect(() => {
		setUnlisten(history.listen(() => {
			setMobileMenu(false);
		}));

		return () => {
			unlisten();
		};
	}, [history]);

	const toggleMenu = () => {
		setMobileMenu(!mobileMenu);
	};

	return (
		<nav className={styles.navigation}>
			<div
				className={classNames(styles.inner, {
					[styles.active]: mobileMenu,
				})}
			>
				<div className={styles.topBar}>
					<h1 className={styles.partnerName}>{name}</h1>
					<NavigationBlock
						type="top"
						navElements={top}
						className={styles.topNav}
					/>
				</div>
				<div className={styles.main}>
					<Link to="home" className={styles.logoLink}>
						{isServicePartner ? <Logo /> : <Logo />}
					</Link>
					<button onClick={toggleMenu} className={styles.burgerMenu}>
						{!mobileMenu ? <Burger /> : <Burger close />}
					</button>
					<div
						className={classNames(styles.menus, {
							[styles.active]: mobileMenu,
						})}
					>
						<NavigationDropdownBlock
							type="primary"
							navElements={primary}
							className={styles.primaryMenu}
						/>
						<div className={styles.secondaryMenuWrapper}>
							<NavigationBlock
								type="secondary"
								navElements={secondary}
								className={styles.secondaryMenu}
							/>
						</div>
						{isLoggedIn && (
							<div className={styles.adminMenu}>
								<LogoutButtonSimple>Logout</LogoutButtonSimple>
								<NavigationBlock
									type="admin"
									navElements={admin}
									className={styles.adminMenu}
								/>
							</div>
						)}
					</div>
				</div>
			</div>
		</nav>
	);
}

export default withRouter(DesktopNav);
