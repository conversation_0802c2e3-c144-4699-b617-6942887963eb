/* eslint-disable max-len */
/* @flow */
import React from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import { getSession } from '../../../../modules/session';
import { localize, type LocalizeProps } from '../../../../modules/i18n';
import withDealerBrandWebsiteConfig from '../../../amagCore/modules/withDealerBrandWebsiteConfig';
import type { DealerBrandWebsiteConfigProps } from '../../../amagCore/modules/withDealerBrandWebsiteConfig';
import {
	makeGetDealerIsRetail,
	makeGetDealerIsServicePartner,
	getDealerHasNewRetailerLayout,
	getDealerId,
	getDealerIsAmagRetailer,
} from '../../../amagCore/modules/DealerConfigProvider/selectors';
import configuratorLink from '../configuratorLink';
import DesktopNav from './DesktopNav';
import { namespace } from './i18n';

import withDealer from '../../../amagCore/modules/withDealer';
import CarElement from '../Car/CarElement';
import withAlias from '../../../amagCore/modules/alias/withAlias';
import WholesaleDashboard from '../../views/WholesaleDashboard';
import PromotionDashboard from '../../views/PromotionDashboard';
import ServiceDashboard from '../../views/ServiceDashboard';
import { DAMAGE_REPORT_ALIAS } from '../../modules/constants';
import { prepareLinkItem } from '../../../amagCore/modules/DealerConfigProvider/prepareLinkItem';
import { NAV_MODELS } from '../../modules/Car/constants';

// TODO: Temporary fix until images are fixed in the backend
function convertModelImage(model: any) {
	const rawImage = model.getIn(['image', 'raw']);
	const refactoredImage = rawImage.mapKeys(k => {
		if (k === 'src') {
			return 'images';
		}
		return k;
	});
	return model.setIn(['image', 'raw', 'images'], refactoredImage.get('images'));
}

function makePrimaryDropdownNav(
	t: TranslateFunction,
	isRetailPartner: boolean,
	post,
	isServicePartner: boolean,
) {
	const carOverview = NAV_MODELS.map((model, index) => (
		<CarElement
			key={index}
			alias={model.get('alias')}
			aliasTemplate={model.get('aliasTemplate')}
			url={model.get('url')}
			name={model.get('name')}
			image={convertModelImage(model).get('image')}
			staticModel={model.get('staticModel')}
		/>
	));

	const showUsedCarsOnly = isServicePartner;

	const links = [
		{
			label: t(`${namespace}.models`),
			route: 'car.dashboard',
			innerElements: carOverview,
			modelOverview: true,
		},
		{
			label: showUsedCarsOnly
				? t(`${namespace}.buy`)
				: t(`${namespace}.configureNbuy`),
			route: 'configureNbuy',
			innerElements: [
				<PromotionDashboard
					showTitle={false}
					usedCarsOnly={showUsedCarsOnly}
				/>,
			],
		},
		{
			label: t(`${namespace}.service`),
			route: 'service',
			innerElements: [<ServiceDashboard showTitle={false} />],
		},
		{
			label: t(`${namespace}.business`),
			route: 'business',
			innerElements: [<WholesaleDashboard showTitle={false} />],
		},
	].filter(
		link =>
			isRetailPartner ||
			(link.route !== 'car.dashboard' && link.route !== 'business'),
	);

	return links;
}

function makeSecondaryNav(
	t: TranslateFunction,
	locale: Locale,
	testDriveUrl: string,
	isRetailPartner: boolean,
	dealerId: string,
	damageReportUrl: string,
	hasNewRetailerLayout: boolean,
) {
	const testdriveLink = hasNewRetailerLayoutParam => {
		if (hasNewRetailerLayoutParam) {
			return {
				route: 'contact',
			};
		}

		if (dealerId === '0000003900' && testDriveUrl) {
			return {
				route: testDriveUrl,
				external: true,
			};
		}

		const testdriveLinkItem = prepareLinkItem({ uri: testDriveUrl, locale });

		if (!testdriveLinkItem || testdriveLinkItem.external) {
			return {
				route: 'form.testdrive',
			};
		}

		return {
			route: {
				name: testdriveLinkItem.to,
				params: testdriveLinkItem.params,
			},
		};
	};

	const damageReportLink = () => {
		const damageReportLinkItem = prepareLinkItem({
			uri: damageReportUrl || DAMAGE_REPORT_ALIAS,
			locale,
			isInternalLink: !damageReportUrl,
		});

		if (!damageReportLinkItem || !damageReportLinkItem.params) {
			return {
				route: '',
			};
		}

		return {
			route: {
				name: damageReportLinkItem.to,
				params: damageReportLinkItem.params,
			},
		};
	};

	const hasTestDriveUrl = Boolean(testDriveUrl);
	const configuratorNavItem = {
		id: 'configurator',
		route: configuratorLink[locale],
		text: t(`${namespace}.configurator`),
		external: true,
	};
	const links = [
		...(isRetailPartner ? [configuratorNavItem] : []),
		{
			id: 'testDrive',
			text: t(`${namespace}.testDrive`),
			...testdriveLink(hasNewRetailerLayout),
		},
		{
			id: 'damageReport',
			text: t(`${namespace}.damageReport`),
			...damageReportLink(),
		},
	]
		.filter(
			link =>
				hasTestDriveUrl || (!link.route || link.route !== 'form.testdrive'),
		)
		.filter(link => isRetailPartner || link.route !== 'form.testdrive');

	return links;
}

function makeTopNav(
	t: TranslateFunction,
	isRetailPartner: boolean,
	hasNewRetailerLayout: boolean,
	isAmagRetailer: boolean,
) {
	const links = [
		{
			route: 'aboutus',
			text: t(`${namespace}.aboutus`),
		},
		{
			route: 'team',
			text: t(`${namespace}.team`),
		},
		{
			route: 'contact',
			text: t(`${namespace}.contact`),
		},
	].filter(
		item =>
			(!hasNewRetailerLayout ||
				(hasNewRetailerLayout && item.route !== 'team')) &&
			(!isRetailPartner || (item.route !== 'team' && isRetailPartner)) &&
			(!isAmagRetailer || (item.route !== 'team' && isAmagRetailer)),
	);

	return links;
}

function makeAdminNav(t: TranslateFunction) {
	return [
		{
			route: 'support',
			text: t(`${namespace}.support`),
		},
		{
			route: 'admin.article',
			text: t(`${namespace}.articles`),
		},
	];
}

type Props = LocalizeProps &
	DealerBrandWebsiteConfigProps & {
		name: string,
		isLoggedIn: boolean,
		isRetailPartner: boolean,
		isServicePartner: boolean,
		hasNewRetailerLayout: boolean,
		dealerId: string,
		isAmagRetailer: boolean,
	};

function Navigation(props: Props) {
	const {
		name,
		website,
		t,
		locale,
		testDriveUrl,
		isLoggedIn,
		isRetailPartner,
		isServicePartner,
		post,
		hasNewRetailerLayout,
		dealerId,
		isAmagRetailer,
		damageReportUrl,
	} = props;
	const top = makeTopNav(t, false, hasNewRetailerLayout, isAmagRetailer);
	const primaryDropdownNav = makePrimaryDropdownNav(
		t,
		isRetailPartner,
		post,
		isServicePartner,
	);
	const secondaryNav = makeSecondaryNav(
		t,
		locale,
		testDriveUrl,
		isRetailPartner,
		dealerId,
		damageReportUrl,
		hasNewRetailerLayout,
	);
	const adminNav = makeAdminNav(t);
	return (
		<DesktopNav
			primary={primaryDropdownNav}
			secondary={secondaryNav}
			admin={adminNav}
			top={top}
			isLoggedIn={isLoggedIn}
			isServicePartner={isServicePartner}
			website={website}
			name={name}
		/>
	);
}

function makeMapStateToProps() {
	const getDealerIsRetail = makeGetDealerIsRetail();
	const getDealerIsServicePartner = makeGetDealerIsServicePartner();
	return (state: State) => {
		const session = getSession(state);
		return {
			isLoggedIn: session.isLoggedIn,
			isRetailPartner: getDealerIsRetail(state)('seat'),
			isAmagRetailer: getDealerIsAmagRetailer(state),
			isServicePartner:
				getDealerIsServicePartner(state)('seat') &&
				!getDealerIsRetail(state)('seat'),
			hasNewRetailerLayout: getDealerHasNewRetailerLayout(state),
			dealerId: getDealerId(state),
		};
	};
}

function mapPropsToAlias({ locale }: LocalizeProps): string {
	return `amag/${locale}/seat2019/overview`;
}

const withHocs = combineHOCs([
	withRouter,
	connect(makeMapStateToProps),
	withDealer,
	withDealerBrandWebsiteConfig,
	localize(),
	withAlias(mapPropsToAlias),
]);

export default (withHocs(Navigation): ReactComponent<EmptyProps>);
