/* @flow */
import React from 'react';
import { connect } from 'react-redux';
import { localize, type LocalizeProps } from '../../../../modules/i18n';
import type { Props as AliasProps } from '../../../amagCore/modules/alias/withAlias';
import {
	makeGetDealerIsRetail,
	makeGetDealerName,
	getDealerHasNewRetailerLayout,
	getDealerId,
	getDealerIsAmagRetailer,
	makeGetDealerIsServicePartner,
} from '../../../amagCore/modules/DealerConfigProvider/selectors';
import { getLocale } from '../../../../modules/i18n/selectors';
import Footer from './Footer';
import { namespace } from './i18n';
import withAlias from '../../../amagCore/modules/alias/withAlias';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import configuratorLinks from '../configuratorLink';
import { SeatFooterSection, type FooterLinkType } from './SeatFooterSection';
import FooterLink from './FooterLink';
import withDealerBrandWebsiteConfig, {
	type DealerBrandWebsiteConfigProps,
} from '../../../amagCore/modules/withDealerBrandWebsiteConfig';
import {
	externalUrls,
	NEW_RETAIL_PAGE_DEALERS_WHITELIST,
} from '../../../amagCore/modules/constants';
import seasonalLinks from '../../views/PromotionDashboard/seasonalLinks';
import { OCCASION_PLUS_ALIAS } from '../../modules/constants';

import styles from './Footer.css';
import { prepareLinkItem } from '../../../amagCore/modules/DealerConfigProvider/prepareLinkItem';
import { NAV_MODELS } from '../../modules/Car/constants';

type FooterNavColumnType = {
	title: string,
	links: Array<FooterLinkType>,
	locale?: string,
};

type Props = LocalizeProps &
	AliasProps &
	DealerBrandWebsiteConfigProps & {
		isRetailPartner: boolean,
		dealerName: string,
		hasNewRetailerLayout: boolean,
		dealerkey: string,
		isAmagRetailer: boolean,
		hasOccasionPlus: boolean,
		isServicePartner: boolean,
	};

function FooterColumn(links: FooterNavColumnType) {
	return (
		<div className={styles.item}>
			<h3 className={styles.title}>{links.title}</h3>
			<ul className={styles.linkList}>
				{links.links.map((item, idx) => (
					<FooterLink key={idx} className={styles.linkItem} {...item} />
				))}
			</ul>
		</div>
	);
}
const getLinks = (alias: string, locale: string, name: string) => ({
	caption: name,
	external: false,
	route: {
		params: {
			lang: locale,
			aliasName: alias,
		},
		name: 'car.model.alias',
	},
});

function modelColumn(post, t, locale): FooterNavColumnType {
	const modelNavLinks = NAV_MODELS.map(model =>
		getLinks(model.get('alias'), locale, model.get('name')),
	).filter(link => link);

	return {
		title: t('models'),
		links: modelNavLinks,
	};
}

function SeatFooter({
	t,
	locale,
	isRetailPartner,
	dealerName,
	post,
	testDriveUrl,
	hasNewRetailerLayout,
	dealerId,
	isAmagRetailer,
	newCarsUrl,
	usedCarsUrl,
	hasOccasionPlus,
	isServicePartner,
}: Props) {
	const dealerLinks: FooterNavColumnType = {
		title: dealerName,
		links: [
			{ key: 'aboutus', route: 'aboutus', caption: t('aboutus') },
			{ key: 'team', route: 'team', caption: t('team') },
			{ key: 'contact', route: 'contact', caption: t('contact') },
		].filter(
			item =>
				(!hasNewRetailerLayout ||
					(hasNewRetailerLayout && item.key !== 'team')) &&
				(!isAmagRetailer || (isAmagRetailer && item.key !== 'team')),
		),
	};

	const footerLinks: Array<FooterLinkType> = [
		{ key: 'legalNotice', route: 'legalNotice', caption: t('legalNotice') },
		{
			key: 'dataProtection',
			route: externalUrls.seat.dataProtection[locale],
			caption: t('dataProtection'),
			external: true,
		},
		{ key: 'impressum', route: 'imprint', caption: 'Impressum' },
	].filter(
		item =>
			NEW_RETAIL_PAGE_DEALERS_WHITELIST.includes(dealerId) ||
			item.key !== 'impressum',
	);

	if (dealerId === '0000000757') {
		footerLinks.push({
			key: 'partnerDataProtection',
			route: `https://www.gschwend-garage.ch/${locale}/datenschutzerklaerung`,
			caption: t('partnerDataProtection'),
			external: true,
		});
	}

	if (dealerId === '0000003416') {
		footerLinks.push({
			key: 'partnerDataProtection',
			route: 'https://www.pschweizerag.ch/datenschutz',
			caption: t('partnerDataProtection'),
			external: true,
		});
	}

	if (dealerId === '0000000522') {
		footerLinks.push({
			key: 'partnerDataProtection',
			route: `https://www.gng.ch/${locale}/datenschutzerklaerung`,
			caption: t('partnerDataProtection'),
			external: true,
		});
	}

	if (dealerId === 'CHE040') {
		footerLinks.push({
			key: 'partnerDataProtection',
			route: 'https://www.garage-johann-frei.ch/datenschutz',
			caption: t('partnerDataProtection'),
			external: true,
		});
	}

	if (dealerId === 'CHE024') {
		footerLinks.push({
			key: 'partnerDataProtection',
			route: 'https://www.sennautos.ch/fr/protection-des-donnees',
			caption: t('partnerDataProtection'),
			external: true,
		});
	}

	// Used car link "can" come from the dealer config, but it's not always there, so fall back to the 'usedcars' alias
	// TODO: pull these 2 functions out and combine into a reusable function, also needed by PromotionsDashboard.js
	const usedCarsLink = () => {
		if (hasOccasionPlus) {
			return {
				route: OCCASION_PLUS_ALIAS,
				isInternalLink: true,
			};
		}

		if (usedCarsUrl) {
			const usedCarsLinkItem = prepareLinkItem({ uri: usedCarsUrl, locale });

			if (!usedCarsLinkItem) return {};

			if (!usedCarsLinkItem.external) {
				// it's a local route
				return {
					// transform the used car link result to a footer link
					route: {
						name: usedCarsLinkItem.to,
						params: usedCarsLinkItem.params,
					},
				};
			}

			return {
				// it's an external URL
				route: usedCarsLinkItem.to,
				external: true,
			};
		}

		return {}; // will be filtered out
	};

	const newCarsLink = () => {
		if (newCarsUrl) {
			const newCarsLinkItem = prepareLinkItem({ uri: newCarsUrl, locale });

			if (!newCarsLinkItem) return {};

			if (!newCarsLinkItem.external) {
				return {
					route: {
						name: newCarsLinkItem.to,
						params: newCarsLinkItem.params,
					},
				};
			}

			return {
				route: newCarsLinkItem.to,
				external: true,
			};
		}

		return {};
	};

	const testdriveLink = () => {
		if (dealerId === '0000003900' && testDriveUrl) {
			return {
				route: testDriveUrl,
				external: true,
			};
		}

		const testdriveLinkItem = prepareLinkItem({ uri: testDriveUrl, locale });

		if (!testdriveLinkItem || testdriveLinkItem.external) {
			return {
				route: 'form.testdrive',
			};
		}

		return {
			route: {
				name: testdriveLinkItem.to,
				params: testdriveLinkItem.params,
			},
		};
	};

	const dashboards = [
		{
			dashboardName: 'promotions',
			title: t('configureAndBuy'),
			links: [
				// Attention: ids are used for chained filters
				{
					key: 'offers',
					route: seasonalLinks[locale],
					caption: t('offers'),
					external: true,
				},
				{
					key: 'specialModels',
					route: t('specialModelsUrl'),
					caption: t('specialModelsLabel'),
					external: true,
				},
				{
					key: 'specialPackages',
					route: t('specialPackagesUrl'),
					caption: t('specialPackagesLabel'),
					external: true,
				},
				{
					key: 'configurator',
					route: configuratorLinks[locale],
					caption: t('configurator'),
					external: true,
				},
				{ key: 'testDrive', caption: t('testDrive'), ...testdriveLink() },
				{ key: 'usedcars', caption: t('usedcars'), ...usedCarsLink() },
				{ key: 'newcars', caption: t('newcars'), ...newCarsLink() },
			]
				.filter(link => testDriveUrl || link.key !== 'testDrive')
				.filter(link => usedCarsUrl || link.key !== 'usedcars')
				.filter(link => newCarsUrl || link.key !== 'newcars'),
		},
		{
			dashboardName: 'service',
			title: t('service'),
		},
		{
			dashboardName: 'wholesale',
			title: t('business'),
		},
	];

	const serviceDashboards = [
		{
			dashboardName: 'promotions',
			title: isServicePartner ? t('buy') : t('configureAndBuy'),
			links: [{ key: 'usedcars', caption: t('usedcars'), ...usedCarsLink() }]
				.filter(link => testDriveUrl || link.key !== 'testDrive')
				.filter(link => usedCarsUrl || link.key !== 'usedcars'),
		},
		{
			dashboardName: 'service',
			title: t('service'),
		},
	];

	const footerDashboards = isServicePartner ? serviceDashboards : dashboards;

	return (
		<Footer links={footerLinks}>
			{isRetailPartner && FooterColumn(modelColumn(post, t, locale))}
			{footerDashboards.map((dashboard, idx) => (
				<SeatFooterSection
					key={idx}
					dashboardName={dashboard.dashboardName}
					title={dashboard.title}
					links={dashboard.links || []}
				/>
			))}
			{FooterColumn(dealerLinks)}
		</Footer>
	);
}

function makeMapStateToProps() {
	const gerDealerName = makeGetDealerName();
	const getDealerIsRetail = makeGetDealerIsRetail();
	const getDealerIsServicePartner = makeGetDealerIsServicePartner();

	return (state: State) => {
		const locale = getLocale(state);
		return {
			locale,
			dealerName: gerDealerName(state)(locale),
			isRetailPartner: getDealerIsRetail(state)('seat'),
			isAmagRetailer: getDealerIsAmagRetailer(state),
			hasNewRetailerLayout: getDealerHasNewRetailerLayout(state),
			dealerId: getDealerId(state),
			isServicePartner:
				getDealerIsServicePartner(state)('seat') &&
				!getDealerIsRetail(state)('seat'),
		};
	};
}

function mapPropsToAlias({ locale }: LocalizeProps): string {
	return `amag/${locale}/seat2019/overview`;
}

const withHocs = combineHOCs([
	withDealerBrandWebsiteConfig,
	connect(makeMapStateToProps),
	localize(namespace),
	withAlias(mapPropsToAlias),
]);

export default withHocs(SeatFooter);
