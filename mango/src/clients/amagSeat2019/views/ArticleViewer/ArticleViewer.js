/* @flow */
import React from 'react';
import { Map } from 'immutable';
import { Helmet } from 'react-helmet-async';

import type { Identity } from '../../../../modules/request/types';
import CoreArticleViewer, {
	type ChildProps,
} from '../../../amagCore/modules/article/ArticleViewer';
import EditButton from '../../modules/EditButton';
import Loader from '../../modules/Loader';
import Error404 from '../Error404';
import Article from './Article';
import { NoIndex } from '../../../amagCore/modules/NoIndex';
import { getCustomClassKey } from '../../../amagCore/modules/Placeholders/constants';

type Props = {
	alias?: string,
	identity?: Identity,
	isTeamPage?: boolean,
	placeholder?: Map<string, any>,
	isCarModel?: boolean,
	locale?: string,
};

function ArticleViewer(props: Props) {
	const {
		alias,
		identity,
		isTeamPage,
		placeholder,
		locale,
		isCarModel = false,
	} = props;

	const hasPlaceholder = !!placeholder;

	return (
		<CoreArticleViewer
			alias={alias}
			identity={identity}
			notFoundComponent={Error404}
			loaderComponent={Loader}
			hasPlaceholder={hasPlaceholder}
		>
			{(childProps: ChildProps) => {
				const {
					tags,
					post,
					identity: postIdentity,
					usePlaceholder,
				} = childProps;

				const postOrPlaceholder =
					usePlaceholder && placeholder ? placeholder : post;

				const modelText = {
					de: 'modelle',
					fr: 'voitures',
					it: 'auto',
				};

				return (
					<React.Fragment>
						{identity && !alias && <NoIndex />}

						{isCarModel && (
							<Helmet>
								<link
									rel="canonical"
									href={`https://seat.ch/${locale}/${
										modelText[locale]
									}/${alias.split('model-seat-')[1]}`}
								/>
							</Helmet>
						)}

						<Article
							post={postOrPlaceholder}
							customClassKey={getCustomClassKey(alias)}
						/>

						{!usePlaceholder && (
							<EditButton
								articleId={postIdentity.first()}
								isTeamPage={isTeamPage}
								tags={tags}
							/>
						)}
					</React.Fragment>
				);
			}}
		</CoreArticleViewer>
	);
}

export default ArticleViewer;
