/* @flow */
export const cloudinaryPhrasesDefault = {
	de: {
		'modules.files.cloudinaryUpload.dropArea.images': 'Laden Sie ein Bild hoch (max. 20 MB) - Optimale Bildgröße: 1920 x 800 Pixel',
		'modules.files.cloudinaryUpload.dropArea.images.teamMember': 'Laden Sie ein Bild hoch (max. 20 MB) - Optimale Bildgröße: 600 x 800 Pixel',
		'modules.files.cloudinaryUpload.dropArea.imagesMultiple': 'Laden Sie ein oder mehrere Bilder hoch (max. 20 MB)',
		'modules.files.cloudinaryUpload.dropArea.documents': 'Laden Sie ein oder oder mehrere Dokumente hoch.',
		'modules.files.cloudinaryUpload.dropArea.spectraDocumentsApi': 'Laden Sie ein oder oder mehrere Dokumente hoch.',
		'modules.files.upload.invalidImageSize':
			'Ungültige Bild Dimensionen für: %{imageName} ( %{imageWidth} x %{imageHeight} ) - Minimum: %{minW} x %{minH}',
		'modules.files.upload.type.SINGLE': 'Bild',
		'modules.files.upload.type.GALLERY': 'Galerie',
		'modules.files.upload.caption.placeholder': 'Bildbeschriftung eingeben',
	},
	fr: {
		'modules.files.cloudinaryUpload.dropArea.images': 'Ajouter une photo (max. 20 MB) - Taille optimale de l\'image : 1920 x 800 Pixel',
		'modules.files.cloudinaryUpload.dropArea.images.teamMember': 'Ajouter une photo (max. 20 MB)',
		'modules.files.cloudinaryUpload.dropArea.imagesMultiple': 'Ajouter plusieurs photos ou images (max. 20 MB)',
		'modules.files.cloudinaryUpload.dropArea.documents': 'Téléchargez des documents',
		'modules.files.cloudinaryUpload.dropArea.spectraDocumentsApi': 'Téléchargez des documents',
		'modules.files.upload.invalidImageSize': 'Dimensions d\'image pas valable pour: %{imageName} ( %{imageWidth} x %{imageHeight} ) - Minimum: %{minW} x %{minH}',
		'modules.files.upload.type.SINGLE': 'image',
		'modules.files.upload.type.GALLERY': 'galerie',
		'modules.files.upload.caption.placeholder': 'Entrer la légende de l\'image',
	},
	it: {
		'modules.files.cloudinaryUpload.dropArea.images': 'Aggiungi un\'immagine (max. 20 MB) - Dimensione ottimale dell\'immagine: 1920 x 800 Pixel',
		'modules.files.cloudinaryUpload.dropArea.images.teamMember': 'Aggiungi un\'immagine (max. 20 MB)',
		'modules.files.cloudinaryUpload.dropArea.imagesMultiple': 'Aggiungi più foto o immagini (max. 20MB)',
		'modules.files.cloudinaryUpload.dropArea.documents': 'Aggiungi documenti',
		'modules.files.cloudinaryUpload.dropArea.spectraDocumentsApi': 'Aggiungi documenti',
		'modules.files.upload.invalidImageSize': 'Dimensioni immagine non valide: %{imageName} ( %{imageWidth} x %{imageHeight} ) - minimo: %{minW} x %{minH}',
		'modules.files.upload.type.SINGLE': 'immagine',
		'modules.files.upload.type.GALLERY': 'galleria',
		'modules.files.upload.caption.placeholder': 'Inserire didascalia immagine',
	},
	en: {
		'modules.files.cloudinaryUpload.dropArea.images': 'Upload an image (max. 20 MB) - Optimal image dimensions: 1920 x 800 Pixel',
		'modules.files.cloudinaryUpload.dropArea.images.teamMember': 'Upload an image (max. 20 MB)',
		'modules.files.cloudinaryUpload.dropArea.imagesMultiple': 'Upload one or more images (max. 20 MB)',
		'modules.files.cloudinaryUpload.dropArea.documents': 'Upload one or more documents.',
		'modules.files.cloudinaryUpload.dropArea.spectraDocumentsApi': 'Upload one or more documents.',
		'modules.files.upload.invalidImageSize':
			'Picture dimensions not valid: %{imageName} ( %{imageWidth} x %{imageHeight} ) - Minimum: %{minW} x %{minH}',
		'modules.files.upload.type.SINGLE': 'Image',
		'modules.files.upload.type.GALLERY': 'Gallery',
		'modules.files.upload.caption.placeholder': 'Enter caption',
	},
};
