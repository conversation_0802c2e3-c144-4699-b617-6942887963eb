/* @flow */
import { spread } from '../../../modules/i18n/util';
import translationPhrases from './translationPhrases.json';

import * as EditorModule from '../../../modules/editor/i18n';
import * as ModulesEditorToolbar from '../../../modules/editor-toolbar/i18n';
import * as EditorDraft from '../../../modules/editorDraft/i18n';

import * as ModulesModals from '../../amagCore/modules/article-modals/i18n';
import * as ModulesArticleEditor from '../../amagCore/modules/ArticleEditor/i18n';
import * as ModulesArticlePreview from '../../amagCore/modules/ArticlePreview/i18n';
import * as PublishDialog from '../../amagCore/modules/editorMenuDialogs/PublishDialog/i18n';
import * as ArchiveDialog from '../../amagCore/modules/editorMenuDialogs/ArchiveDialog/i18n';
import * as ResetDialog from '../../amagCore/modules/editorMenuDialogs/ResetDialog/i18n';
import * as ModulesDynamicMenu from '../../amagCore/modules/dynamicMenu/i18n';
import * as ModulesSupport from '../../amagCore/modules/Support/i18n';
import * as ModulesTeam from '../../amagCore/modules/team/i18n';
import * as ModulesBrands from '../../amagCore/modules/brands/i18n';
import * as ModulesSSO from '../../amagCore/modules/sso/i18n';
import * as ModulesForm from '../../amagCore/modules/form/i18n';
import * as ModulesSeo from '../../../modules/seo/i18n';
import * as ModulesEditorMenu from '../../../modules/editorMenu/i18n';
import * as ModulesLogin from '../../../modules/login/i18n';

import * as Navigation from '../modules/Navigation/i18n';
import * as Error404 from '../views/Error404/i18n';
import * as CurrentNews from '../views/CurrentNews/i18n';
import * as Service from '../views/Service/i18n';
import * as ServiceAppointmentForm from '../views/ServiceAppointmentForm/i18n';
import * as Footer from '../modules/Footer/i18n';
import * as HeroVideo from '../modules/hero-video/i18n';
import { cloudinaryPhrasesDefault } from '../assets/translations/modules';
import * as ArticleOverview from '../../amagCore/modules/admin/ArticleOverview/i18n';
import * as ArticleOverviewGenericPost from '../../amagCore/modules/admin/ArticleOverview/GenericPostContainer/i18n';
import * as ArticleOverviewControl from '../../amagCore/modules/admin/ArticleOverview/Control/i18n';
import { teamI18n } from '../views/Team/team.i18n';
import * as Posts from '../modules/posts/i18n';
import * as Buttons from '../modules/buttons/i18n';
import * as Navbar from '../modules/Navbar/i18n';
import * as Homepage from '../views/Homepage/i18n';
import * as LocalUmbrellaPages from '../views/UmbrellaPage/i18n';
import * as UmbrellaPages from '../../amagCore/modules/umbrellaPages/i18n';
import * as GarageFinder from '../../amagCore/modules/umbrellaPages/garage-finder/i18n';
import * as Impressum from '../views/Imprint/i18n';
import * as OurCompetence from '../views/OurCompetence/ourCompetence.i18n';
import { usedCarsI18n } from '../views/UsedCars/usedCars.i18n';
import * as UmrellaPageContact from '../../amagCore/modules/contact/i18n';

const phrases = spread([
	ModulesEditorToolbar,
	Navigation,
	EditorModule,
	EditorDraft,
	Error404,
	ModulesSSO,
	CurrentNews,
	Service,
	ServiceAppointmentForm,
	Footer,
	ArticleOverview,
	ArticleOverviewGenericPost,
	ArticleOverviewControl,
	HeroVideo,
	ModulesLogin,
	ModulesModals,
	ModulesArticleEditor,
	ModulesArticlePreview,
	PublishDialog,
	ArchiveDialog,
	ResetDialog,
	ModulesDynamicMenu,
	ModulesSupport,
	ModulesTeam,
	ModulesBrands,
	ModulesForm,
	ModulesSeo,
	ModulesEditorMenu,
	Buttons,
	Posts,
	teamI18n,
	Navbar,
	Homepage,
	LocalUmbrellaPages,
	UmbrellaPages,
	Impressum,
	usedCarsI18n,
	OurCompetence,
	UmrellaPageContact,
	GarageFinder,
]);

const translations = {
	de: {
		...phrases.de,
		...translationPhrases.de,
		...cloudinaryPhrasesDefault.de,
	},
	en: {
		...phrases.en,
		...cloudinaryPhrasesDefault.en,
	},
	fr: {
		...phrases.fr,
		...translationPhrases.fr,
		...cloudinaryPhrasesDefault.fr,
	},
	it: {
		...phrases.it,
		...translationPhrases.it,
		...cloudinaryPhrasesDefault.it,
	},
};

export default translations;
