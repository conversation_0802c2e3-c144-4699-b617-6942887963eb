@import theme(amagCoreTeam/teamMember);
@import '../../assets/variables.css';

.teamMember {
	left: 0px;
	box-sizing: border-box;
	flex-basis: 33%;
	margin-bottom: 20px;
	z-index: 4;
	position: relative;
	border-bottom: var(--amag-core-team-border-bottom, 1px solid #333);
	display: inline-block;
	float: none;
	vertical-align: top;
	background: #ffffff;

	flex: 1 0 calc(25% - 20px); /* Calculate width for four columns with gap */
	max-width: calc(25% - 20px); /* Maximum width for four columns with gap */
	margin-bottom: 20px; /* Ensure gap below the last row */
	border: 1px solid #e5e5e5;
	border-radius: 6px;
	@media(--screen-mobile){
		flex: 1; /* Calculate width for three columns with gap */
		max-width: 100%; /* Maximum width for three columns with gap */
	
	}

	& .buttonsBlock {
		position: absolute;
		width: 100%;
		height: 0;
		left: 0;
		background: rgba(55, 55, 55, .8);
		top: 0;
		display: flex;
		align-content: center;
		align-items: center;
		justify-content: center;
		overflow: hidden;
		z-index: 12;
		transition: .3s ease;
	}

	& button {
		height: auto;
		line-height: 1.4;
		margin-bottom: 6px;
		max-height: auto !important;
		max-width: 100%;
		padding: 10px 0;
		width: 100%;
		position: relative;

		&.editButton,
		&.deleteButton {
			border-radius: 20px;
			width: 40px;
			height: 40px;
			border: 1px solid var(--amag-core-team-edit-button-border-color);
			padding: 0;
			line-height: 40px;
			max-height: none;
			margin: 0 5px;
			font-weight: 300;

			& span {
				position: absolute;
				bottom: -20px;
				text-align: center;
				width: 100%;
				opacity: 0;
			}
		}

		&.editButton {
			background: var(--amag-core-team-edit-button-icon) no-repeat center center / 50%;
		}

		&.deleteButton {
			border-color: var(--amag-core-team-delete-button-border-color);
			background: var(--amag-core-team-delete-button-icon) no-repeat center center / 50%;

			&:hover & .deleteButton & span {
				opacity: 1;
			}
		}

		&:first-of-type {
			background-color: var(--amag-core-team-primary-color);
			box-shadow: none;
		}

		& span {
			padding: 0;
			margin: 0;
		}
	}
	@media (--screen-tablet) {
		flex-basis: 50%;

		& .buttonsBlock {
			height: 100%;
			background: none;
			align-items: flex-end;
			justify-content: flex-end;
			box-sizing: border-box;
			padding-bottom: 20px;
			padding-right: 20px;
			z-index: 1;

			&::after {
				height: 150px;
				position: absolute;
				bottom: 0;
				left: 0;
				display: block;
				width: 100%;
				content: '';
				z-index: -1;
				background-color: transparent;
			}
		}
	}
	@media (--screen-mobile) {
		flex-basis: 100%;
		margin: 0;

		& .buttonsBlock {
			height: 100%;
			background: none;
			align-items: flex-end;
			justify-content: flex-end;
			box-sizing: border-box;
			padding-bottom: 20px;
			padding-right: 20px;
			z-index: 1;

			&::after {
				height: 150px;
				position: absolute;
				bottom: 0;
				left: 0;
				display: block;
				width: 100%;
				content: '';
				z-index: -1;
				background-color: transparent;
			}
		}

		&:last-child {
			margin-bottom: 10px;
		}
	}

	& .image {
		/* padding-bottom: 125.6%; */
		background-position: top;
		background-size: cover;
		background-repeat: no-repeat;
		height: 350px;
		margin-bottom: 15px;
		position: relative;

		&.editable:hover .buttonsBlock {
			bottom: 0;
			height: 100%;
			@media (--screen-tablet) {
				bottom: inherit;
			}
		}
	}

	& img {
		width: 100%;
	}

	& h3 {
		all: initial;
		font-family: var(--amag-core-team-nameInputFontFamily);
		font-weight: bold;
		font-size: var(--amag-core-team-nameInputFontSize);
		line-height: var(--amag-core-team-nameInputLineHeight);
		width: 100%;
		display: inline-block;

		& span {
			font-size: var(--amag-core-team-nameInputFontSize);
			font-weight: normal;
		}
	}

	& .text {
		white-space: pre-wrap;
	}

	& > div > span {
		font-family: var(--amag-core-team-inputFontFamily);
		font-size: var(--amag-core-team-inputFontSize);
		line-height: var(--amag-core-team-inputLineHeight);
		display: block;


		& a {
			color: var(--amag-core-team-link-color);
			transition: all 0.3s ease;

			&:hover {
				color: var(--amag-core-team-link-color-hover);
			}
		}
	}

	& .textContainer {
		padding: 25px;
	}

	& h3 {
		font-size: 32px;
		font-style: normal;
		font-weight: 700;
		line-height: 38px;
	}

	& .title {
		display: block;
		color: #666;
		font-size: 20px;
		font-style: normal;
		font-weight: 300;
		line-height: 30px;
		margin-bottom: 15px;
	}

	& .emailPhone {
		display: block;
		font-size: 20px;
		font-style: normal;
		font-weight: 300;
		line-height: 30px;

		& svg {
			margin-right: 10px;
		}
	}

	@apply --amag-core-team-member-template;
}
