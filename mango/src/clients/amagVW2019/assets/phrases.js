/* @flow */
import { spread } from '../../../modules/i18n/util';
import translationPhrases from './translationPhrases.json';

import * as ModulesLogin from '../../../modules/login/i18n';
import * as ModulesEditorMenu from '../../../modules/editorMenu/i18n';
import * as ModulesSeo from '../../../modules/seo/i18n';
import * as ModulesAlias from '../../../modules/alias/i18n';
import * as ModulesEditorToolbar from '../../../modules/editor-toolbar/i18n';
import * as ModulesEditorDraft from '../../../modules/editorDraft/i18n';
import * as ModulesEditor from '../../../modules/editor/i18n';
import { cloudinaryPhrasesDefault } from '../assets/translations/modules';

import * as ModulesModals from '../../amagCore/modules/article-modals/i18n';
import * as ModulesArticleEditor from '../../amagCore/modules/ArticleEditor/i18n';
import * as ModulesArticlePreview from '../../amagCore/modules/ArticlePreview/i18n';
import * as PublishDialog from '../../amagCore/modules/editorMenuDialogs/PublishDialog/i18n';
import * as ArchiveDialog from '../../amagCore/modules/editorMenuDialogs/ArchiveDialog/i18n';
import * as ResetDialog from '../../amagCore/modules/editorMenuDialogs/ResetDialog/i18n';
import * as ModulesDynamicMenu from '../../amagCore/modules/dynamicMenu/i18n';
import * as ModulesSupport from '../../amagCore/modules/Support/i18n';
import * as ModulesTeam from '../../amagCore/modules/team/i18n';
import * as ModulesContact from '../../amagCore/modules/contact/i18n';
import * as ModulesBrands from '../../amagCore/modules/brands/i18n';
import * as ModulesGoogleMap from '../../amagCore/modules/googleMap/i18n';
import * as ModulesSSO from '../../amagCore/modules/sso/i18n';
import * as ModulesForm from '../../amagCore/modules/form/i18n';
import * as ModulesPostGrid from '../modules/PostGrid/i18n';
import * as ModulesNavigation from '../modules/Navigation/i18n';
import * as ModulesHeader from '../modules/Header/i18n';
import * as ModulesModelsOverview from '../modules/car-models-overview/i18n';
import * as ModulesElementsScrapedLink from '../modules/ScrapedLink/i18n';
import * as ArticleOverview from '../../amagCore/modules/admin/ArticleOverview/i18n';
import * as ArticleOverviewGenericPost from '../../amagCore/modules/admin/ArticleOverview/GenericPostContainer/i18n';
import * as ArticleOverviewControl from '../../amagCore/modules/admin/ArticleOverview/Control/i18n';
import * as Footer from '../../amagCore/modules/footer/i18n';
import * as UmbrellaPages from '../../amagCore/modules/umbrellaPages/i18n';
import * as GarageFinder from '../../amagCore/modules/umbrellaPages/garage-finder/i18n';
import * as ModulesFooter from '../modules/Footer/i18n';
import * as StaticPosts from '../modules/createStaticPosts';

import * as ModulesCookieDisclaimer from '../modules/CookieDisclaimer/i18n';

import * as ViewsContacts from '../views/Contacts/i18n';
import * as ViewsPromotions from '../views/Promotions/i18n';
import * as ViewWorthKnowing from '../views/WorthKnowing/i18n';
import * as ViewsService from '../views/Service/i18n';
import * as ViewsModelsOverview from '../views/CarModelsOverview/i18n';
import * as ViewsCarModel from '../views/CarModel/i18n';
import * as ViewsHome from '../views/Home/i18n';
import * as ViewsContactForm from '../views/ContactForm/i18n';
import * as ViewsServiceAppointmentForm from '../views/ServiceAppointmentForm/i18n';
import * as ViewsTeam from '../views/Team/i18n';
import * as LocalUmbrellaPages from '../views/UmbrellaPage/i18n';
import * as Imprint from '../views/Imprint/i18n';

const phrases = spread([
	ModulesEditorToolbar,
	ModulesLogin,
	ModulesSSO,
	ModulesContact,
	ModulesEditorMenu,
	ModulesSeo,
	ModulesAlias,
	ModulesEditorDraft,
	ModulesEditor,
	ModulesCookieDisclaimer,
	ModulesModals,
	ModulesArticleEditor,
	ModulesArticlePreview,
	PublishDialog,
	ArchiveDialog,
	ResetDialog,
	ModulesDynamicMenu,
	ModulesSupport,
	ModulesTeam,
	ModulesPostGrid,
	ModulesNavigation,
	ModulesHeader,
	ModulesBrands,
	ModulesGoogleMap,
	ModulesForm,
	ModulesElementsScrapedLink,
	ViewsContacts,
	ViewsPromotions,
	ViewsService,
	ViewWorthKnowing,
	ViewsModelsOverview,
	ViewsCarModel,
	ModulesFooter,
	ViewsHome,
	ViewsContactForm,
	ViewsServiceAppointmentForm,
	ViewsTeam,
	ModulesModelsOverview,
	ArticleOverview,
	ArticleOverviewGenericPost,
	ArticleOverviewControl,
	LocalUmbrellaPages,
	UmbrellaPages,
	Footer,
	Imprint,
	StaticPosts,
	GarageFinder,
]);

const translations = {
	de: {
		...phrases.de,
		...translationPhrases.de,
		...cloudinaryPhrasesDefault.de,
	},
	fr: {
		...phrases.fr,
		...translationPhrases.fr,
		...cloudinaryPhrasesDefault.fr,
	},
	it: {
		...phrases.it,
		...translationPhrases.it,
		...cloudinaryPhrasesDefault.it,
	},
};

export default translations;
