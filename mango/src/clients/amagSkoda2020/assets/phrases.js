/* @flow */
import { spread } from '../../../modules/i18n/util';
import translationPhrases from '../assets/translationPhrases.json';

import * as ModulesAlias from '../../../modules/alias/i18n';
import * as ModulesEditorMenu from '../../../modules/editorMenu/i18n';
import * as ModulesSeo from '../../../modules/seo/i18n';
import * as ModulesEditorToolbar from '../../../modules/editor-toolbar/i18n';
import * as ModulesEditorDraft from '../../../modules/editorDraft/i18n';
import * as ModulesModals from '../../amagCore/modules/article-modals/i18n';
import * as PublishDialog from '../../amagCore/modules/editorMenuDialogs/PublishDialog/i18n';
import * as ArchiveDialog from '../../amagCore/modules/editorMenuDialogs/ArchiveDialog/i18n';
import * as ResetDialog from '../../amagCore/modules/editorMenuDialogs/ResetDialog/i18n';
import * as ModulesDynamicMenu from '../../amagCore/modules/dynamicMenu/i18n';
import * as ModulesArticleEditor from '../../amagCore/modules/ArticleEditor/i18n';
import * as ModulesArticlePreview from '../../amagCore/modules/ArticlePreview/i18n';
import * as ModulesTeam from '../../amagCore/modules/team/i18n';
import * as ModulesSupport from '../../amagCore/modules/Support/i18n';
import * as ModulesContact from '../../amagCore/modules/contact/i18n';
import * as ModulesBrands from '../../amagCore/modules/brands/i18n';
import * as ModulesGoogleMap from '../../amagCore/modules/googleMap/i18n';
import * as ModulesForm from '../../amagCore/modules/form/i18n';
import * as ModulesLogin from '../../../modules/login/i18n';
import * as ModulesSSO from '../../amagCore/modules/sso/i18n';
import * as ModulesEditor from '../../../modules/editor/i18n';
import * as CoreFooter from '../../amagCore/modules/footer/i18n';

import * as Gallery from '../modules/elements/Gallery/i18n';
import * as LatestPromotions from '../modules/elements/LatestPromotions/i18n';
import * as TechnologySlideshow from '../modules/elements/TechnologySlideshow/i18n';

import * as Navigation from '../modules/Navigation/i18n';
import * as Footer from '../modules/Footer/i18n';
import * as NoResultsInfo from '../modules/NoResultsInfo/i18n';
import * as SkodaArticleHeader from '../modules/SkodaArticleHeader/i18n';
import * as CarHeader from '../modules/CarHeader/i18n';
import * as CarOverview from '../modules/CarOverview/i18n';
import * as MobileNavigation from '../modules/Navigation/MobileNavigation/i18n';
import * as Tiles from '../modules/Navigation/Tiles/i18n';
import * as Mosaic from '../modules/Mosaic/i18n';
import * as LatestOffers from '../modules/LatestOffers/i18n';
import * as SliderList from '../modules/SliderList/i18n';

import * as Error404 from '../views/Error404/i18n';
import * as Prospects from '../views/Prospects/i18n';
import * as Home from '../views/Home/i18n';
import * as AdminArticleList from '../views/Admin/ArticleList/i18n';
import * as Occasions from '../views/Occasions/i18n';
import * as StockCars from '../views/StockCars/i18n';
import * as Contact from '../views/Contact/i18n';
import * as ContactForm from '../views/ContactForm/i18n';
import * as ServiceAppointmentForm from '../views/ServiceAppointmentForm/i18n';
import * as Team from '../views/Team/i18n';
import * as Imprint from '../views/Imprint/i18n';
import * as CarModelsOverview from '../views/CarModelsOverview/i18n';


import { cloudinaryPhrasesDefault } from './translations/modules';
import * as ArticleOverview from '../../amagCore/modules/admin/ArticleOverview/i18n';
import * as ArticleOverviewGenericPost from '../../amagCore/modules/admin/ArticleOverview/GenericPostContainer/i18n';
import * as ArticleOverviewControl from '../../amagCore/modules/admin/ArticleOverview/Control/i18n';
import * as LocalUmbrellaPages from '../views/UmbrellaPage/i18n';
import * as UmbrellaPages from '../../amagCore/modules/umbrellaPages/i18n';
import * as GarageFinder from '../../amagCore/modules/umbrellaPages/garage-finder/i18n';

const phrases = spread([
	ModulesEditorToolbar,
	ModulesAlias,
	ModulesEditorMenu,
	ModulesSeo,
	ModulesModals,
	ModulesEditorDraft,
	PublishDialog,
	ArchiveDialog,
	ResetDialog,
	ModulesDynamicMenu,
	ModulesArticleEditor,
	ModulesArticlePreview,
	ModulesTeam,
	ModulesSupport,
	ModulesLogin,
	ModulesSSO,
	ModulesEditor,
	ModulesContact,
	ModulesBrands,
	ModulesGoogleMap,
	ModulesForm,
	Navigation,
	Footer,
	NoResultsInfo,
	SkodaArticleHeader,
	CarHeader,
	CarOverview,
	MobileNavigation,
	Tiles,
	Mosaic,
	LatestOffers,
	SliderList,
	Error404,
	Prospects,
	Home,
	Occasions,
	StockCars,
	Contact,
	ContactForm,
	ServiceAppointmentForm,
	Team,
	Imprint,
	CarModelsOverview,
	AdminArticleList,
	Gallery,
	LatestPromotions,
	TechnologySlideshow,
	ArticleOverview,
	ArticleOverviewGenericPost,
	ArticleOverviewControl,
	LocalUmbrellaPages,
	UmbrellaPages,
	GarageFinder,
	CoreFooter,
]);

const translations = {
	de: {
		...phrases.de,
		...translationPhrases.de,
		...cloudinaryPhrasesDefault.de,
	},
	fr: {
		...phrases.fr,
		...translationPhrases.fr,
		...cloudinaryPhrasesDefault.fr,
	},
	it: {
		...phrases.it,
		...translationPhrases.it,
		...cloudinaryPhrasesDefault.it,
	},
};

export default translations;
