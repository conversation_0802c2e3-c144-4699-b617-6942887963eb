import React, { useEffect, useRef } from 'react';
import { Helmet } from 'react-helmet-async';
import { connect } from 'react-redux';
import { getThirdPartyConfig } from '../config/selectors';
import { logger } from '../../tango/logger';
import { localize, type LocalizeProps } from '../i18n';

function loadWhenReady(callback) {
	if (window.friendlyChallenge) {
		callback();
	} else {
		setTimeout(() => loadWhenReady(callback), 300);
	}
}

type StartMode = 'focus' | 'auto' | 'none';

type Props = {
	onChange: Function,
	siteKey: string,
	isMobileApp: boolean,
	className?: string,
	startMode?: StartMode,
} & LocalizeProps;

function FriendlyCaptcha({
	siteKey,
	locale,
	className,
	onChange,
	isMobileApp,
	startMode = 'focus',
}: Props) {
	const container = useRef();
	const widget = useRef();

	useEffect(() => {
		let interval;
		const options = {
			doneCallback: solution => {
				logger.info('Captcha was solved. The form can be submitted.');
				logger.info(solution);
				onChange(solution);
			},
			errorCallback: err => {
				logger.error('There was an error when trying to solve the Captcha.');
				logger.error(err);
			},
		};
		if (!widget.current && container.current) {
			loadWhenReady(() => {
				widget.current = new window.friendlyChallenge.WidgetInstance(
					container.current,
					options,
				);
				if (startMode === 'none') {
					widget.current.start();
				}
				// needs to be overwritten repeatedly as the widget dom gets updated
				if (isMobileApp) {
					interval = setInterval(() => {
						const node = document.getElementsByClassName('frc-banner')[0]
							.childNodes[0];
						if (node) {
							node.href = '#doc=friendlyCaptcha';
							node.target = '_self';
						}
					}, 500);
				}
			});
		}
		return () => {
			if (interval) {
				clearInterval(interval);
			}
		};
	}, []);

	return (
		<div>
			<Helmet>
				<script
					type="module"
					src="https://unpkg.com/friendly-challenge@0.9.11/widget.module.min.js"
					async
					defer
				/>
				<script
					nomodule
					src="https://unpkg.com/friendly-challenge@0.9.11/widget.min.js"
					async
					defer
				/>
			</Helmet>
			<div
				ref={container}
				className={`frc-captcha ${className}`}
				data-sitekey={siteKey}
				data-start={startMode}
				data-lang={locale}
			/>
		</div>
	);
}

const mapStateToProps = (state: State) => {
	const config = getThirdPartyConfig(state);
	return {
		siteKey: config && config.getIn(['friendlyCaptcha', 'siteKey']),
	};
};

const withHocs = localize()(connect(mapStateToProps)(FriendlyCaptcha));

export { withHocs as FriendlyCaptcha };
