/* @flow */
import request from 'request';

const verifyFriendlyCaptcha = async (
	friendlyCaptcha: Object,
	recaptchaCode: string,
) => {
	try {
		const formData = {
			solution: recaptchaCode,
			siteKey: friendlyCaptcha.siteKey,
			secret: friendlyCaptcha.secret,
		};
		const result = await new Promise((resolve, reject) =>
			request.post('https://api.friendlycaptcha.com/api/v1/siteverify', { formData },
				(error, httpResponse, body) => {
					if (error) {
						reject({ httpResponse, error });
					}

					resolve({ httpResponse, body });
				},
			));
		const body = JSON.parse(result.body);

		return {
			active: true,
			verified: body.success,
		};
	} catch (e) {
		return {
			active: true,
			verified: false,
		};
	}
};

function getRecaptchaConfig(thirdParty, captchaType = 'v2') {
	if (captchaType === 'v3') {
		return (thirdParty && thirdParty.google && thirdParty.google.recaptchaV3);
	}
	return (thirdParty && thirdParty.google && thirdParty.google.recaptcha);
}

const verifyRecaptcha = async (
	req: Request,
	res: Response,
	recaptchaCode: string,
	captchaType?: string,
) => {
	const thirdParty = res.locals.context.app.thirdPartyConfig;
	const remoteip = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
	if (captchaType === 'friendly') {
		const friendlyCaptcha = (
			thirdParty && thirdParty.friendlyCaptcha && thirdParty.friendlyCaptcha
		);
		if (friendlyCaptcha && friendlyCaptcha.secret) {
			return verifyFriendlyCaptcha(friendlyCaptcha, recaptchaCode);
		}
	}
	const recaptchaConfig = getRecaptchaConfig(thirdParty, captchaType);
	if (recaptchaConfig && recaptchaConfig.secret) {
		const formData = {
			response: recaptchaCode,
			secret: recaptchaConfig.secret,
			remoteip,
		};

		try {
			const result = await new Promise((resolve, reject) =>
				request.post('https://www.google.com/recaptcha/api/siteverify', { formData },
					(error, httpResponse, body) => {
						if (error) {
							reject({ httpResponse, error });
						}
						resolve({ httpResponse, body });
					}));

			const body = JSON.parse(result.body);
			if (body.score && body.score < 0.5) {
				// eslint-disable-next-line no-console
				console.warn('Recaptcha score too low', result.body);
				return {
					active: true,
					verified: false,
				};
			}
			return {
				active: true,
				verified: body.success,
			};
		} catch (err) {
			return {
				active: true,
				verified: false,
			};
		}
	}
	return {
		active: false,
		verified: true,
	};
};

export { verifyRecaptcha };
