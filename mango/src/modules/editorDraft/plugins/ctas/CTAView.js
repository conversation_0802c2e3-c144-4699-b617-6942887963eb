/* @flow */
import React from 'react';
import classnames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { connect } from 'react-redux';
import { Map } from 'immutable';
import { localize } from '../../../../modules/i18n';
import combineHOCs from '../../../../tango/hoc/combineHOCs';
import withDealerBrandWebsiteConfig, {
	type DealerBrandWebsiteConfigProps,
} from '../../../../clients/amagCore/modules/withDealerBrandWebsiteConfig';
import { Link } from '../../../../tango/routing';
import ExternalLink from '../../../../modules/external-link';
import {
	DEFAULT_ALIGNMENT,
	LEFT,
	CENTER,
	RIGHT,
	OPTIONS,
	DEFAULT_STYLE,
	DEFAULT_AVAILABLE_STYLES,
} from './constants';
import { getBrand } from '../../../../clients/amagCore/modules/config/selectors';
import { namespace } from '../../i18n';
import {
	getDealerWebsiteTemplate,
	getDealerId,
	getDealerHasNewRetailerLayout,
} from '../../../../clients/amagCore/modules/DealerConfigProvider/selectors';
import { getGtmClass } from '../../../../clients/amagCore/modules/tracking/constants';
import { getParamsForBrandRoute } from './getParamsForBrandRoute';
import { useGetDealerRating } from '../../../../clients/amagCore/modules/dealer/useGetDealerRating';
import { ButtonArrow } from '../../../../clients/amagAudi/assets/svg';

import s from './ctaView.css';

type CTAViewPluginConfig = {
	classNames?: any,
	defaultStyle?: string,
};

type Props = {
	data: Object,
	key: number,
	config: ?CTAViewPluginConfig,
	testDriveUrl: ?string,
	damageReportUrl: ?string,
	serviceAppointmentUrl: ?string,
	webShopUrl: ?string,
	brand: string,
	locale: string,
	variables: Map<*, *>,
	t: Function,
	template: ?string,
	dealerId: ?string,
	allLocations: Array<any>,
	appointmentUrl: ?string,
	hasNewRetailerLayout?: boolean,
} & DealerBrandWebsiteConfigProps;

function CTAView(props: DealerBrandWebsiteConfigProps & Props) {
	const {
		data,
		key,
		config,
		serviceAppointmentUrl,
		appointmentUrl,
		testDriveUrl,
		usedCarsUrl,
		newCarsUrl,
		webShopUrl,
		damageReportUrl,
		brand,
		locale,
		variables = new Map(),
		t,
		template,
		dealerId,
		allLocations,
		hasNewRetailerLayout,
	} = props;
	const classNames = config ? config.classNames || {} : {};
	const defaultStyle = config
		? config.defaultStyle || DEFAULT_STYLE
		: DEFAULT_STYLE;
	const {
		label = '',
		link = OPTIONS[0],
		alignment = DEFAULT_ALIGNMENT,
		style = defaultStyle,
	} = data;

	const dealerRatingData = useGetDealerRating(dealerId);

	const availableStyles = DEFAULT_AVAILABLE_STYLES.map(
		availableStyle => availableStyle.value,
	);

	const finalStyle = availableStyles.includes(style) ? style : defaultStyle;

	const routeConfig = {
		locale,
		serviceAppointmentUrl,
		testDriveUrl,
		usedCarsUrl,
		newCarsUrl,
		webShopUrl,
		damageReportUrl,
		appointmentUrl,
	};

	const ctaUrl = variables.get('cta_url');
	const url =
		getParamsForBrandRoute(
			brand || template,
			link,
			routeConfig,
			dealerId,
			dealerRatingData,
			allLocations,
		) || (ctaUrl ? { to: ctaUrl, external: true, params: { locale } } : null);

	if (!url) {
		return null;
	}

	const { to, params, external } = url;
	const isDamageReportForm = to && to.includes('claim');

	const removeZeroesBeforePositive = number => {
		const convertToNumber = Number(number);

		if (convertToNumber >= 0) {
			return Number(convertToNumber.toString().replace(/^0+/, ''));
		}

		return number;
	};

	const getDamageReportLink = (brandName: string, dealerIdentity: string) => {
		if (brandName === 'cupra') {
			return `https://claim.cupraofficial.ch/${removeZeroesBeforePositive(
				dealerIdentity,
			)}`;
		}

		return `claim.${brandName}.ch/${removeZeroesBeforePositive(
			dealerIdentity,
		)}`;
	};

	const multiLocationDealer = allLocations.length > 1;

	const showArrowOnLinkBtn = brand === 'audi' && finalStyle === 'linkButton';

	if (hasNewRetailerLayout && isDamageReportForm) {
		const retailerLink = `https://amag.ec2.ch/?&lng=${locale}`;

		return (
			<div
				key={key}
				className={classnames(s.buttonContainer, classNames.buttonContainer, {
					[s.leftAlignment]: alignment === LEFT,
					[s.centerAlignment]: alignment === CENTER,
					[s.rightAlignment]: alignment === RIGHT,
				})}
			>
				<ExternalLink
					className={classnames(
						getGtmClass(link),
						s.button,
						s[finalStyle],
						classNames.button,
					)}
					href={retailerLink}
				>
					{label ||
						t(
							`cta.labels.${link.toLowerCase()}`,
							null,
							null,
							variables.get('cta_label'),
						)}
				</ExternalLink>
			</div>
		);
	}

	if (external) {
		if (isDamageReportForm) {
			return (
				<div
					className={multiLocationDealer ? s.multipleIconsContainer : undefined}
				>
					{allLocations.map(locationData => (
						<div
							key={locationData.customerNr}
							className={classnames(
								s.buttonContainer,
								classNames.buttonContainer,
								{
									[s.leftAlignment]: alignment === LEFT,
									[s.centerAlignment]: alignment === CENTER,
									[s.rightAlignment]: alignment === RIGHT,
								},
							)}
						>
							<ExternalLink
								className={classnames(
									getGtmClass(link),
									s.button,
									s[finalStyle],
									classNames.button,
								)}
								href={getDamageReportLink(brand, locationData.customerNr)}
							>
								{label ||
									t(
										`cta.labels.${link.toLowerCase()}`,
										null,
										null,
										variables.get('cta_label'),
									)}{' '}
								{multiLocationDealer &&
									`${locationData.street}, ${locationData.place}`}
							</ExternalLink>
						</div>
					))}
				</div>
			);
		}

		return (
			<div
				key={key}
				className={classnames(s.buttonContainer, classNames.buttonContainer, {
					[s.leftAlignment]: alignment === LEFT,
					[s.centerAlignment]: alignment === CENTER,
					[s.rightAlignment]: alignment === RIGHT,
				})}
			>
				<ExternalLink
					className={classnames(
						getGtmClass(link),
						s.button,
						s[finalStyle],
						classNames.button,
					)}
					href={to}
				>
					{label ||
						t(
							`cta.labels.${link.toLowerCase()}`,
							null,
							null,
							variables.get('cta_label'),
						)}
				</ExternalLink>
			</div>
		);
	}

	return (
		<div
			key={key}
			className={classnames(s.buttonContainer, classNames.buttonContainer, {
				[s.leftAlignment]: alignment === LEFT,
				[s.centerAlignment]: alignment === CENTER,
				[s.rightAlignment]: alignment === RIGHT,
			})}
		>
			<Link
				className={classnames(
					getGtmClass(link),
					s.button,
					s[finalStyle],
					classNames.button,
				)}
				to={to}
				locale={locale}
				params={params}
			>
				{label ||
					t(
						`cta.labels.${link.toLowerCase()}`,
						null,
						null,
						variables.get('cta_label'),
					)}

				{showArrowOnLinkBtn && (
					<ButtonArrow />
				)}
			</Link>
		</div>
	);
}

function mapStateToProps(state) {
	return {
		brand: getBrand(state),
		template: getDealerWebsiteTemplate(state),
		dealerId: getDealerId(state),
		hasNewRetailerLayout: getDealerHasNewRetailerLayout(state),
	};
}

export default combineHOCs([
	withDealerBrandWebsiteConfig,
	connect(mapStateToProps),
	localize(namespace),
	withStyles(s),
])(CTAView);
