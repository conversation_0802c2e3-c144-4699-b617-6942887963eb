# Mango

## Deployment

## Triggering deploy

- merging to `develop` will deploy to `test` and `dsttest` 
- merging to `master` will deploy to `prod` 
- merging to `preprod` will deploy to `preprod`
- merging to `pretest` will deploy to `pretest` 
- `dstprod` follow procedure here: https://deepimpactag.atlassian.net/wiki/spaces/Spectra/pages/12190074/Spectra+Grape-Mango+Infrastructure+Overview#Manual-Deploy-Procedure-(CH1%2FDST)

- to check the currently deployed version open `https://dstprod.spectra.io/healthcheck` in your browser
- add this to your `~/.ssh/config`

````
Host dsttest
  HostName chtest.dealestate.ch
  User deepimpact
  IdentityFile ~/.ssh/exoscale
Host dstprod
  HostName chprod.dealestate.ch
  User deepimpact
  IdentityFile ~/.ssh/exoscale
````

- get exoscale ssh keys from 1Password `Exoscale ssh keys` entry, unpack into .ssh directory
- run `ssh dstprod` to log into prod server
- `cd /home/<USER>/spectra/tools/dreamliner`
- `./scripts/spectradeploy dstprod`

### To deploy a custom version:
- env=dstprod tag=version you want to deploy

````script
  ansible-playbook ch-docker-pull-and-run.yml \
   -i inventory/hosts \
   --vault-password-file=".vault_pass_${env}.txt" \
   --extra-vars="dreamliner_env=${env} spectra_tag=${tag}"
   -vv
````
  

### Troubleshooting

- if you ever encounter a "no space left on disc" error in github actions you need to delete the cache in actions->caches->node-* 
- afterwards restart the stop from scratch, only re-running failed jobs will not work

## Index

- [Files](./src/modules/files/README.md)
- [Posts](./src/modules/postGrid/README.md)

## Tennant Specific Setups

- [amagAdmin](./src/clients/amagAdmin/README.md)
- [amagAudi](./src/clients/amagAudi/README.md)
- [amagCupra](./src/clients/amagCupra/README.md)
- [amagRetailer](./src/clients/amagRetailer/README.md)
- [amagSeasonalCampaigns](./src/clients/amagSeasonalCampaigns/README.md)
- [amagSeat2019](./src/clients/amagSeat2019/README.md)
- [amagSkoda2020](./src/clients/amagSkoda2020/README.md)
- [amagVW2019](./src/clients/amagVW2019/README.md)
- [dealerSites](./src/clients/dealerSites/README.md) => these are called `Dachseiten` by AMAG
- [dealEstate](./src/clients/dealEstate/README.md)
- [kartause](./src/clients/kartause/README.md)
- [mobileid19](./src/clients/mobileid19/README.md)

## Dev

### Setup Hosts File

Since spectra is a multi tenant system it uses the domain to determine the site to display. Add the following entries to your `hosts` file:

<details>
  <summary>Long list of localhost hostnames - Click to expand!</summary>

```ini
# AMAG specific domains
127.0.0.1       amag.spectra.local
127.0.0.1       amagadmin.spectra.local
127.0.0.1       amagauth.spectra.local
127.0.0.1       amagseasonalcampaigns.spectra.local

# AMAG retail (deprecated)
127.0.0.1       amag-autowelt.spectra.local
127.0.0.1       amag-amag-oftringen.spectra.local
127.0.0.1       amag-amag-schinznach.spectra.local
127.0.0.1       amag-von-kaenel.spectra.local
127.0.0.1       amag-amag-schaffhausen.spectra.local
127.0.0.1       amag-baschnagel.spectra.local
127.0.0.1       amag-autorama.spectra.local
127.0.0.1       amag-amag-winterthur.spectra.local
127.0.0.1       amag-gng.spectra.local
127.0.0.1       amag-ticino.spectra.local

# AMAG Brand pages
127.0.0.1       skoda2020.spectra.local
127.0.0.1       volkswagen2019.spectra.local
127.0.0.1       volkswagen-nutzfahrzeuge2019.spectra.local
127.0.0.1       audi.spectra.local
127.0.0.1       cupra.spectra.local
127.0.0.1       seat.spectra.local

# AMAG Dealer sites - template version (which backend environment)
127.0.0.1       amag-auto-suter-kuessnacht.spectra.local # v3 (PROD)
127.0.0.1       amag-autobolli.spectra.local # v3 (PROD)
127.0.0.1       amag-autorama.spectra.local # v3
127.0.0.1       amag-baschnagel.spectra.local # v3 (TEST)
127.0.0.1       amag-christen-automobile.spectra.local # v3
127.0.0.1       amag-dileomotors.spectra.local # v3 (PROD)
127.0.0.1       amag-garage-balmer.spectra.local # v2 (PROD)
127.0.0.1       amag-garage-gianella.spectra.local # v3 (PROD)
127.0.0.1       amag-garage-johann-frei.spectra.local # v3 (PROD)
127.0.0.1       amag-garage-mustermann.spectra.local # v3B (TEST) v3 (PROD)
127.0.0.1       amag-garage-remo-borer.spectra.local # (PROD)
127.0.0.1       amag-garagedunord.spectra.local # v3 (PROD)
127.0.0.1       amag-garagekarpf.spectra.local # DE FR IT v3 (PROD)
127.0.0.1       amag-garageolympic.spectra.local # v3B (TEST) v3 (PROD)
127.0.0.1       amag-gng.spectra.local # v3 (PROD)
127.0.0.1       amag-gschwend-garage.spectra.local # v1 (TEST), v3 (PROD)
127.0.0.1       amag-kocher-erlach.spectra.local # DE FR v3 (PROD)
127.0.0.1       amag-pp-autotreff.spectra.local # v3 (PROD)
127.0.0.1       amag-regli.spectra.local # v3
127.0.0.1       amag-rhauto.spectra.local # v3
127.0.0.1       amag-rutishauser-online.spectra.local # v2 (PROD)
127.0.0.1       amag-schweizer.spectra.local # v3 (PROD)
127.0.0.1       amag-sennautos.spectra.local #v2
127.0.0.1       amag-strickler.spectra.local # v1 (PROD)
127.0.0.1       amag-garage-wicki.spectra.local # v1 (TEST) v3 (PROD)
127.0.0.1       amag-von-kaenel.spectra.local # v1 (TEST)
127.0.0.1       amag-von-rotz-automobile.spectra.local # v3 (PROD)

# Other domains
127.0.0.1       di.spectra.local
127.0.0.1       mobimo.spectra.local
127.0.0.1       dealestate.spectra.local
127.0.0.1       hpn-ch.spectra.local
127.0.0.1       hpn-sge.spectra.local
127.0.0.1       hpn-net.spectra.local
127.0.0.1       stadttheater-langenthal.spectra.local
127.0.0.1       mobileid.spectra.local
127.0.0.1       mobileid19.spectra.local
127.0.0.1       kartause.spectra.local
127.0.0.1       ll.spectra.local
```
</details>

### Override Hostname

Sometimes it's necessary to test your application on `localhost` (eg. service worker) and not use any custom hostname. In this case you can override the hostname statically with the environment variable `HOST_OVERRIDE`:

```sh
HOST_OVERRIDE=mobileid19.spectra.local npm run dev mobileid19
HOST_OVERRIDE=mobimo.spectra.local npm run dev mobimo # must configure .env to CH1PROD or CH1TEST
HOST_OVERRIDE=dealestate.spectra.local npm run dev dealEstate # must connect environment to DSTTEST, DSTPROD
HOST_OVERRIDE=amagauth.spectra.local npm run dev amagAuthentication
HOST_OVERRIDE=cupra.spectra.local npm run dev amagCupra
HOST_OVERRIDE=volkswagen2019.spectra.local npm run dev amagVW2019
HOST_OVERRIDE=volkswagen-nutzfahrzeuge2019.spectra.local npm run dev amagVW2019
HOST_OVERRIDE=audi.spectra.local npm run dev amagAudi
HOST_OVERRIDE=seat.spectra.local npm run dev amagSeat2019
HOST_OVERRIDE=skoda2020.spectra.local npm run dev amagSkoda2020
HOST_OVERRIDE=kartause.spectra.local npm run dev kartause
HOST_OVERRIDE=stadttheater-langenthal.spectra.local npm run dev stadttheaterLangenthal
```

### Development

To start the development server just run the `dev` task.

1. `code .env` or `open .env`
1. Copy and paste in "Spectra Mango .env" from LastPass to `.env` and save it in the "mango" folder.
1. Setup localhost name mappings based on the "Setup Hosts File" secion of the repo [README.md](../README.md#setup-hosts-file)
1. `npm run dev {tenantName}` eg. `npm run dev amag`

This will start the development server. The server is based on webpack and supports hot reloading which enables hot swapping of code without reloading the page.

## Build

Mango tenants are built with the following command:
```sh
npm run build {tenantName}
npm run build mobileid19 # will build mobileid only
npm run build mobileid19 amag # will build mobileid and amag
```

### Build Analysis & Debugging

Optionally the `debug` mode can be enabled by passing `-- -D` to the build command.
This will analyse the bundle size for the tenant assets.

```sh
npm run build mobileid19 -- -D # will run build in debug mode
```

The resulting report can be found in `build/clients/{tenantName}/assets/report.html`.

## Debugging

Sometimes you need to debug during rendering and want to disable SSR. This can be done easily by creating a cookie with the name `spectraDebug` and value `true`.

Here are some handy bookmarklets that do it for you:

### Enable Debug Mode
```js
javascript: (function () {
  document.cookie = 'spectraDebug=true'
}());
```

### Disable Debug Mode
```js
javascript: (function () {
  document.cookie = 'spectraDebug=false'
}());
```

### Atom

The packages that you need to install are:

- atom-beautify
- atom-handlebars
- autoclose-html
- autocomplete-modules
- busy-signal
- emmet
- highlight-line
- highlight-selected
- hyperclick
- intentions
- js-hyperclick
- js-refactor
- language-babel
- linter
- linter-eslint
- linter-flow
- linter-ui-default
- project-manager
- refactor
- set-syntax
- tool-bar

Note: Set `./node_modules/.bin/flow` as the path for flow executable in `linter-flow`.

### Visual Studio Code (VSCode)

The plugins that you need to install are:

- [Flow Language Support](https://marketplace.visualstudio.com/items?itemName=flowtype.flow-for-vscode) TIP: downgrade to VScode extension to **v0.8.5** to make work with Mango's old version of Flow, by clicking on the gear/settings icon of the Flow extension is in the Extensions panel
- [Eslint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint)
- [Stylelint](https://marketplace.visualstudio.com/items?itemName=stylelint.vscode-stylelint)
- (_optional_) [PostCSS syntax highlighting](https://marketplace.visualstudio.com/items?itemName=csstools.postcss)

Your `settings.json` should be:

```js
// Place your settings in this file to overwrite the default settings
{
    "javascript.validate.enable": false,
    "flow.useNPMPackagedFlow": true
}
```

## Resources

### Hardware
- [Spectra Infrastructure Overview](https://www.notion.so/deepimpact/Spectra-Infrastructure-Overview-3ccf9252b8f8442985049c21005eeb17)
- [How to build and deploy Spectra](https://www.notion.so/deepimpact/How-to-build-and-deploy-Spectra-75ce16924e3f4b92bbe09ce8e2985e3e)

## AMAG
- [Client URLs and mappings](https://deepimpactag.atlassian.net/wiki/spaces/AMAG/pages/15106246/AMAG+General)


## TENANT REMOVAL

See example here: https://github.com/DEEP-IMPACT-AG/spectra/pull/9824/files

## Errors and how to fix them

### npm run dev:*** errors out with error -86

- first encounterd on M3 Macbook Air

```
node:internal/child_process:414
    throw errnoException(err, 'spawn');
    ^

Error: spawn Unknown system error -86
```

- run `softwareupdate --install-rosetta`
- looks like some very old libs do not exist in m1/2/3 versions and require this

## DEPLOY TRIGGER
- trigger v4