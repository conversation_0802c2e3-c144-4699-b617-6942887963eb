/* @flow */

// Content Security Policy common sources
const doubleclickSrcs = ['https://*.doubleclick.net'];

const googleSrcs = [
	...doubleclickSrcs,
	'https://*.googleapis.com',
	'https://*.google.com',
	'https://*.gstatic.com',
	'https://*.google-analytics.com',
	'https://*.googleusercontent.com',
	'https://www.googletagmanager.com',
];

const commonImgSrcs = ['https://*.gravatar.com', 'https://*.wp.com'];

const cloudinarySrcs = ['*.cloudinary.com'];

const auth0Srcs = ['*.auth0.com'];

const marketoSrcs = ['http://munchkin.marketo.net', 'http://159-txx-817.mktoresp.com'];


const facebookSrcs = [
	'https://connect.facebook.net',
	'https://www.facebook.com',
];

const sizmekSources = [
	'https://bs.serving-sys.com',
	'https://secure-ds.serving-sys.com',
];

const oneTrustSources = [
	'https://cdn.cookielaw.org',
	'https://*.onetrust.com',
	'https://optanon.blob.core.windows.net',
];

const hotjarSrcs = ['http://*.hotjar.io', '*.hotjar.com'];

const twilioSrcs = ['media.twiliocdn.com', 'https://*.twilio.com'];

const dialogFlowSrcs = ['https://api.api.ai'];

const magictour360Srcs = ['https://www.360magictour.com'];

const linkedIn = ['https://dc.ads.linkedin.com'];

const typekit = ['https://*.typekit.net'];

const fontsNet = ['https://*.fonts.com', 'https://*.fonts.net'];

const cookieBotScriptSrcs = [
	'https://consent.cookiebot.com',
	'https://*.cookiebot.com',
	'https://cookie-script.com',
	'*.cookie-script.com',
];

const youtubeSrcs = [
	'https://www.youtube.com',
	'https://www.youtube-nocookie.com',
];

export {
	doubleclickSrcs,
	googleSrcs,
	typekit,
	fontsNet,
	commonImgSrcs,
	cloudinarySrcs,
	auth0Srcs,
	marketoSrcs,
	facebookSrcs,
	hotjarSrcs,
	twilioSrcs,
	dialogFlowSrcs,
	magictour360Srcs,
	linkedIn,
	sizmekSources,
	oneTrustSources,
	cookieBotScriptSrcs,
	youtubeSrcs,
};
