// Application Configuration.
//
// Please see the /docs/APPLICATION_CONFIG.md documentation for more info.
//
// Note: all file/folder paths should be relative to the project root. The
// absolute paths should be resolved during runtime by our build tools/server.
import { getStringEnvVar, getIntEnvVar, getBoolVar } from './internals/environmentVars';
import { amagVW2019 } from './apps/amag/amagVW2019';
import { kartause } from './apps/kartause';
import { mobileid19 } from './apps/mobileid19';
import { stadttheaterLangenthal } from './apps/stadtheaterLangenthal';
import { amagAuthentication } from './apps/amag/amagAuthentication';
import { amagAdmin } from './apps/amag/amagAdmin';
import { amagSeat2019 } from './apps/amag/amagSeat2019';
import { amagAudi } from './apps/amag/amagAudi';
import { dealerSites } from './apps/amag/dealerSites';
import { dealEstate } from './apps/dealEstate';
import { mobimo } from './apps/mobimo';
import { amagCupra } from './apps/amag/amagCupra';
import { amagSeasonalCampaigns } from './apps/amag/amagSeasonalCampaigns';
import { amagSkoda2020 } from './apps/amag/amagSkoda2020';
import { amagStopGo } from './apps/amag/amagStopGo';

// This protects us from accidentally including this configuration in our
// client bundle. That would be a big NO NO to do. :)
if (process.env.IS_CLIENT) {
	throw new Error("You shouldn't be importing the `./config` directly into your 'client' or 'shared' source as the configuration object will get included in your client bundle. Not a safe move! Instead, use the `safeConfigGet` helper function (located at `./src/shared/utils/config`) within the 'client' or 'shared' source files to reference configuration values in a safe manner.");
}

const isNode = process.env.IS_NODE;
function getBlackUrl(host, ssl) {
	/* istanbul ignore next */
	if (isNode) {
		return ssl ? `https://${host}` : `http://${host}`;
	}
	return '';
}

const blackHost = getStringEnvVar('BLACK_HOST', 'localhost:3000');
const cambridgeHost = getStringEnvVar('CAMBRIDGE_HOST', 'cambridge-qa.spectra-dev.io');
const cambridgeSsl = getBoolVar('CAMBRIDGE_SSL', true);
const blackSsl = getBoolVar('BLACK_SSL', false);
const blackUrl = getBlackUrl(blackSsl, blackHost);
const pdfServiceUrl = getStringEnvVar('PDF_SERVICE_URL', '');
const pdfServiceToken = getStringEnvVar('PDF_SERVICE_TOKEN', '');
const muninHost = getStringEnvVar('MUNIN_HOST', 'munin.s.spectra.io');
const muninSsl = getBoolVar('MUNIN_SSL', true);

const config = {
	// The host on which the server should run.
	host: getStringEnvVar('SERVER_HOST', 'localhost'),

	// The port on which the server should run.
	port: getIntEnvVar('SERVER_PORT', 8080),

	debugPort: getIntEnvVar('DEBUG_PORT'),

	// The port on which the client bundle development server should run.
	clientDevServerPort: getIntEnvVar('CLIENT_DEVSERVER_PORT', 7331),

	// This is an example environment variable which is consumed within the
	// './client.js' config.  See there for more details.
	welcomeMessage: getStringEnvVar('WELCOME_MSG', 'Hello world!'),

	// How long should we set the browser cache for the served assets?
	// Don't worry, we add hashes to the files, so if they change the new files
	// will be served to browsers.
	// We are using the "ms" format to set the length.
	// @see https://www.npmjs.com/package/ms
	browserCacheMaxAge: getStringEnvVar('CLIENT_BUNDLE_CACHE_MAXAGE', '365d'),

	// Path to the public assets that will be served off the root of the
	// HTTP server.
	publicAssetsPath: './public',

	// Where does our build output live?
	buildOutputPath: getStringEnvVar('BUNDLE_OUTPUT_PATH', './build'),

	// Should we optimize production builds (i.e. minify etc).
	// Sometimes you don't want this to happen to aid in debugging complex
	// problems.  Having this configuration flag here allows you to quickly
	// toggle the feature.
	optimizeProductionBuilds: true,

	// Do you want to included source maps (will be served as seperate files)
	// for production builds?
	includeSourceMapsForProductionBuilds: false,

	// These extensions are tried when resolving src files for our bundles..
	bundleSrcTypes: ['js', 'jsx', 'json'],

	// Additional asset types to be supported for our bundles.
	// i.e. you can import the following file types within your source and the
	// webpack bundling process will bundle them with your source and create
	// URLs for them that can be resolved at runtime.
	bundleAssetTypes: [
		'jpg',
		'jpeg',
		'png',
		'gif',
		'ico',
		'eot',
		'svg',
		'ttf',
		'woff',
		'woff2',
		'otf',
	],

	// What should we name the json output file that webpack generates
	// containing details of all output files for a bundle?
	bundleAssetsFileName: getStringEnvVar('BUNDLE_ASSETS_FILENAME', 'assets.json'),

	// node_modules are not included in any bundles that target "node" as a runtime
	// (i.e. the server bundle).
	// The node_modules may however contain files that will need to be processed by
	// one of our webpack loaders.
	// Add any required file types to the list below.
	nodeBundlesIncludeNodeModuleFileTypes: [
		/\.(eot|woff|woff2|ttf|otf)$/,
		/\.(svg|png|jpg|jpeg|gif|ico)$/,
		/\.(mp4|mp3|ogg|swf|webp)$/,
		/\.(css|scss|sass|sss|less)$/,
	],

	blackHost,
	blackSsl,
	blackUrl,

	cambridgeHost,
	cambridgeSsl,

	pdfServiceUrl,
	pdfServiceToken,

	muninHost,
	muninSsl,

	srcPath: './src',
	webPath: getStringEnvVar('CLIENT_BUNDLE_HTTP_PATH', '/assets/'),

	devVendorDLL: {
		enabled: true,
		include: [
			'auth0-js',
			'axios',
			'babel-polyfill',
			'babel-template',
			'classnames',
			'cloudinary-core',
			'google-map-react',
			'react-slick',
			'draft-js',
			'draft-js-focus-plugin',
			'draft-js-plugins-editor',
			'express-prometheus-middleware',
			'hoist-non-react-statics',
			'immutable',
			'isomorphic-fetch',
			'isomorphic-style-loader',
			'jwt-decode',
			'lodash',
			'moment',
			'node-polyglot',
			'path',
			'rc-progress',
			'react',
			'react-autosuggest',
			'react-datepicker',
			'react-dd-menu',
			'react-dnd',
			'react-dnd-html5-backend',
			'react-dom',
			'react-dropzone',
			'react-helmet',
			'react-image-crop',
			'react-json-pretty',
			'react-modal',
			'react-motion',
			'react-redux',
			'react-router-dom',
			'react-scroll',
			'react-social',
			'redraft',
			'redux',
			'redux-form',
			'redux-immutable',
			'redux-observable',
			'reselect',
			'rxjs',
			'serialize-javascript',
			'slick-carousel',
			'source-map-support',
			'transit-immutable-js',
			'transit-js',
			'uuid',
			'xml2js',
		],
		name: 'vendor',
	},
	bundles: {
		amagVW2019,
		amagAuthentication,
		amagAdmin,
		amagSeat2019,
		amagAudi,
		amagCupra,
		amagSeasonalCampaigns,
		amagSkoda2020,
		dealerSites,
		dealEstate,
		stadttheaterLangenthal,
		mobileid19,
		kartause,
		mobimo,
		amagStopGo,
		server: {
			srcEntryFile: './src/server/index.js',
			outputPath: './build/server',
		},
		cluster: {
			srcEntryFile: './src/server/cluster.js',
			outputPath: './build/server',
		},
	},
	archivedBundles: {},

	// These plugin definitions provide you with advanced hooks into customising
	// the project without having to reach into the internals of the tools.
	//
	// We have decided to create this plugin approach so that you can come to
	// a centralised configuration folder to do most of your application
	// configuration adjustments.  Additionally it helps to make merging
	// from the origin starter kit a bit easier.
	plugins: {
		// This plugin allows you to provide final adjustments your babel
		// configurations for each bundle before they get processed.
		//
		// This function will be called once for each for your bundles.  It will be
		// provided the current webpack config, as well as the buildOptions which
		// detail which bundle and mode is being targetted for the current function run.
		babelConfig: (babelConfig, buildOptions) => {
			// eslint-disable-next-line no-unused-vars
			const { target, mode } = buildOptions;
			// Example
			/*
				if (target === 'server' && mode === 'development') {
					babelConfig.presets.push('foo');
				}
			 */

			return babelConfig;
		},

		// This plugin allows you to provide final adjustments your webpack
		// configurations for each bundle before they get processed.
		//
		// I would recommend looking at the "webpack-merge" module to help you with
		// merging modifications to each config.
		//
		// This function will be called once for each for your bundles.  It will be
		// provided the current webpack config, as well as the buildOptions which
		// detail which bundle and mode is being targetted for the current function run.
		webpackConfig: (webpackConfig, buildOptions) => {
			// eslint-disable-next-line no-unused-vars
			const { target, mode } = buildOptions;

			// Example:
			/*
				if (target === 'server' && mode === 'development') {
					webpackConfig.plugins.push(new MyCoolWebpackPlugin());
				}
			 */

			// Debugging/Logging Example:
			/*
				if (target === 'server') {
					console.log(JSON.stringify(webpackConfig, null, 4));
				}
			 */

			return webpackConfig;
		},
	},
};

// Export the main config as the default export.
export default config;
