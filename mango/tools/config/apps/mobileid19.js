/* @flow */
import { cloudinarySrcs, hotjarSrcs, auth0Srcs, youtubeSrcs } from '../sources';

export const mobileid19 = {
	app: {
		srcEntryFile: './src/clients/mobileid19/middleware/index.js',
		outputPath: './build/mobileid19/app',
		themePath: './src/clients/mobileid19/assets',
	},
	assets: {
		srcEntryFile: './src/clients/mobileid19/index.js',
		outputPath: './build/mobileid19/assets',
		themePath: './src/clients/mobileid19/assets',
		cspConfig: {
			connectSrc: [
				...cloudinarySrcs,
				...auth0Srcs,
				...hotjarSrcs,
				'https://api.friendlycaptcha.com',
			],
			imgSrc: [
				...cloudinarySrcs,
				'https://api.mid-dev.futurae.ch',
				'https://api.mid.futurae.com',
			],
			scriptSrc: [
				...hotjarSrcs,
				'https://www.google.com/recaptcha/api.js',
				'https://www.gstatic.com/recaptcha/',
				'https://www.googletagmanager.com',
				'https://unpkg.com',
				'\'unsafe-eval\'',
			],
			workerSrc: [
				'blob:',
			],
			styleSrc: ['https://fonts.googleapis.com'],
			fontSrc: ['https://fonts.gstatic.com'],
			frameSrc: [
				'https://www.google.com',
				...youtubeSrcs,
				...hotjarSrcs,
			],
			objectSrc: [],
			mediaSrc: [...cloudinarySrcs],
		},
	},
};
