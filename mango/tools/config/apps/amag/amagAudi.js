
/* @flow */
import {
	cloudinarySrcs,
	googleSrcs,
	facebookSrcs,
	sizmekSources,
	oneTrustSources,
	youtubeSrcs,
	marketoSrcs,
} from '../../sources';
import {
	amagAuth0Srcs,
	amagImgSrcs,
	livechatScriptSrcs,
	audiSrcs,
	frameSrcs,
	ppAutotreffSrcs,
	ppAutotreffVimeoSrcs,
	logRocketSrcs,
	VWOSrcs,
} from './sources';

export const amagAudi = {
	app: {
		srcEntryFile: './src/clients/amagAudi/middleware/index.js',
		outputPath: './build/amagAudi/app',
		themePath: './src/clients/amagAudi/assets',
	},
	assets: {
		srcEntryFile: './src/clients/amagAudi/index.js',
		outputPath: './build/amagAudi/assets',
		themePath: './src/clients/amagAudi/assets',
		cspConfig: {
			childSrc: [
				'blob:', // needed for LogRocket
			],
			connectSrc: [
				...googleSrcs,
				...cloudinarySrcs,
				...amagAuth0Srcs,
				...facebookSrcs,
				...ppAutotreffSrcs,
				...sizmekSources,
				...oneTrustSources,
				'https://hooks.slack.com', // specifically *****************************************************************************',
				...logRocketSrcs,
				...VWOSrcs.connectSrc,
			],
			imgSrc: [
				...oneTrustSources,
				...googleSrcs,
				...cloudinarySrcs,
				...amagImgSrcs,
				...facebookSrcs,
				...livechatScriptSrcs,
				...audiSrcs,
				...ppAutotreffSrcs,
				...VWOSrcs.imgSrc,
				'https://tinyurl.com',
			],
			scriptSrc: [
				...googleSrcs,
				...livechatScriptSrcs,
				...facebookSrcs,
				...ppAutotreffSrcs,
				...ppAutotreffVimeoSrcs,
				...sizmekSources,
				...oneTrustSources,
				...logRocketSrcs,
				...VWOSrcs.scriptSrc,
				...marketoSrcs,
			],
			styleSrc: [
				...googleSrcs, // roboto font for google map
				'fast.fonts.net',
				...livechatScriptSrcs,
				...ppAutotreffSrcs,
				...VWOSrcs.styleSrc,
				...marketoSrcs,
			],
			frameSrc: [
				'forms.seat.ch',
				'https://www.google.com',
				...youtubeSrcs,
				'https://ad.autoricardo.ch',
				'http://ad.autoricardo.ch',
				'https://audi.ch',
				'http://audi.ch',
				'https://www.audi.ch',
				'http://www.audi.ch',
				...facebookSrcs,
				...livechatScriptSrcs,
				...frameSrcs,
				...googleSrcs,
				...VWOSrcs.frameSrc,
				...marketoSrcs,
			],
			fontSrc: [
				...googleSrcs, // roboto font for google map
			],
			mediaSrc: [
				...livechatScriptSrcs,
				...cloudinarySrcs,
				...audiSrcs,
			],
			workerSrc: [
				...VWOSrcs.workerSrc,
			],
		},
	},
};
