import workerFarm from 'worker-farm';

// this build opens many files, so make sure we do not encounter EMFILE error
const realFs = require('fs');
const gracefulFs = require('graceful-fs');
gracefulFs.gracefulify(realFs);

// we create at least one worker for the server
let workerCount = 1;
let workerDoneCount = 0;

let clients = [
	'amagVW2019',
	'amagAdmin',
	'amagAuthentication',
	'amagSeat2019',
	'amagCupra',
	'amagSkoda2020',
	'amagAudi',
	'amagSeasonalCampaigns',
	'dealerSites',
	'dealEstate',
	'kartause',
	'mobileid19',
	'stadttheaterLangenthal',
	'mobimo',
	'amagStopGo',
];

const debug = process.argv.includes('-D');
if (debug) {
	console.log('DEBUG mode is ON');
}

if (process.argv.length > 2) {
	clients = process.argv.slice(2).filter(arg => !arg.startsWith('-'));
}

const clientsBasedWorkerCount = (clients.length * 2) + 1; // 2 workers per client and the server
const cpuCount = Math.floor(require('os').cpus().length);
const workerNo = Math.min(clientsBasedWorkerCount, cpuCount);

console.log('Working with ', workerNo, ' worker processes.');

const workerOptions = {
	maxCallsPerWorker: Infinity,
	maxConcurrentWorkers: workerNo,
	maxConcurrentCallsPerWorker: 2,
	maxConcurrentCalls: Infinity,
	maxCallTime: Infinity,
	maxRetries: Infinity,
	autoStart: false,
};

const workers = workerFarm(workerOptions, require.resolve('../webpack/BundleWorker'), ['createBundle']);


function workerReturn(err, outp) {
	if (err) {
		console.log(err);
	}
	if (outp) {
		console.log(outp);
	}
	workerDoneCount += 1;
	console.log(`${workerDoneCount}/${workerCount} workers completed`);
	if (workerDoneCount === workerCount) {
		console.log('Stopping the workers farm...');
		workerFarm.end(workers);
		console.log('Workers farm stopped.');
	}
}

function createClient(client) {
	workerCount += 2;
	workers.createBundle({ target: 'assets', client, mode: 'production', debug }, workerReturn);
	workers.createBundle({ target: 'app', client, mode: 'production', debug }, workerReturn);
}

clients.map(createClient);
workers.createBundle({ target: 'cluster', client: '', mode: 'production', debug }, workerReturn);
