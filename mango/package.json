{"name": "mango", "version": "0.9.5", "description": "The old version of Spectra", "repository": {"type": "git", "url": "**************:DEEP-IMPACT-AG/spectra.git"}, "private": true, "license": "UNLICENSED", "main": "build/server/index.js", "engines": {"node": "10"}, "scripts": {"dev": "babel-node --trace-warnings tools/development", "grape": "cd ../grape && lein with-profile dev run local", "grape:t": "cd ../grape && lein with-profile dev run local --no-basedata --seed-tenants", "grape:db": "cd ../grape && lein run server", "black": "npm run grape", "dev:amagadmin": "HOST_OVERRIDE=amagadmin.spectra.local npm run dev amagAdmin", "dev:amagauth": "HOST_OVERRIDE=amagauth.spectra.local npm run dev amagAuthentication", "dev:amagaudi": "HOST_OVERRIDE=audi.spectra.local npm run dev amagAudi", "dev:amagcupra": "HOST_OVERRIDE=cupra.spectra.local npm run dev amagCupra", "dev:amagseat": "HOST_OVERRIDE=seat.spectra.local npm run dev amagSeat2019", "dev:amagskoda": "HOST_OVERRIDE=skoda2020.spectra.local npm run dev amagSkoda2020", "dev:amagstopgo": "HOST_OVERRIDE=stopgo.spectra.local npm run dev amagStopGo", "dev:amagvw": "HOST_OVERRIDE=volkswagen2019.spectra.local npm run dev amagVW2019", "dev:amagvwn": "HOST_OVERRIDE=volkswagen-nutzfahrzeuge2019.spectra.local npm run dev amagVW2019", "dev:dealerSites": "npm run dev dealerSites", "dev:dealestate": "HOST_OVERRIDE=dealestate.spectra.local npm run dev dealEstate", "dev:kartause": "HOST_OVERRIDE=kartause.spectra.local npm run dev kartause", "dev:langenthal": "HOST_OVERRIDE=stadttheater-langenthal.spectra.local npm run dev stadttheaterLangenthal", "dev:mobileid": "HOST_OVERRIDE=mobileid19.spectra.local npm run dev mobileid19", "dev:mobimo": "HOST_OVERRIDE=mobimo.spectra.local npm run dev mobimo", "lint": "npm run lint:css && npm run lint:js && npm run lint:flow", "lint:js": "eslint src tools --cache", "lint:fix": "npm run lint:js -- --fix", "lint:css": "stylelint src/**/*.css", "lint:flow": "eslint src --ignore-path .eslintignore-flow --cache --cache-location .eslintcache-flow", "lint:all": "npm run lint:fix && npm run lint:css", "flow": "echo flow disabled", "flow:disabled": "flow --show-all-errors", "flow:coverage": "flow-coverage-report -i 'src/**/*.js' -t html -t json -t text", "test": "cross-env TEST_MODE=true jest", "test:watch": "npm test -- --watch", "test:coverage": "npm test -- --coverage", "test:debug": "node --debug-brk ./node_modules/.bin/jest --runInBand", "test:system": "cross-env IS_NODE=true env-cmd ./env/.env_test jest --config jest-config-system-test.json", "qa": "npm run lint && npm run test:coverage && npm run test:system", "grape:exit": "node ./tools/runGrape", "ci:test": "npm run test:coverage -- --runInBand", "ci:test:system": "npm run grape:exit && npm run test:system -- --runInBand", "ci:junit:html": "mkdirp test-results && mkdirp ../test-results junit-viewer --results=test-results --save=test-results/index.html && junit-viewer --results=../test-results --save=../test-results/index.html", "clean": "rm -rf build && rm -rf dist", "build": "npm run clean && cross-env NODE_ENV=production babel-node ./tools/scripts/build.js", "start": "cross-env NODE_ENV=production node build/server", "dist": "rm -rf dist && mkdirp dist/mango && ncp build dist/mango/build && ncp package.json dist/mango/package.json", "deps:check": "npm-check", "deps:update": "npm-check -u --save-exact", "flow:defs": "flow-typed install -f 0.52 --overwrite", "cypress": "cypress open", "sonarqube": "npm i && babel-node tools/operations"}, "author": "Deep Impact AG", "homepage": "http://www.deep-impact.ch", "dependencies": {"@reduxjs/toolkit": "^2.2.6", "accepts": "1.3.3", "accounting": "0.4.1", "activecampaign": "1.2.5", "apollo-cache-inmemory": "1.6.6", "apollo-client": "2.2.6", "apollo-link-http": "1.5.3", "app-root-path": "2.0.1", "auth0-js": "9.11.3", "auth0-lock": "11.17.2", "babel-polyfill": "6.22.0", "babel-template": "6.16.0", "basic-auth": "1.1.0", "body-parser": "1.16.1", "btoa": "1.1.2", "classnames": "2.2.6", "cloudinary": "1.41.2", "cloudinary-core": "2.13.0", "compression": "1.6.2", "cookie-parser": "1.4.3", "date-fns": "2", "deep-equal": "1.0.1", "di-react-number-input": "15.6.1", "dompurify": "3", "dotenv": "2.0.0", "downloadjs": "1.4.7", "downshift": "3.1.13", "draft-js": "0.10.5", "draft-js-focus-plugin": "2.0.1", "draft-js-mention-plugin": "^3.1.5", "draft-js-plugins-editor": "2.0.4", "express": "4.19.2", "express-http-proxy": "1.6.3", "express-prometheus-middleware": "1", "font-awesome": "4.7.0", "formik": "1.5.2", "get-video-id": "^3.1.1", "google-libphonenumber": "^3.2.27", "google-map-react": "1.1.2", "graphql": "0.13.1", "graphql-tag": "2.8.0", "helmet": "3.12.1", "history": "4.7.2", "hoist-non-react-statics": "3.2.1", "hpp": "0.2.1", "iframe-resizer": "3.6.6", "immutable": "3.8.2", "intersection-observer": "0.5.0", "isomorphic-fetch": "2.2.1", "isomorphic-style-loader": "3.0.0", "js-search": "1.4.2", "jsdom": "11.6.2", "json2csv": "3.9.1", "jwt-decode": "2.1.0", "libphonenumber-js": "^1.10.53", "lodash": "4.17.4", "logrocket": "2.2.0", "mandrill-api": "1.0.45", "moment": "2.22.1", "moment-timezone": "0.5.15", "morgan": "^1.10.0", "murmurhash": "0.0.2", "node-cache": "5.1.2", "node-polyglot": "2.5.0", "normalize.css": "5.0.0", "numbro": "1.11.0", "object-fit-images": "3.2.3", "path": "0.12.7", "path-to-regexp": "1.7.0", "postcss-for": "2.1.1", "postcss-object-fit-images": "1.1.2", "prom-client": "^13.1.0", "prop-types": "15.6.1", "pusher": "1.5.1", "pusher-js": "4.1.0", "rc-progress": "^2.2.5", "react": "16.9.0", "react-apollo": "2.0.4", "react-autosuggest": "9.4.1", "react-color": "^2.14.1", "react-cookie": "3", "react-datepicker": "1.6.0", "react-dd-menu": "2.0.2", "react-dnd": "10", "react-dnd-html5-backend": "10", "react-dom": "16.8.6", "react-dropzone": "5.1.0", "react-helmet-async": "1.3.0", "react-highlight-words": "0.17.0", "react-image-crop": "5.0.0", "react-intersection-observer": "6.2.3", "react-medium-image-zoom": "^4.4.3", "react-modal": "3.6.1", "react-onclickoutside": "6.7.1", "react-redux": "7.2.9", "react-router": "4.3.1", "react-router-dom": "4.3.1", "react-scroll": "1.7.10", "react-select": "1", "react-share": "1.16.0", "react-sizeme": "2.5.2", "react-slick": "0.23.2", "react-social": "1.10.0", "react-ssr-prepass": "^1.5.0", "react-transition-group": "4.2.1", "read-excel-file": "^4.0.5", "redraft": "0.10.1", "redux": "4.0.0", "redux-form": "8", "redux-immutable": "4.0.0", "redux-observable": "1.0.0", "request": "2.79.0", "reselect": "4.0.0", "rxjs": "6.2.1", "rxjs-compat": "6.2.1", "serialize-javascript": "1.3.0", "slick-carousel": "1.6.0", "slug": "0.9.1", "smoothscroll-polyfill": "0.4.3", "source-map-support": "0.4.6", "transit-immutable-js": "0.7.0", "transit-js": "0.8.846", "ua-parser-js": "0.7.17", "universal-cookie": "2.0.8", "universal-cookie-express": "2.0.6", "user-home": "2.0.0", "uuid": "3.0.1", "xlsx": "0.18.5", "xml2js": "0.6.2"}, "devDependencies": {"assets-webpack-plugin": "3.5.1", "aws-sdk": "2.7.26", "babel-cli": "6.26.0", "babel-core": "6.26.3", "babel-eslint": "7.1.1", "babel-jest": "23.4.2", "babel-loader": "6.2.10", "babel-plugin-transform-builtin-extend": "1.1.2", "babel-plugin-transform-class-properties": "6.24.1", "babel-plugin-transform-es2015-destructuring": "6.23.0", "babel-plugin-transform-object-rest-spread": "6.26.0", "babel-preset-latest": "6.24.1", "babel-preset-react": "6.24.1", "case-sensitive-paths-webpack-plugin": "2.1.1", "chokidar": "3.5.3", "copy-webpack-plugin": "4.5.1", "cross-env": "3.1.4", "css-loader": "0.26.1", "cypress": "^2.1.0", "env-cmd": "4.0.0", "enzyme": "3.4.1", "enzyme-adapter-react-16": "1.5.0", "eslint": "3.12.0", "eslint-config-airbnb": "13.0.0", "eslint-plugin-flowtype": "2.30.4", "eslint-plugin-import": "2.2.0", "eslint-plugin-jsx-a11y": "2.2.3", "eslint-plugin-react": "6.8.0", "extract-text-webpack-plugin": "2.0.0-beta.4", "fetch-mock": "5.5.0", "file-loader": "0.10.1", "flow-bin": "0.52.0", "flow-coverage-report": "0.3.0", "flow-typed": "2.5.1", "happypack": "3.0.2", "jasmine-reporters": "2.2.1", "jest": "23.5.0", "json-loader": "0.5.4", "junit-viewer": "4.11.1", "md5": "2.2.1", "mkdirp": "0.5.1", "ncp": "2.0.0", "node-notifier": "10.0.1", "npm-check": "6.0.1", "postcss": "6.0.0", "postcss-cssnext": "2.9.0", "postcss-import": "9.0.0", "postcss-load-config": "1.0.0", "postcss-loader": "1.2.2", "postcss-theme": "1.1.1", "postcss-url": "5.1.2", "react-hot-loader": "3.0.0-beta.6", "redux-mock-store": "1.5.3", "regenerator-runtime": "0.10.1", "sinon": "1.17.7", "style-loader": "0.13.1", "stylelint": "8.0.0", "url-loader": "0.5.9", "webpack": "2.2.0", "webpack-bundle-analyzer": "2.2.1", "webpack-dev-middleware": "1.8.4", "webpack-hot-middleware": "2.13.2", "webpack-md5-hash": "0.0.5", "webpack-node-externals": "1.5.4", "workbox-webpack-plugin": "3.2.0", "worker-farm": "1.3.1"}, "jest": {"coverageThreshold": {"global": {"branches": 30, "functions": 30, "lines": 30, "statements": 30}}, "moduleNameMapper": {"^.+\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga|css|less|scss)$": "<rootDir>/tools/test/styleMock.js"}, "setupTestFrameworkScriptFile": "./tools/scripts/setup-test-env.js", "testPathIgnorePatterns": ["<rootDir>/node_modules/", "<rootDir>/build/", "<rootDir>/dist/", "<rootDir>/coverage/", ".system.test.js$"]}, "resolutions": {"chokidar": "3.5.3"}}