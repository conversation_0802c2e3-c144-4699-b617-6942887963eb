name: Spectra mango tests

on:
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review]
    paths:
      - "mango/**"

concurrency:
  group: ci-fe-tests-${{ github.ref }}-1
  cancel-in-progress: true

jobs:
  pre_job:
    continue-on-error: true
    runs-on:
      labels: ubuntu-latest
    if: ${{ github.event.pull_request.draft == false }}
    outputs:
      should_skip: ${{ steps.skip_check.outputs.should_skip }}
    steps:
      - id: skip_check
        uses: fkirc/skip-duplicate-actions@master
  frontend_tests:
    needs: pre_job
    if: ${{ needs.pre_job.outputs.should_skip != 'true' && github.event.pull_request.draft == false }}
    runs-on:
      labels: ubuntu-latest
    # https://docs.github.com/en/actions/reference/workflow-syntax-for-github-actions#jobsjob_idtimeout-minutes
    timeout-minutes: 15
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Prepare java
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: '11'

      - name: Install clojure tools
        uses: DeLaGuardo/setup-clojure@12.5
        with:
          lein: 2.9.8           # Leiningen
      
      - name: Cache Node modules
        id: node_cache
        uses: actions/cache@v4
        with:
          path: |
            mango/node_modules
            mango/.happypack
          key: node-${{ hashFiles('mango/package-lock.json') }}
          restore-keys: |
            node-${{ hashFiles('mango/package-lock.json') }}
            node
          save-always: true

      - name: Cache Lint modules
        id: lint_cache
        uses: actions/cache@v4
        with:
          path: |
            mango/.eslintcache
          key: mango-lint-1-${GITHUB_REF##*/}
          restore-keys: |
            mango-lint-1-${GITHUB_REF##*/}
            mango-lint-1
          save-always: true
      
      - name: Install dependencies
        working-directory: mango
        run: cp env/.env_test .env & npm install

      - name: Run linter
        working-directory: mango
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_REGION: "eu-west-1"
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          SPECTRA_UNIVERSE: universe-test
          SPECTRA_ENV: dev
        run: npm run lint
      
      - name: Run flow
        working-directory: mango
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_REGION: "eu-west-1"
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          SPECTRA_UNIVERSE: universe-test
          SPECTRA_ENV: dev
        run: npm run flow

      - name: Run CI Test
        working-directory: mango
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_REGION: "eu-west-1"
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          SPECTRA_UNIVERSE: universe-test
          SPECTRA_ENV: dev
        run: npm run ci:test